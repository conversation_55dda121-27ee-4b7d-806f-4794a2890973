import React from "react";

interface LogoProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  showText?: boolean;
}

export default function Logo({ className = "", size = "md", showText = true }: LogoProps) {
  const sizes = {
    sm: { icon: "h-6 w-6", text: "text-lg" },
    md: { icon: "h-8 w-8", text: "text-xl" },
    lg: { icon: "h-12 w-12", text: "text-3xl" },
  };

  const { icon, text } = sizes[size];

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Logo Icon - Modern geometric design */}
      <svg
        className={icon}
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Background Circle */}
        <circle cx="20" cy="20" r="20" fill="url(#gradient)" />

        {/* Document/Resume Icon */}
        <path
          d="M12 10C12 9.44772 12.4477 9 13 9H23L29 15V30C29 30.5523 28.5523 31 28 31H13C12.4477 31 12 30.5523 12 30V10Z"
          fill="white"
          fillOpacity="0.95"
        />

        {/* Document Lines */}
        <rect x="15" y="14" width="10" height="1.5" rx="0.75" fill="url(#gradient)" opacity="0.7" />
        <rect x="15" y="18" width="10" height="1.5" rx="0.75" fill="url(#gradient)" opacity="0.7" />
        <rect x="15" y="22" width="7" height="1.5" rx="0.75" fill="url(#gradient)" opacity="0.7" />

        {/* Sparkle/AI Element */}
        <circle cx="25" cy="25" r="3" fill="#FFA500" />
        <path
          d="M25 23.5L25.5 24.5L26.5 25L25.5 25.5L25 26.5L24.5 25.5L23.5 25L24.5 24.5L25 23.5Z"
          fill="white"
        />

        {/* Gradient Definitions */}
        <defs>
          <linearGradient id="gradient" x1="0" y1="0" x2="40" y2="40" gradientUnits="userSpaceOnUse">
            <stop offset="0%" stopColor="#3B82F6" />
            <stop offset="100%" stopColor="#1E40AF" />
          </linearGradient>
        </defs>
      </svg>

      {/* Logo Text */}
      {showText && (
        <span className={`font-bold ${text} bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent`}>
          Try2Work
        </span>
      )}
    </div>
  );
}
