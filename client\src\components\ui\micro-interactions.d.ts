import "./micro-interactions.css";
interface HoverEffectProps {
    children: React.ReactNode;
    className?: string;
    scale?: number;
    rotation?: number;
    borderRadius?: string;
    shadow?: boolean;
}
export declare function HoverEffect({ children, className, scale, rotation, borderRadius, shadow, }: HoverEffectProps): import("react").JSX.Element;
interface TiltEffectProps {
    children: React.ReactNode;
    className?: string;
    maxTilt?: number;
    scale?: number;
    glare?: boolean;
    glareColor?: string;
    glareMaxOpacity?: number;
}
export declare function TiltEffect({ children, className, maxTilt, scale, glare, glareColor, glareMaxOpacity, }: TiltEffectProps): import("react").JSX.Element;
interface MagneticEffectProps {
    children: React.ReactNode;
    className?: string;
    strength?: number;
}
export declare function MagneticEffect({ children, className, strength, }: MagneticEffectProps): import("react").JSX.Element;
interface RippleEffectProps {
    children: React.ReactNode;
    className?: string;
    color?: string;
    duration?: number;
}
export declare function RippleEffect({ children, className, color, duration, }: RippleEffectProps): import("react").JSX.Element;
interface TypewriterEffectProps {
    text: string;
    className?: string;
    speed?: number;
    delay?: number;
    cursor?: boolean;
    cursorChar?: string;
    onComplete?: () => void;
}
export declare function TypewriterEffect({ text, className, speed, delay, cursor, cursorChar, onComplete, }: TypewriterEffectProps): import("react").JSX.Element;
interface FloatingElementProps {
    children: React.ReactNode;
    className?: string;
    duration?: number;
    distance?: number;
    delay?: number;
}
export declare function FloatingElement({ children, className, duration, distance, delay, }: FloatingElementProps): import("react").JSX.Element;
interface PulseEffectProps {
    children: React.ReactNode;
    className?: string;
    intensity?: number;
    duration?: number;
}
export declare function PulseEffect({ children, className, intensity, duration, }: PulseEffectProps): import("react").JSX.Element;
interface StaggeredAnimationProps {
    children: React.ReactNode;
    className?: string;
    stagger?: number;
    duration?: number;
    delay?: number;
    direction?: "up" | "down" | "left" | "right" | "fade" | "scale";
}
export declare function StaggeredAnimation({ children, className, stagger, duration, delay, direction, }: StaggeredAnimationProps): import("react").JSX.Element;
export {};
//# sourceMappingURL=micro-interactions.d.ts.map