/**
 * Simple test script for the payment flow
 */

import fetch from 'node-fetch';

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:5000';

async function testServerConnection() {
  console.log('Testing server connection...');
  
  try {
    const response = await fetch(`${API_URL}/api/health`);
    if (response.ok) {
      console.log('✅ Server is running');
      return true;
    } else {
      console.log('❌ Server returned an error:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Could not connect to server:', error.message);
    return false;
  }
}

async function runSimpleTest() {
  console.log('🧪 Starting simple payment flow test...\n');
  
  const serverRunning = await testServerConnection();
  if (!serverRunning) {
    console.log('\n❌ Server is not running. Please start the server first.');
    process.exit(1);
  }
  
  console.log('\n✅ Basic test completed. The server is running and ready for payment testing.');
  console.log('\nTo test the complete payment flow:');
  console.log('1. Start the server: npm run dev');
  console.log('2. Run the full test: node scripts/test-payment-flow.js');
  console.log('3. Or manually test through the web interface');
}

// Run the test
runSimpleTest().catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});