/**
 * Lightweight post-processing helpers for resume content.
 * These are intentionally deterministic, fast, and safe (no external AI calls).
 * They improve readability, enforce bullet formatting, and trim/shorten text.
 */

function splitIntoSentences(text: string): string[] {
  return text
    .split(/(?<=[.?!])\s+/)
    .map((s: string) => s.trim())
    .filter(Boolean);
}

function ensureStrongVerb(line: string): string {
  // remove leading hyphen and whitespace
  let t = line.replace(/^-+\s*/, "").trim();

  // if line already starts with a verb-like token (common verbs list), keep it
  const verbs = [
    "led", "managed", "implemented", "created", "developed", "improved", "increased", "reduced",
    "optimized", "designed", "built", "launched", "coordinated", "executed", "owned", "delivered",
    "streamlined", "automated", "resolved", "spearheaded"
  ];
  const firstWord = (t.split(/\s+/)[0] || "").replace(/[^a-zA-Z]/g, "").toLowerCase();

  if (verbs.includes(firstWord)) {
    return `- ${t}`;
  }

  // remove leading pronouns or weak phrases
  t = t.replace(/^(responsible\s+for|responsibly\s+|was\s+responsible\s+for|worked\s+on|worked\s+with|helped\s+to|helped\s+|participated\s+in)\b[:\s]*/i, "");

  // if there's a clear numeric metric already, keep it and prefix with a verb
  if (/\d+%|\$\d+|[0-9]{1,3}(?= employees| customers| users| clients)/i.test(t)) {
    return `- Improved ${t}`;
  }

  // default: prefix with 'Led' to make it action-oriented
  return `- Led ${t.charAt(0).toLowerCase() === t.charAt(0) ? t : t}`;
}

export function postProcessExperienceBullets(bullets: string[]): string[] {
  // normalize and produce concise bullets
  const processed = bullets
    .map((b: string) => b.trim())
    .filter(Boolean)
    .map((b: string) => {
      // strip existing leading hyphen(s)
      const raw = b.replace(/^-+\s*/, "").trim();

      // split long sentences into smaller ones and take up to 2 parts
      const parts = splitIntoSentences(raw);
      const candidate = parts.length > 1 ? `${parts[0]}${parts[1] ? " " + parts[1] : ""}` : raw;

      // enforce max length (truncate gracefully)
      const maxLen = 140;
      const clipped = candidate.length > maxLen ? candidate.slice(0, maxLen - 1).trim() + "…" : candidate;

      return ensureStrongVerb(clipped);
    });

  // dedupe near-duplicates
  const seen = new Set<string>();
  return processed.filter((p: string) => {
    if (seen.has(p)) return false;
    seen.add(p);
    return true;
  });
}

export function postProcessSummary(summary: string): string {
  if (!summary || typeof summary !== "string") return summary;
  // prefer to keep 2-3 short sentences
  const sentences = splitIntoSentences(summary);
  const take = sentences.slice(0, 3).join(" ");
  const maxLen = 320;
  const trimmed = take.length > maxLen ? take.slice(0, maxLen - 1).trim() + "…" : take;
  // ensure first person: if it starts with "<Name> is" convert to "I am"
  const nameIsMatch = trimmed.match(/^[A-Z][a-z]+(\s[A-Z][a-z]+)?\s+(is|was|has|brings)/);
  if (nameIsMatch) {
    return trimmed.replace(/^[A-Z][^ ]*(\s[A-Z][^ ]*)?\s+(is|was|has|brings)/, "I am");
  }
  return trimmed;
}

export function normalizeDraft(draft: any): any {
  if (!draft) return draft;

  const out = { ...draft };

  if (Array.isArray(out.workExperience)) {
    out.workExperience = out.workExperience.map((exp: any) => {
      const description = typeof exp.description === "string" ? exp.description : (exp.description ?? "");
      // convert description to bullets if it's paragraph-like
      const sentences = splitIntoSentences(description);
      let bullets: string[] = [];
      if (sentences.length > 1) {
        bullets = sentences.slice(0, 4).map((s) => `- ${s}`);
      } else if (description.includes("\n")) {
        bullets = description.split(/\r?\n/).map((s: string) => `- ${s.trim()}`).filter(Boolean);
      } else if (description.trim().length > 0) {
        bullets = [`- ${description.trim()}`];
      }

      if (bullets.length === 0 && Array.isArray(exp.achievements)) {
        bullets = exp.achievements.map((a: string) => `- ${a}`);
      }

      bullets = postProcessExperienceBullets(bullets);

      return {
        ...exp,
        // keep description as a newline-joined string of bullets to match existing app expectations
        description: bullets.join("\n"),
      };
    });
  }

  if (typeof out.professionalSummary === "string") {
    out.professionalSummary = postProcessSummary(out.professionalSummary);
  }

  return out;
}
