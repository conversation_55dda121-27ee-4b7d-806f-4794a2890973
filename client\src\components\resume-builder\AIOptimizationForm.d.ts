import type { Resume } from "@shared/schema";
interface AIOptimizationFormProps {
    data: Partial<Resume>;
    onDataChange: (data: Partial<Resume>) => void;
    onNext: () => void;
    onPrevious: () => void;
    resumeId?: string;
}
export default function AIOptimizationForm({ data, onDataChange, onNext, onPrevious, resumeId, }: AIOptimizationFormProps): import("react").JSX.Element;
export {};
//# sourceMappingURL=AIOptimizationForm.d.ts.map