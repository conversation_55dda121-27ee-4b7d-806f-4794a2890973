// JavaScript for Job Application Tracker

document.addEventListener('DOMContentLoaded', () => {
    // Initialize all functionality
    initNavigation();
    initAnimations();
    initDashboardDemo();
    initFormHandling();
    initNotifications();
    initModals();
    initTabs();
});

// Navigation functionality
function initNavigation() {
    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Mobile menu toggle (if needed)
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
    }
}

// Animation on scroll
function initAnimations() {
    // Add animation classes to feature cards
    const featureCards = document.querySelectorAll('.bg-gray-50.rounded-xl');
    featureCards.forEach((card, index) => {
        card.classList.add('feature-card');
    });

    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.feature-card, .stat-animation');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Dashboard demo functionality
function initDashboardDemo() {
    // Animate stats on page load
    const statsElements = document.querySelectorAll('.text-2xl.font-bold');
    statsElements.forEach(el => {
        const finalValue = parseInt(el.textContent);
        let currentValue = 0;
        const increment = Math.ceil(finalValue / 20);
        
        const updateCounter = () => {
            if (currentValue < finalValue) {
                currentValue += increment;
                if (currentValue > finalValue) currentValue = finalValue;
                el.textContent = currentValue;
                requestAnimationFrame(updateCounter);
            }
        };
        
        // Start counter when element is in viewport
        const statsObserver = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting) {
                updateCounter();
                statsObserver.unobserve(el);
            }
        });
        
        statsObserver.observe(el);
    });

    // Add hover effect to application cards
    const applicationCards = document.querySelectorAll('.bg-white.rounded-lg.p-3');
    applicationCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateX(5px)';
            card.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateX(0)';
            card.style.boxShadow = 'none';
        });
    });
}

// Form handling
function initFormHandling() {
    // Handle sign up form
    const signUpForms = document.querySelectorAll('form');
    signUpForms.forEach(form => {
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            
            // Show success notification
            showNotification('Success!', 'Your account has been created.', 'success');
            
            // Reset form
            form.reset();
        });
    });

    // Add input validation
    const inputs = document.querySelectorAll('input[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', () => validateInput(input));
        input.addEventListener('input', () => {
            if (input.classList.contains('border-red-500')) {
                validateInput(input);
            }
        });
    });
}

function validateInput(input) {
    const isValid = input.checkValidity();
    
    if (isValid) {
        input.classList.remove('border-red-500');
        input.classList.add('border-green-500');
    } else {
        input.classList.remove('border-green-500');
        input.classList.add('border-red-500');
    }
    
    return isValid;
}

// Notification system
function initNotifications() {
    // Create notification container if it doesn't exist
    if (!document.getElementById('notification-container')) {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.style.position = 'fixed';
        container.style.top = '20px';
        container.style.right = '20px';
        container.style.zIndex = '1000';
        document.body.appendChild(container);
    }
}

function showNotification(title, message, type = 'info') {
    const container = document.getElementById('notification-container');
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                ${getNotificationIcon(type)}
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium">${title}</h3>
                <div class="text-sm">${message}</div>
            </div>
            <div class="ml-auto pl-3">
                <button class="inline-flex text-gray-400 focus:outline-none" onclick="closeNotification(this)">
                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;
    
    // Add to container
    container.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Auto hide after 5 seconds
    setTimeout(() => {
        closeNotification(notification.querySelector('button'));
    }, 5000);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success':
            return `<svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>`;
        case 'error':
            return `<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>`;
        default:
            return `<svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>`;
    }
}

function closeNotification(button) {
    const notification = button.closest('.notification');
    notification.classList.remove('show');
    
    setTimeout(() => {
        notification.remove();
    }, 300);
}

// Modal functionality
function initModals() {
    // Handle modal triggers
    const modalTriggers = document.querySelectorAll('[data-modal-target]');
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', () => {
            const targetId = trigger.getAttribute('data-modal-target');
            openModal(targetId);
        });
    });

    // Handle modal close buttons
    const closeButtons = document.querySelectorAll('[data-modal-close]');
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            const modal = button.closest('.modal');
            closeModal(modal);
        });
    });

    // Close modal when clicking outside
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal(modal);
            }
        });
    });

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const activeModal = document.querySelector('.modal.active');
            if (activeModal) {
                closeModal(activeModal);
            }
        }
    });
}

function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modal) {
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
}

// Tab functionality
function initTabs() {
    // Handle tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');
            
            // Deactivate all tabs
            tabButtons.forEach(btn => btn.classList.remove('active'));
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Activate selected tab
            button.classList.add('active');
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });
}

// Utility function to fetch data (for future API integration)
async function fetchData(url, options = {}) {
    try {
        const response = await fetch(url, options);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error fetching data:', error);
        showNotification('Error', 'Failed to fetch data. Please try again.', 'error');
        return null;
    }
}

// Demo function to add a new application
function addApplication(jobTitle, company, status) {
    const applicationsList = document.querySelector('.space-y-3');
    if (!applicationsList) return;
    
    const newApplication = document.createElement('div');
    newApplication.className = 'bg-white rounded-lg p-3 flex items-center justify-between';
    
    // Generate a random color for the company avatar
    const colors = ['bg-indigo-100', 'bg-blue-100', 'bg-purple-100', 'bg-green-100', 'bg-yellow-100'];
    const textColors = ['text-indigo-600', 'text-blue-600', 'text-purple-600', 'text-green-600', 'text-yellow-600'];
    const randomColor = Math.floor(Math.random() * colors.length);
    
    // Determine status badge color
    let statusColor = 'bg-blue-100 text-blue-800';
    if (status === 'Interview') statusColor = 'bg-green-100 text-green-800';
    if (status === 'Applied') statusColor = 'bg-yellow-100 text-yellow-800';
    if (status === 'Review') statusColor = 'bg-purple-100 text-purple-800';
    
    newApplication.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 ${colors[randomColor]} rounded-lg flex items-center justify-center">
                <span class="${textColors[randomColor]} font-semibold">${company.charAt(0)}</span>
            </div>
            <div>
                <div class="font-medium text-gray-900">${jobTitle}</div>
                <div class="text-sm text-gray-500">${company}</div>
            </div>
        </div>
        <span class="${statusColor} text-xs px-2 py-1 rounded">${status}</span>
    `;
    
    // Add animation
    newApplication.style.opacity = '0';
    newApplication.style.transform = 'translateY(-10px)';
    
    applicationsList.insertBefore(newApplication, applicationsList.firstChild);
    
    // Animate in
    setTimeout(() => {
        newApplication.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        newApplication.style.opacity = '1';
        newApplication.style.transform = 'translateY(0)';
    }, 10);
    
    // Show notification
    showNotification('Success!', `Application to ${company} has been added.`, 'success');
}