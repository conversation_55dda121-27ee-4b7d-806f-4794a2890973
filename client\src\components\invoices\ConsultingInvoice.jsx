import React from "react";
export default function ConsultingInvoice({ data }) {
    const subtotal = data.lineItems.reduce((sum, item) => sum + item.thisInvoice, 0);
    const gstAmount = subtotal * data.gstRate;
    const totalNowDue = subtotal + gstAmount;
    return (<div className="w-full max-w-[8.5in] mx-auto bg-white p-12 print:p-8">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">{data.companyName}</h1>
        </div>
        {data.companyLogoUrl && (<div className="w-32 h-32 bg-green-500 flex items-center justify-center">
            <img src={data.companyLogoUrl} alt={`${data.companyName} Logo`} className="w-full h-full object-contain"/>
          </div>)}
      </div>

      {/* Invoice Details */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        {/* Left - Client Info */}
        <div>
          <p className="font-bold text-lg mb-2">TO</p>
          <p className="font-semibold">{data.clientName}</p>
          {data.clientAddress.map((line, index) => (<p key={index} className="text-sm">{line}</p>))}
          {data.attention && (<p className="text-sm mt-2">
              <span className="font-semibold">Attn:</span> {data.attention}
            </p>)}
        </div>

        {/* Right - Invoice Info */}
        <div className="text-right">
          <p className="font-bold text-2xl mb-4">TAX INVOICE {data.invoiceNumber}</p>
          <div className="space-y-1 text-sm">
            <p><span className="font-semibold">Date:</span> {data.invoiceDate}</p>
            <p><span className="font-semibold">GST:</span> {data.gst}</p>
            <p className="mt-4"><span className="font-semibold">Job No:</span> {data.jobNo}</p>
            <p><span className="font-semibold">Order Number:</span> {data.orderNumber}</p>
            <p><span className="font-semibold">Amount:</span> ${data.totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}</p>
          </div>
        </div>
      </div>

      {/* Project Reference */}
      <div className="mb-6">
        <p className="text-sm">
          <span className="font-semibold">Re:</span> {data.projectReference}
        </p>
        <p className="text-sm text-right">
          <span className="font-semibold">Progress Claim No:</span> {data.progressClaimNo}
        </p>
      </div>

      {/* Line Items Table */}
      <div className="border-t-2 border-b-2 border-black mb-8">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-300">
              <th className="text-left py-3 px-2 font-semibold text-sm"></th>
              <th className="text-right py-3 px-2 font-semibold text-sm w-24">Fee Value</th>
              <th className="text-right py-3 px-2 font-semibold text-sm w-24">% Done</th>
              <th className="text-right py-3 px-2 font-semibold text-sm w-28">Fee to Date</th>
              <th className="text-right py-3 px-2 font-semibold text-sm w-28">Prev<br />Invoice</th>
              <th className="text-right py-3 px-2 font-semibold text-sm w-28">This Invoice</th>
            </tr>
          </thead>
          <tbody>
            {data.lineItems.map((item, index) => (<tr key={index} className="border-b border-gray-200">
                <td className="py-3 px-2 text-sm">{item.description}</td>
                <td className="py-3 px-2 text-right text-sm">
                  {item.feeValue.toLocaleString('en-US', { minimumFractionDigits: 0 })}
                </td>
                <td className="py-3 px-2 text-right text-sm">
                  {item.percentDone}%
                </td>
                <td className="py-3 px-2 text-right text-sm">
                  {item.feeToDate.toLocaleString('en-US', { minimumFractionDigits: 0 })}
                </td>
                <td className="py-3 px-2 text-right text-sm">
                  {item.prevInvoice.toLocaleString('en-US', { minimumFractionDigits: 0 })}
                </td>
                <td className="py-3 px-2 text-right text-sm font-semibold">
                  {item.thisInvoice.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </td>
              </tr>))}
            <tr className="font-bold border-t-2 border-black">
              <td className="py-3 px-2" colSpan={3}>Total Due</td>
              <td className="py-3 px-2 text-right">
                {data.lineItems.reduce((sum, item) => sum + item.feeToDate, 0).toLocaleString('en-US', { minimumFractionDigits: 0 })}
              </td>
              <td className="py-3 px-2 text-right">
                {data.lineItems.reduce((sum, item) => sum + item.prevInvoice, 0).toLocaleString('en-US', { minimumFractionDigits: 0 })}
              </td>
              <td className="py-3 px-2 text-right">
                {subtotal.toLocaleString('en-US', { minimumFractionDigits: 2 })}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-end mb-12">
        <div className="w-96">
          <div className="flex justify-between py-3 border-b border-gray-300">
            <span className="font-semibold">Subtotal:</span>
            <span className="text-right">{subtotal.toLocaleString('en-US', { minimumFractionDigits: 2 })}</span>
          </div>
          <div className="flex justify-between py-3 border-b border-gray-300">
            <span className="font-semibold">GST:</span>
            <span className="text-right">{gstAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}</span>
          </div>
          <div className="flex justify-between py-4 border-t-2 border-black">
            <span className="font-bold text-lg">Total Now Due:</span>
            <span className="font-bold text-lg">{totalNowDue.toLocaleString('en-US', { minimumFractionDigits: 2 })}</span>
          </div>
        </div>
      </div>
    </div>);
}
