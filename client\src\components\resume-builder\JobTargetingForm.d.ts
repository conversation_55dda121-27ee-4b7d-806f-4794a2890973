import type { Resume } from "@shared/schema";
interface JobTargetingFormProps {
    data: Partial<Resume>;
    onDataChange: (data: Partial<Resume>) => void;
    onNext: () => void;
    onPrevious: () => void;
}
export default function JobTargetingForm({ data, onDataChange, onNext, onPrevious, }: JobTargetingFormProps): import("react").JSX.Element;
export {};
//# sourceMappingURL=JobTargetingForm.d.ts.map