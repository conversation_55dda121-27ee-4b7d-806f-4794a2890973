<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ResumeAI - Dashboard</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f8fafc;
      color: #1e293b;
      line-height: 1.6;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    header {
      background-color: #fff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      position: sticky;
      top: 0;
      z-index: 100;
    }
    
    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 0;
    }
    
    .logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 1.5rem;
      font-weight: bold;
      color: #3b82f6;
    }
    
    .logo-icon {
      width: 2rem;
      height: 2rem;
      background-color: #3b82f6;
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }
    
    .user-menu {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .user-avatar {
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
      background-color: #e2e8f0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #64748b;
    }
    
    .btn {
      display: inline-block;
      background-color: #3b82f6;
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 0.25rem;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.3s;
      border: none;
      cursor: pointer;
    }
    
    .btn:hover {
      background-color: #2563eb;
    }
    
    .btn-secondary {
      background-color: #e2e8f0;
      color: #475569;
    }
    
    .btn-secondary:hover {
      background-color: #cbd5e1;
    }
    
    main {
      padding: 2rem 0;
    }
    
    .dashboard-header {
      margin-bottom: 2rem;
    }
    
    .dashboard-header h1 {
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }
    
    .dashboard-header p {
      color: #64748b;
    }
    
    .dashboard-actions {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }
    
    .action-card {
      background-color: white;
      padding: 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s, box-shadow 0.3s;
    }
    
    .action-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .action-card h3 {
      margin-bottom: 0.5rem;
      font-size: 1.25rem;
    }
    
    .action-card p {
      color: #64748b;
      margin-bottom: 1rem;
    }
    
    .resumes-section {
      background-color: white;
      padding: 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .resumes-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
    }
    
    .resumes-header h2 {
      font-size: 1.5rem;
    }
    
    .resumes-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 1rem;
    }
    
    .resume-card {
      background-color: #f8fafc;
      padding: 1rem;
      border-radius: 0.25rem;
      border: 1px solid #e2e8f0;
    }
    
    .resume-card h3 {
      margin-bottom: 0.5rem;
    }
    
    .resume-card p {
      color: #64748b;
      font-size: 0.875rem;
      margin-bottom: 1rem;
    }
    
    .resume-actions {
      display: flex;
      gap: 0.5rem;
    }
    
    .empty-state {
      text-align: center;
      padding: 3rem 0;
      color: #64748b;
    }
    
    .empty-state h3 {
      margin-bottom: 0.5rem;
      font-size: 1.25rem;
    }
    
    footer {
      background-color: #1e293b;
      color: white;
      padding: 2rem 0;
      text-align: center;
      margin-top: 4rem;
    }
    
    .status-message {
      background-color: #fef3c7;
      border: 1px solid #f59e0b;
      color: #92400e;
      padding: 1rem;
      border-radius: 0.5rem;
      margin-bottom: 2rem;
      text-align: center;
    }
  </style>
</head>
<body>
  <header>
    <div class="container">
      <nav>
        <div class="logo">
          <div class="logo-icon">📄</div>
          ResumeAI
        </div>
        <div class="user-menu">
          <div class="user-avatar">DU</div>
          <span>Dev User</span>
          <a href="/api/logout" class="btn btn-secondary">Logout</a>
        </div>
      </nav>
    </div>
  </header>

  <main>
    <div class="container">
      <div class="status-message">
        <strong>Development Dashboard</strong> - This is a simplified version of the application for testing purposes.
      </div>
      
      <section class="dashboard-header">
        <h1>Welcome back, Dev User!</h1>
        <p>Create and manage your professional resumes with AI assistance.</p>
      </section>

      <section class="dashboard-actions">
        <div class="action-card">
          <h3>📝 Create New Resume</h3>
          <p>Start from scratch or use AI to generate a professional resume.</p>
          <button class="btn" onclick="createResume()">Create Resume</button>
        </div>

        <div class="action-card">
          <h3>📄 Upload Existing Resume</h3>
          <p>Upload a PDF or DOCX file to enhance and optimize your resume.</p>
          <button class="btn" onclick="uploadResume()">Upload Resume</button>
        </div>

        <div class="action-card">
          <h3>🔍 Analyze Job Description</h3>
          <p>Paste a job description or provide a URL to match with your resume.</p>
          <button class="btn" onclick="analyzeJob()">Analyze Job</button>
        </div>
      </section>

      <section class="resumes-section">
        <div class="resumes-header">
          <h2>Your Resumes</h2>
          <button class="btn" onclick="createResume()">New Resume</button>
        </div>
        
        <div class="resumes-list" id="resumes-list">
          <!-- Resumes will be loaded here -->
        </div>
        
        <div class="empty-state" id="empty-state">
          <h3>No resumes yet</h3>
          <p>Create your first resume to get started.</p>
        </div>
      </section>
    </div>
  </main>

  <footer>
    <div class="container">
      <p>&copy; 2025 ResumeAI. All rights reserved.</p>
    </div>
  </footer>

  <script>
    // Simple JavaScript for demo purposes
    function createResume() {
      alert('In the full application, this would open the resume builder. For now, this is just a demonstration.');
    }
    
    function uploadResume() {
      alert('In the full application, this would open a file upload dialog. For now, this is just a demonstration.');
    }
    
    function analyzeJob() {
      alert('In the full application, this would open the job analysis tool. For now, this is just a demonstration.');
    }
    
    // Load user resumes (mock data for demo)
    document.addEventListener('DOMContentLoaded', function() {
      const resumesList = document.getElementById('resumes-list');
      const emptyState = document.getElementById('empty-state');
      
      // For this demo, we'll just show the empty state
      // In the full application, this would load actual resumes from the API
      emptyState.style.display = 'block';
      resumesList.style.display = 'none';
    });
  </script>
</body>
</html>