interface PDFOptions {
    filename?: string;
    orientation?: 'portrait' | 'landscape';
    unit?: 'pt' | 'px' | 'in' | 'mm' | 'cm' | 'ex' | 'em' | 'pc';
    format?: string | number[];
}
/**
 * Generate a PDF from an HTML element
 * @param element - The HTML element to convert to PDF
 * @param options - PDF generation options
 */
export declare function generatePDFFromElement(element: HTMLElement, options?: PDFOptions): Promise<Blob>;
/**
 * Download a PDF blob
 * @param blob - The PDF blob to download
 * @param filename - The filename for the download
 */
export declare function downloadPDFBlob(blob: Blob, filename: string): void;
/**
 * Generate and download a PDF from an HTML element
 * @param element - The HTML element to convert to PDF
 * @param filename - The filename for the download
 * @param options - Additional PDF options
 */
export declare function generateAndDownloadPDF(element: HTMLElement, filename: string, options?: PDFOptions): Promise<void>;
export {};
//# sourceMappingURL=pdfGenerator.d.ts.map