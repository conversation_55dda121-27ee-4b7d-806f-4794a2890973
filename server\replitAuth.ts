import path from "node:path";
import * as client from "openid-client";
import { Strategy, type VerifyFunction } from "openid-client/passport";

import passport from "passport";
import session from "express-session";
import type { Express, RequestHandler } from "express";
import memoize from "memoizee";
import connectSqlite3 from "connect-sqlite3";
import connectPgSimple from "connect-pg-simple";
import pg from "pg";
import { storage } from "./storage";

// Set default values for development
const APP_URL = process.env.APP_URL || "http://localhost:5001";
const REPL_ID = process.env.REPL_ID || "dev-resume-app";

// Extract domains from APP_URL for compatibility
// For Vercel: https://your-app.vercel.app
// For local: http://localhost:5001
const REPLIT_DOMAINS = process.env.REPLIT_DOMAINS || new URL(APP_URL).host;

const getOidcConfig = memoize(
  async () => {
    return await client.discovery(
      new URL(process.env.ISSUER_URL ?? "https://replit.com/oidc"),
      REPL_ID
    );
  },
  { maxAge: 3600 * 1000 }
);

export function getSession() {
  const sessionTtl = 7 * 24 * 60 * 60 * 1000;
  const DATABASE_URL = process.env.DATABASE_URL;
  const isProduction = process.env.NODE_ENV === "production";

  let sessionStore: any;

  if (DATABASE_URL && !DATABASE_URL.startsWith("file:")) {
    // Use PostgreSQL for sessions in production
    const PgSession = connectPgSimple(session);
    const { Pool } = pg;
    const pool = new Pool({
      connectionString: DATABASE_URL,
      ssl: { rejectUnauthorized: false }
    });

    sessionStore = new PgSession({
      pool,
      tableName: "sessions",
      createTableIfMissing: true,
      ttl: Math.floor(sessionTtl / 1000),
    });
    console.log("Using PostgreSQL session store");
  } else {
    // Use SQLite for sessions in development
    const SqliteStore = connectSqlite3(session);
    sessionStore = new SqliteStore({
      db: process.env.SESSION_DB || "sessions.db",
      dir: path.join(process.cwd(), "data"),
      table: "sessions",
      ttl: Math.floor(sessionTtl / 1000),
    });
    console.log("Using SQLite session store");
  }

  const sessionSecret = process.env.SESSION_SECRET || "dev-secret-key-change-in-production-12345";
  console.log("Session secret configured:", sessionSecret ? "✓" : "✗");

  return session({
    secret: sessionSecret,
    store: sessionStore,
    resave: false,
    saveUninitialized: false,
    cookie: {
      httpOnly: true,
      secure: isProduction, // Secure cookies in production
      maxAge: sessionTtl,
    },
  });
}

function updateUserSession(
  user: any,
  tokens: client.TokenEndpointResponse & client.TokenEndpointResponseHelpers
) {
  user.claims = tokens.claims();
  user.access_token = tokens.access_token;
  user.refresh_token = tokens.refresh_token;
  user.expires_at = user.claims?.exp;
}

async function upsertUser(
  claims: any,
) {
  await storage.upsertUser({
    id: claims["sub"],
    email: claims["email"],
    firstName: claims["first_name"],
    lastName: claims["last_name"],
    profileImageUrl: claims["profile_image_url"],
  });
}

export async function setupAuth(app: Express) {
  app.set("trust proxy", 1);
  app.use(getSession());
  app.use(passport.initialize());
  app.use(passport.session());

  const config = await getOidcConfig();

  const verify: VerifyFunction = async (
    tokens: client.TokenEndpointResponse & client.TokenEndpointResponseHelpers,
    verified: passport.AuthenticateCallback
  ) => {
    const user = {};
    updateUserSession(user, tokens);
    await upsertUser(tokens.claims());
    verified(null, user);
  };

  // Parse domains - can be comma-separated or single domain
  const domains = typeof REPLIT_DOMAINS === 'string'
    ? REPLIT_DOMAINS.split(",")
    : [REPLIT_DOMAINS];

  for (const domain of domains) {
    const hostname = domain.split(":")[0];
    const appUrl = new URL(APP_URL);
    const protocol = appUrl.protocol.replace(":", "");

    // primary strategy including port (if provided)
    const primaryStrategy = new Strategy(
      {
        name: `replitauth:${domain}`,
        config,
        scope: "openid email profile offline_access",
        callbackURL: `${protocol}://${domain}/api/callback`,
      },
      verify,
    );
    passport.use(primaryStrategy);

    // register an alias without port (e.g., "localhost") so req.hostname matches
    if (hostname !== domain) {
      const aliasStrategy = new Strategy(
        {
          name: `replitauth:${hostname}`,
          config,
          scope: "openid email profile offline_access",
          // keep callbackURL pointed at the full domain (with port) used for OIDC
          callbackURL: `${protocol}://${domain}/api/callback`,
        },
        verify,
      );
      passport.use(aliasStrategy);
    }
  }
  
  // debug: list registered passport strategies to verify alias registration
  // (helps diagnose "Unknown authentication strategy" errors during local dev)
  // eslint-disable-next-line no-console
  console.log(
    "Registered passport strategies:",
    Object.keys((passport as any)._strategies || {})
  );
  
  passport.serializeUser((user: Express.User, cb) => cb(null, user));
  passport.deserializeUser((user: Express.User, cb) => cb(null, user));

  app.get("/api/login", async (req, res, next) => {
    // Local development fallback: only enabled when both the app is in `development`
    // AND the environment variable ALLOW_LOCAL_LOGIN is explicitly set to "true".
    // This prevents accidental insecure logins in shared/devops environments.
    if (app.get("env") === "development" && process.env.ALLOW_LOCAL_LOGIN === "true") {
      const devUser: any = {
        claims: { sub: "dev-user", email: "<EMAIL>", first_name: "Dev", last_name: "User" },
        access_token: "dev-token",
        refresh_token: undefined,
        expires_at: Math.floor(Date.now() / 1000) + 60 * 60,
      };

      // persist the dev user into the database so FK-linked operations (resumes, etc.)
      // work during local development
      try {
        await upsertUser(devUser.claims);
        // eslint-disable-next-line no-console
        console.log("Persisted dev user for local development:", devUser.claims.sub);
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error("Failed to persist dev user:", err);
      }

      req.login(devUser, (err) => {
        if (err) return next(err);
        return res.redirect("/app.html");
      });
      return;
    } else if (app.get("env") === "development") {
      // informative log when fallback is disabled to avoid confusion during local runs
      // eslint-disable-next-line no-console
      console.log("Local login fallback disabled. Set ALLOW_LOCAL_LOGIN=true to enable it.");
    }

    passport.authenticate(`replitauth:${req.hostname}`, {
      prompt: "login consent",
      scope: ["openid", "email", "profile", "offline_access"],
    })(req, res, next);
  });

  app.get("/api/callback", (req, res, next) => {
    // In development the login route above already logs in a dev user, so just redirect.
    if (app.get("env") === "development" && process.env.ALLOW_LOCAL_LOGIN === "true") {
      return res.redirect("/app.html");
    }

    passport.authenticate(`replitauth:${req.hostname}`, {
      successReturnToOrRedirect: "/app.html",
      failureRedirect: "/api/login",
    })(req, res, next);
  });

  app.get("/api/logout", (req, res) => {
    req.logout(() => {
      res.redirect(
        client.buildEndSessionUrl(config, {
          client_id: process.env.REPL_ID!,
          post_logout_redirect_uri: `${req.protocol}://${req.hostname}`,
        }).href
      );
    });
  });
}

export const isAuthenticated: RequestHandler = async (req, res, next) => {
  const user = req.user as any;

  if (!req.isAuthenticated() || !user.expires_at) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  const now = Math.floor(Date.now() / 1000);
  if (now <= user.expires_at) {
    return next();
  }

  const refreshToken = user.refresh_token;
  if (!refreshToken) {
    res.status(401).json({ message: "Unauthorized" });
    return;
  }

  try {
    const config = await getOidcConfig();
    const tokenResponse = await client.refreshTokenGrant(config, refreshToken);
    updateUserSession(user, tokenResponse);
    return next();
  } catch (error) {
    res.status(401).json({ message: "Unauthorized" });
    return;
  }
};
