/**
 * Vercel server entry helper
 *
 * This file ensures the server is initialized when running in a Vercel environment
 * (the original code attempted to import ../dist/index.js which caused TS errors
 * when that file doesn't exist locally). This implementation:
 *  - sets process.env.VERCEL = 'true'
 *  - attempts to import the built ../dist/index.js first (if present)
 *  - falls back to importing ../server/index.js (the local compiled server output)
 *  - safely calls initializeApp() if it exists
 *
 * Keep this file minimal and defensive to avoid runtime and TS import errors.
 */

process.env.VERCEL = 'true';

async function bootstrapServer() {
  try {
    let serverModule: any;

    // Try the dist build first (the original intent). If that file isn't present,
    // fall back to the local server build output under /server.
    // Use an indirect dynamic import so TypeScript won't try to resolve the literal module
    // at compile time (the built dist file may not exist in development).
    const dynamicImport = (p: string) => new Function('p', 'return import(p);')(p);
    try {
      // Attempt to load the production build if available
      // types/dist-index.d.ts already declares '../dist/index.js' so TS shouldn't error
      // when that declaration exists; the try/catch handles runtime absence.
      const distPath = '../dist/index.js';
      serverModule = await dynamicImport(distPath);
    } catch (distErr) {
      // Fallback to the repo's server entrypoint (compiled JS lives at server/index.js)
      serverModule = await dynamicImport('../server/index.js');
    }

    const initServer = serverModule?.initializeApp ?? serverModule?.default?.initializeApp;

    if (typeof initServer === 'function') {
      await initServer();
      // eslint-disable-next-line no-console
      console.log('[api/server] initializeApp completed');
    } else {
      // eslint-disable-next-line no-console
      console.warn('[api/server] No initializeApp() export found on server module');
    }
  } catch (err) {
    // Avoid crashing during import in environments where initialization isn't necessary
    // Log error for visibility.
    // eslint-disable-next-line no-console
    console.error('[api/server] Failed to bootstrap server:', err);
  }
}

// Run bootstrap asynchronously. Top-level await is intentionally avoided to keep
// this file compatible with different module system behaviors.
bootstrapServer();
