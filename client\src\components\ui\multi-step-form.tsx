import { ReactNode } from "react";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";

interface Step {
  id: number;
  title: string;
  description?: string;
}

interface MultiStepFormProps {
  steps: Step[];
  currentStep: number;
  children: ReactNode;
  className?: string;
}

interface StepIndicatorProps {
  steps: Step[];
  currentStep: number;
  onStepClick?: (stepId: number) => void;
  className?: string;
}

export function MultiStepForm({ steps, currentStep, children, className }: MultiStepFormProps) {
  return (
    <div className={cn("space-y-8", className)}>
      <StepIndicator steps={steps} currentStep={currentStep} />
      <div className="min-h-[400px]">
        {children}
      </div>
    </div>
  );
}

export function StepIndicator({ steps, currentStep, onStepClick, className }: StepIndicatorProps) {
  return (
    <nav className={cn("flex items-center justify-center", className)} aria-label="Progress">
      <ol className="flex items-center space-x-5">
        {steps.map((step, stepIdx) => {
          const isCompleted = step.id < currentStep;
          const isCurrent = step.id === currentStep;
          const isClickable = onStepClick && step.id <= currentStep;

          return (
            <li key={step.id} className="flex items-center">
              {stepIdx !== 0 && (
                <div
                  className={cn(
                    "hidden sm:block w-5 h-0.5 mx-4",
                    isCompleted ? "bg-primary" : "bg-muted"
                  )}
                />
              )}
              <div className="relative flex items-center">
                <button
                  type="button"
                  className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors",
                    isCurrent && "bg-primary text-primary-foreground",
                    isCompleted && "bg-primary text-primary-foreground",
                    !isCurrent && !isCompleted && "bg-muted text-muted-foreground",
                    isClickable && "hover:bg-primary/80 cursor-pointer",
                    !isClickable && "cursor-default"
                  )}
                  onClick={() => isClickable && onStepClick(step.id)}
                  disabled={!isClickable}
                  data-testid={`step-indicator-${step.id}`}
                >
                  {isCompleted ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <span>{step.id}</span>
                  )}
                </button>
                {step.title && (
                  <div className="ml-4 min-w-0 hidden sm:block">
                    <p
                      className={cn(
                        "text-sm font-medium",
                        isCurrent && "text-primary",
                        isCompleted && "text-foreground",
                        !isCurrent && !isCompleted && "text-muted-foreground"
                      )}
                    >
                      {step.title}
                    </p>
                    {step.description && (
                      <p className="text-xs text-muted-foreground">
                        {step.description}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

export function StepContent({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <div className={cn("space-y-6", className)}>
      {children}
    </div>
  );
}

export function StepNavigation({ 
  onPrevious, 
  onNext, 
  previousLabel = "Previous", 
  nextLabel = "Next",
  isPreviousDisabled = false,
  isNextDisabled = false,
  className 
}: {
  onPrevious?: () => void;
  onNext?: () => void;
  previousLabel?: string;
  nextLabel?: string;
  isPreviousDisabled?: boolean;
  isNextDisabled?: boolean;
  className?: string;
}) {
  return (
    <div className={cn("flex justify-between items-center pt-6 border-t border-border", className)}>
      <button
        type="button"
        onClick={onPrevious}
        disabled={isPreviousDisabled || !onPrevious}
        className={cn(
          "px-4 py-2 text-sm font-medium rounded-md transition-colors",
          "border border-border bg-background hover:bg-muted",
          "disabled:opacity-50 disabled:cursor-not-allowed"
        )}
        data-testid="step-navigation-previous"
      >
        {previousLabel}
      </button>
      
      <button
        type="button"
        onClick={onNext}
        disabled={isNextDisabled || !onNext}
        className={cn(
          "px-6 py-2 text-sm font-medium rounded-md transition-colors",
          "bg-primary text-primary-foreground hover:bg-primary/90",
          "disabled:opacity-50 disabled:cursor-not-allowed"
        )}
        data-testid="step-navigation-next"
      >
        {nextLabel}
      </button>
    </div>
  );
}
