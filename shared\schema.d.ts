import { z } from "zod";
export declare const sessions: import("drizzle-orm/sqlite-core").SQLiteTableWithColumns<{
    name: "sessions";
    schema: undefined;
    columns: {
        sid: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "sid";
            tableName: "sessions";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: true;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        sess: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "sess";
            tableName: "sessions";
            dataType: "json";
            columnType: "SQLiteTextJson";
            data: unknown;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        expire: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "expire";
            tableName: "sessions";
            dataType: "date";
            columnType: "SQLiteTimestamp";
            data: Date;
            driverParam: number;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
    };
    dialect: "sqlite";
}>;
export declare const users: import("drizzle-orm/sqlite-core").SQLiteTableWithColumns<{
    name: "users";
    schema: undefined;
    columns: {
        id: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "id";
            tableName: "users";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: true;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        email: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "email";
            tableName: "users";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        firstName: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "first_name";
            tableName: "users";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        lastName: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "last_name";
            tableName: "users";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        profileImageUrl: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "profile_image_url";
            tableName: "users";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        createdAt: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "created_at";
            tableName: "users";
            dataType: "date";
            columnType: "SQLiteTimestamp";
            data: Date;
            driverParam: number;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        updatedAt: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "updated_at";
            tableName: "users";
            dataType: "date";
            columnType: "SQLiteTimestamp";
            data: Date;
            driverParam: number;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
    };
    dialect: "sqlite";
}>;
export declare const resumes: import("drizzle-orm/sqlite-core").SQLiteTableWithColumns<{
    name: "resumes";
    schema: undefined;
    columns: {
        id: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "id";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: true;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        userId: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "user_id";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        title: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "title";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        templateId: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "template_id";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        industry: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "industry";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        status: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "status";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        paymentStatus: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "payment_status";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        stripeSessionId: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "stripe_session_id";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        paymentAmount: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "payment_amount";
            tableName: "resumes";
            dataType: "number";
            columnType: "SQLiteInteger";
            data: number;
            driverParam: number;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        paymentCurrency: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "payment_currency";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        paymentMethod: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "payment_method";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        paidAt: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "paid_at";
            tableName: "resumes";
            dataType: "date";
            columnType: "SQLiteTimestamp";
            data: Date;
            driverParam: number;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        stripeCustomerId: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "stripe_customer_id";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        stripePaymentIntentId: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "stripe_payment_intent_id";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        personalInfo: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "personal_info";
            tableName: "resumes";
            dataType: "json";
            columnType: "SQLiteTextJson";
            data: unknown;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        professionalSummary: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "professional_summary";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        workExperience: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "work_experience";
            tableName: "resumes";
            dataType: "json";
            columnType: "SQLiteTextJson";
            data: unknown;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        education: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "education";
            tableName: "resumes";
            dataType: "json";
            columnType: "SQLiteTextJson";
            data: unknown;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        skills: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "skills";
            tableName: "resumes";
            dataType: "json";
            columnType: "SQLiteTextJson";
            data: unknown;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        targetJobTitle: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "target_job_title";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        targetCompany: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "target_company";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        jobDescription: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "job_description";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        jobUrl: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "job_url";
            tableName: "resumes";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        extractedKeywords: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "extracted_keywords";
            tableName: "resumes";
            dataType: "json";
            columnType: "SQLiteTextJson";
            data: unknown;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        aiEnhanced: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "ai_enhanced";
            tableName: "resumes";
            dataType: "boolean";
            columnType: "SQLiteBoolean";
            data: boolean;
            driverParam: number;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        createdAt: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "created_at";
            tableName: "resumes";
            dataType: "date";
            columnType: "SQLiteTimestamp";
            data: Date;
            driverParam: number;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        updatedAt: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "updated_at";
            tableName: "resumes";
            dataType: "date";
            columnType: "SQLiteTimestamp";
            data: Date;
            driverParam: number;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
    };
    dialect: "sqlite";
}>;
export declare const jobAnalyses: import("drizzle-orm/sqlite-core").SQLiteTableWithColumns<{
    name: "job_analyses";
    schema: undefined;
    columns: {
        id: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "id";
            tableName: "job_analyses";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: true;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        resumeId: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "resume_id";
            tableName: "job_analyses";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        jobDescription: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "job_description";
            tableName: "job_analyses";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        extractedKeywords: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "extracted_keywords";
            tableName: "job_analyses";
            dataType: "json";
            columnType: "SQLiteTextJson";
            data: unknown;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        requirements: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "requirements";
            tableName: "job_analyses";
            dataType: "json";
            columnType: "SQLiteTextJson";
            data: unknown;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        suggestedImprovements: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "suggested_improvements";
            tableName: "job_analyses";
            dataType: "json";
            columnType: "SQLiteTextJson";
            data: unknown;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        matchScore: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "match_score";
            tableName: "job_analyses";
            dataType: "string";
            columnType: "SQLiteText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: number | undefined;
        }>;
        createdAt: import("drizzle-orm/sqlite-core").SQLiteColumn<{
            name: "created_at";
            tableName: "job_analyses";
            dataType: "date";
            columnType: "SQLiteTimestamp";
            data: Date;
            driverParam: number;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
    };
    dialect: "sqlite";
}>;
export declare const usersRelations: import("drizzle-orm").Relations<"users", {
    resumes: import("drizzle-orm").Many<"resumes">;
}>;
export declare const resumesRelations: import("drizzle-orm").Relations<"resumes", {
    user: import("drizzle-orm").One<"users", true>;
    jobAnalyses: import("drizzle-orm").Many<"job_analyses">;
}>;
export declare const jobAnalysesRelations: import("drizzle-orm").Relations<"job_analyses", {
    resume: import("drizzle-orm").One<"resumes", true>;
}>;
export declare const insertUserSchema: z.ZodObject<Omit<{
    id: z.ZodString;
    email: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    firstName: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    lastName: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    profileImageUrl: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    createdAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
    updatedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "createdAt" | "updatedAt">, "strip", z.ZodTypeAny, {
    id: string;
    email?: string | null | undefined;
    firstName?: string | null | undefined;
    lastName?: string | null | undefined;
    profileImageUrl?: string | null | undefined;
}, {
    id: string;
    email?: string | null | undefined;
    firstName?: string | null | undefined;
    lastName?: string | null | undefined;
    profileImageUrl?: string | null | undefined;
}>;
export declare const insertResumeSchema: z.ZodObject<Omit<{
    id: z.ZodString;
    userId: z.ZodString;
    title: z.ZodString;
    templateId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    industry: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    status: z.ZodOptional<z.ZodString>;
    paymentStatus: z.ZodOptional<z.ZodString>;
    stripeSessionId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    paymentAmount: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    paymentCurrency: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    paymentMethod: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    paidAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
    stripeCustomerId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    stripePaymentIntentId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    personalInfo: z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>;
    professionalSummary: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    workExperience: z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>;
    education: z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>;
    skills: z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>;
    targetJobTitle: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    targetCompany: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    jobDescription: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    jobUrl: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    extractedKeywords: z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>;
    aiEnhanced: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
    createdAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
    updatedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "id" | "createdAt" | "updatedAt" | "stripeSessionId" | "paidAt" | "stripeCustomerId" | "stripePaymentIntentId">, "strip", z.ZodTypeAny, {
    userId: string;
    title: string;
    templateId?: string | null | undefined;
    industry?: string | null | undefined;
    status?: string | undefined;
    paymentStatus?: string | undefined;
    paymentAmount?: number | null | undefined;
    paymentCurrency?: string | null | undefined;
    paymentMethod?: string | null | undefined;
    personalInfo?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    professionalSummary?: string | null | undefined;
    workExperience?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    education?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    skills?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    targetJobTitle?: string | null | undefined;
    targetCompany?: string | null | undefined;
    jobDescription?: string | null | undefined;
    jobUrl?: string | null | undefined;
    extractedKeywords?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    aiEnhanced?: boolean | null | undefined;
}, {
    userId: string;
    title: string;
    templateId?: string | null | undefined;
    industry?: string | null | undefined;
    status?: string | undefined;
    paymentStatus?: string | undefined;
    paymentAmount?: number | null | undefined;
    paymentCurrency?: string | null | undefined;
    paymentMethod?: string | null | undefined;
    personalInfo?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    professionalSummary?: string | null | undefined;
    workExperience?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    education?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    skills?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    targetJobTitle?: string | null | undefined;
    targetCompany?: string | null | undefined;
    jobDescription?: string | null | undefined;
    jobUrl?: string | null | undefined;
    extractedKeywords?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    aiEnhanced?: boolean | null | undefined;
}>;
export declare const updateResumeSchema: z.ZodObject<z.objectUtil.extendShape<{
    userId: z.ZodOptional<z.ZodString>;
    title: z.ZodOptional<z.ZodString>;
    templateId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    industry: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    status: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    paymentStatus: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    paymentAmount: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    paymentCurrency: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    paymentMethod: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    personalInfo: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>>;
    professionalSummary: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    workExperience: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>>;
    education: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>>;
    skills: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>>;
    targetJobTitle: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    targetCompany: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    jobDescription: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    jobUrl: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    extractedKeywords: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>>;
    aiEnhanced: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
}, {
    id: z.ZodString;
    stripeSessionId: z.ZodOptional<z.ZodString>;
    stripePaymentIntentId: z.ZodOptional<z.ZodString>;
    stripeCustomerId: z.ZodOptional<z.ZodString>;
    paidAt: z.ZodOptional<z.ZodDate>;
}>, "strip", z.ZodTypeAny, {
    id: string;
    userId?: string | undefined;
    title?: string | undefined;
    templateId?: string | null | undefined;
    industry?: string | null | undefined;
    status?: string | undefined;
    paymentStatus?: string | undefined;
    stripeSessionId?: string | undefined;
    paymentAmount?: number | null | undefined;
    paymentCurrency?: string | null | undefined;
    paymentMethod?: string | null | undefined;
    paidAt?: Date | undefined;
    stripeCustomerId?: string | undefined;
    stripePaymentIntentId?: string | undefined;
    personalInfo?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    professionalSummary?: string | null | undefined;
    workExperience?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    education?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    skills?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    targetJobTitle?: string | null | undefined;
    targetCompany?: string | null | undefined;
    jobDescription?: string | null | undefined;
    jobUrl?: string | null | undefined;
    extractedKeywords?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    aiEnhanced?: boolean | null | undefined;
}, {
    id: string;
    userId?: string | undefined;
    title?: string | undefined;
    templateId?: string | null | undefined;
    industry?: string | null | undefined;
    status?: string | undefined;
    paymentStatus?: string | undefined;
    stripeSessionId?: string | undefined;
    paymentAmount?: number | null | undefined;
    paymentCurrency?: string | null | undefined;
    paymentMethod?: string | null | undefined;
    paidAt?: Date | undefined;
    stripeCustomerId?: string | undefined;
    stripePaymentIntentId?: string | undefined;
    personalInfo?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    professionalSummary?: string | null | undefined;
    workExperience?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    education?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    skills?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    targetJobTitle?: string | null | undefined;
    targetCompany?: string | null | undefined;
    jobDescription?: string | null | undefined;
    jobUrl?: string | null | undefined;
    extractedKeywords?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    aiEnhanced?: boolean | null | undefined;
}>;
export declare const insertJobAnalysisSchema: z.ZodObject<Omit<{
    id: z.ZodString;
    resumeId: z.ZodString;
    jobDescription: z.ZodString;
    extractedKeywords: z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>;
    requirements: z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>;
    suggestedImprovements: z.ZodOptional<z.ZodNullable<z.ZodType<import("node_modules/drizzle-zod/utils.mjs").Json, z.ZodTypeDef, import("node_modules/drizzle-zod/utils.mjs").Json>>>;
    matchScore: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    createdAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "id" | "createdAt">, "strip", z.ZodTypeAny, {
    jobDescription: string;
    resumeId: string;
    extractedKeywords?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    requirements?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    suggestedImprovements?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    matchScore?: string | null | undefined;
}, {
    jobDescription: string;
    resumeId: string;
    extractedKeywords?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    requirements?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    suggestedImprovements?: import("node_modules/drizzle-zod/utils.mjs").Json | undefined;
    matchScore?: string | null | undefined;
}>;
export type UpsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertResume = z.infer<typeof insertResumeSchema>;
export type UpdateResume = z.infer<typeof updateResumeSchema>;
export type Resume = typeof resumes.$inferSelect;
export type InsertJobAnalysis = z.infer<typeof insertJobAnalysisSchema>;
export type JobAnalysis = typeof jobAnalyses.$inferSelect;
export type PersonalInfo = {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    city: string;
    state: string;
    linkedinUrl?: string;
};
export type WorkExperience = {
    id: string;
    company: string;
    position: string;
    startDate: string;
    endDate?: string;
    current: boolean;
    description: string;
    achievements: string[];
};
export type Education = {
    id: string;
    institution: string;
    degree: string;
    field: string;
    graduationDate: string;
    gpa?: string;
};
export type Skill = {
    id: string;
    name: string;
    level: string;
    category: string;
};
//# sourceMappingURL=schema.d.ts.map