@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(210, 40%, 98%);
  --foreground: hsl(222.2, 84%, 4.9%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(222.2, 84%, 4.9%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(222.2, 84%, 4.9%);
  --primary: hsl(221, 83%, 53%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(210, 40%, 96%);
  --secondary-foreground: hsl(222.2, 84%, 4.9%);
  --muted: hsl(210, 40%, 96%);
  --muted-foreground: hsl(215.4, 16.3%, 46.9%);
  --accent: hsl(24, 95%, 53%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --border: hsl(214.3, 31.8%, 91.4%);
  --input: hsl(214.3, 31.8%, 91.4%);
  --ring: hsl(221, 83%, 53%);
  --chart-1: hsl(221, 83%, 53%);
  --chart-2: hsl(159.7826, 100%, 36.0784%);
  --chart-3: hsl(42.0290, 92.8251%, 56.2745%);
  --chart-4: hsl(147.1429, 78.5047%, 41.9608%);
  --chart-5: hsl(341.4894, 75.2000%, 50.9804%);
  --sidebar: hsl(0, 0%, 100%);
  --sidebar-foreground: hsl(222.2, 84%, 4.9%);
  --sidebar-primary: hsl(221, 83%, 53%);
  --sidebar-primary-foreground: hsl(210, 40%, 98%);
  --sidebar-accent: hsl(210, 40%, 96%);
  --sidebar-accent-foreground: hsl(222.2, 84%, 4.9%);
  --sidebar-border: hsl(214.3, 31.8%, 91.4%);
  --sidebar-ring: hsl(221, 83%, 53%);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 0.75rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(221, 83%, 53% / 0.00);
  --shadow-xs: 0px 2px 0px 0px hsl(221, 83%, 53% / 0.00);
  --shadow-sm: 0px 2px 0px 0px hsl(221, 83%, 53% / 0.00), 0px 1px 2px -1px hsl(221, 83%, 53% / 0.00);
  --shadow: 0px 2px 0px 0px hsl(221, 83%, 53% / 0.00), 0px 1px 2px -1px hsl(221, 83%, 53% / 0.00);
  --shadow-md: 0px 2px 0px 0px hsl(221, 83%, 53% / 0.00), 0px 2px 4px -1px hsl(221, 83%, 53% / 0.00);
  --shadow-lg: 0px 2px 0px 0px hsl(221, 83%, 53% / 0.00), 0px 4px 6px -1px hsl(221, 83%, 53% / 0.00);
  --shadow-xl: 0px 2px 0px 0px hsl(221, 83%, 53% / 0.00), 0px 8px 10px -1px hsl(221, 83%, 53% / 0.00);
  --shadow-2xl: 0px 2px 0px 0px hsl(221, 83%, 53% / 0.00);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: hsl(222.2, 84%, 4.9%);
  --foreground: hsl(210, 40%, 98%);
  --card: hsl(222.2, 84%, 4.9%);
  --card-foreground: hsl(210, 40%, 98%);
  --popover: hsl(222.2, 84%, 4.9%);
  --popover-foreground: hsl(210, 40%, 98%);
  --primary: hsl(217.2, 91.2%, 59.8%);
  --primary-foreground: hsl(222.2, 84%, 4.9%);
  --secondary: hsl(217.2, 32.6%, 17.5%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --muted: hsl(217.2, 32.6%, 17.5%);
  --muted-foreground: hsl(215, 20.2%, 65.1%);
  --accent: hsl(24, 95%, 53%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --border: hsl(217.2, 32.6%, 17.5%);
  --input: hsl(217.2, 32.6%, 17.5%);
  --ring: hsl(224.3, 76.3%, 94.1%);
  --chart-1: hsl(217.2, 91.2%, 59.8%);
  --chart-2: hsl(159.7826, 100%, 36.0784%);
  --chart-3: hsl(42.0290, 92.8251%, 56.2745%);
  --chart-4: hsl(147.1429, 78.5047%, 41.9608%);
  --chart-5: hsl(341.4894, 75.2000%, 50.9804%);
  --sidebar: hsl(222.2, 84%, 4.9%);
  --sidebar-foreground: hsl(210, 40%, 98%);
  --sidebar-primary: hsl(221, 83%, 53%);
  --sidebar-primary-foreground: hsl(210, 40%, 98%);
  --sidebar-accent: hsl(217.2, 32.6%, 17.5%);
  --sidebar-accent-foreground: hsl(217.2, 91.2%, 59.8%);
  --sidebar-border: hsl(217.2, 32.6%, 17.5%);
  --sidebar-ring: hsl(221, 83%, 53%);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 0.75rem;
}

.gradient-bg {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
}

.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideInRight {
  from { 
    opacity: 0; 
    transform: translateX(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateX(0); 
  }
}

.slide-in {
  animation: slideInRight 0.3s ease-out;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-lg transition-colors;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 px-4 py-2 rounded-lg transition-colors;
  }

  .card-hover {
    @apply hover:shadow-lg transition-shadow duration-200;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-border rounded-lg bg-card focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-colors;
  }

  .text-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Print Styles for Invoices */
@media print {
  @page {
    size: letter;
    margin: 0.5in;
  }

  body {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }

  .print\:hidden {
    display: none !important;
  }

  .print\:block {
    display: block !important;
  }

  .print\:p-0 {
    padding: 0 !important;
  }

  .print\:p-8 {
    padding: 2rem !important;
  }

  .print\:bg-white {
    background-color: white !important;
  }

  .print\:py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .print\:px-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .page-break-before {
    page-break-before: always;
  }

  .page-break-after {
    page-break-after: always;
  }

  .page-break-inside-avoid {
    page-break-inside: avoid;
  }

  /* Ensure proper border and background printing */
  table,
  .border,
  .border-black,
  .border-t-2,
  .border-b-2,
  .bg-gray-100,
  .bg-gray-200,
  .bg-yellow-100,
  .bg-green-500 {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }
}
