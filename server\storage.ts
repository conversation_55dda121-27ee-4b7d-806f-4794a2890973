import { randomUUID } from "node:crypto";
import {
  users,
  resumes,
  jobAnalyses,
  type User,
  type UpsertUser,
  type Resume,
  type InsertResume,
  type UpdateResume,
  type JobAnalysis,
  type InsertJobAnalysis,
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and } from "drizzle-orm";

function stripUndefined<T extends Record<string, unknown>>(input: T): Partial<T> {
  return Object.fromEntries(
    Object.entries(input).filter(([, value]) => value !== undefined)
  ) as Partial<T>;
}

export interface IStorage {
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  getUserResumes(userId: string): Promise<Resume[]>;
  getResume(id: string, userId: string): Promise<Resume | undefined>;
  createResume(resume: InsertResume): Promise<Resume>;
  updateResume(resume: UpdateResume): Promise<Resume>;
  deleteResume(id: string, userId: string): Promise<void>;
  createJobAnalysis(analysis: InsertJobAnalysis): Promise<JobAnalysis>;
  getJobAnalysisByResumeId(resumeId: string): Promise<JobAnalysis | undefined>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values({ ...userData, id: userData.id ?? randomUUID() })
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  async getUserResumes(userId: string): Promise<Resume[]> {
    return await db
      .select()
      .from(resumes)
      .where(eq(resumes.userId, userId))
      .orderBy(desc(resumes.updatedAt));
  }

  async getResume(id: string, userId: string): Promise<Resume | undefined> {
    const [resume] = await db
      .select()
      .from(resumes)
      .where(and(eq(resumes.id, id), eq(resumes.userId, userId)));
    return resume;
  }

  async createResume(resume: InsertResume): Promise<Resume> {
    const timestamp = new Date();
    const [newResume] = await db
      .insert(resumes)
      .values({
        ...resume,
        id: randomUUID(),
        createdAt: timestamp,
        updatedAt: timestamp,
      })
      .returning();
    return newResume;
  }

  async updateResume(resume: UpdateResume): Promise<Resume> {
    const { id, ...updateData } = resume;
    const payload = stripUndefined(updateData);
    const [updatedResume] = await db
      .update(resumes)
      .set({
        ...payload,
        updatedAt: new Date(),
      })
      .where(eq(resumes.id, id))
      .returning();
    return updatedResume;
  }

  async deleteResume(id: string, userId: string): Promise<void> {
    await db
      .delete(resumes)
      .where(and(eq(resumes.id, id), eq(resumes.userId, userId)));
  }

  async createJobAnalysis(analysis: InsertJobAnalysis): Promise<JobAnalysis> {
    const [newAnalysis] = await db
      .insert(jobAnalyses)
      .values({
        ...analysis,
        id: randomUUID(),
        createdAt: new Date(),
      })
      .returning();
    return newAnalysis;
  }

  async getJobAnalysisByResumeId(resumeId: string): Promise<JobAnalysis | undefined> {
    const [analysis] = await db
      .select()
      .from(jobAnalyses)
      .where(eq(jobAnalyses.resumeId, resumeId))
      .orderBy(desc(jobAnalyses.createdAt))
      .limit(1);
    return analysis;
  }
}

export const storage = new DatabaseStorage();
