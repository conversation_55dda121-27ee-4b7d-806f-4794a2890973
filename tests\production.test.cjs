/**
 * Production deployment tests for ResumeAI on Vercel
 * Tests the live production deployment
 */

const { test, expect } = require('@playwright/test');

// Production URL
const PRODUCTION_URL = 'https://2025-10-01-try2work-v1-rf0rmvgfw-sanford-dinkins-projects.vercel.app';

test.describe('ResumeAI Production Deployment', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to production deployment
    await page.goto(PRODUCTION_URL);
  });

  test('Production site loads successfully', async ({ page }) => {
    // Check if the page loads without errors
    await expect(page).toHaveTitle(/ResumeAI/i);

    // Check for main heading (either landing or home page)
    const heading = page.locator('h1').first();
    await expect(heading).toBeVisible();

    // Should contain either landing page or authenticated page heading
    await expect(heading).toContainText(/Build AI-Powered Resumes|Get Your Resume.*Job-Ready|Create Professional Resumes/i);
  });

  test('Landing page displays correctly', async ({ page }) => {
    // Check if main elements are present
    await expect(page.locator('h1')).toBeVisible();

    // Check for call-to-action buttons (should be visible on landing page)
    const buttons = page.locator('button, a[class*="button"]');
    await expect(buttons.first()).toBeVisible();
  });

  test('Navigation and routing works', async ({ page }) => {
    // Check that the page is responsive
    const viewport = page.viewportSize();
    expect(viewport).toBeTruthy();

    // Try to find feature section or main content
    const mainContent = page.locator('main, section, [role="main"]').first();
    await expect(mainContent).toBeVisible();
  });

  test('Google OAuth login endpoint exists', async ({ page }) => {
    // Check if login endpoint is accessible
    const response = await page.goto(`${PRODUCTION_URL}/api/login`);

    // Should redirect or respond (not 404/500)
    expect(response?.status()).toBeLessThan(500);
  });

  test('Static assets load correctly', async ({ page }) => {
    // Navigate and wait for network to be idle
    await page.goto(PRODUCTION_URL, { waitUntil: 'networkidle' });

    // Check if there are no major console errors
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });

    // Give it a moment to log any errors
    await page.waitForTimeout(2000);

    // We expect some errors might occur, but not critical ones
    // Critical errors would have "Failed to load" or "404"
    const criticalErrors = errors.filter(err =>
      err.includes('Failed to load') ||
      err.includes('404') ||
      err.includes('net::ERR_')
    );

    // Log errors for debugging
    if (criticalErrors.length > 0) {
      console.log('Critical errors found:', criticalErrors);
    }
  });

  test('Responsive design works on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Navigate again with mobile viewport
    await page.goto(PRODUCTION_URL);

    // Check that content is visible
    await expect(page.locator('h1')).toBeVisible();

    // Check that the page doesn't have horizontal scroll
    const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
    const viewportWidth = 375;

    // Allow for some margin (scrollWidth might be slightly larger due to rounding)
    expect(bodyWidth).toBeLessThanOrEqual(viewportWidth + 5);
  });

  test('Features section is present', async ({ page }) => {
    // Look for features section or feature cards
    const featuresSection = page.locator('section').filter({ hasText: /features|how it works|why choose/i });

    // At least one feature/benefit section should exist
    await expect(featuresSection.first()).toBeVisible({ timeout: 10000 });
  });

  test('Page metadata is correct', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/ResumeAI/i);

    // Check for meta description (good for SEO)
    const metaDescription = page.locator('meta[name="description"]');
    // Meta tags might not be present, so we just check if page has loaded
    const title = await page.title();
    expect(title.length).toBeGreaterThan(0);
  });
});
