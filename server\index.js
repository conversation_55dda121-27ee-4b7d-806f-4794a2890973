import dotenv from "dotenv";
dotenv.config();
import express from "express";
import session from "express-session";
import { registerRoutes } from "./routes.js";
import { setupPassportRoutes } from "./passportRoutes.js";
import { passport } from "./passportAuth.js";
import { serveStatic, log } from "./vite.js";
const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
// Session configuration
app.use(session({
    secret: process.env.SESSION_SECRET || 'your-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
}));
// Initialize Passport
app.use(passport.initialize());
app.use(passport.session());
// Setup Passport routes
setupPassportRoutes(app);
app.use((req, res, next) => {
    const start = Date.now();
    const path = req.path;
    let capturedJsonResponse = undefined;
    const originalResJson = res.json;
    res.json = function (bodyJson, ...args) {
        capturedJsonResponse = bodyJson;
        return originalResJson.apply(res, [bodyJson, ...args]);
    };
    res.on("finish", () => {
        const duration = Date.now() - start;
        if (path.startsWith("/api")) {
            let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
            if (capturedJsonResponse) {
                logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
            }
            if (logLine.length > 80) {
                logLine = logLine.slice(0, 79) + "…";
            }
            log(logLine);
        }
    });
    next();
});
// For Vercel serverless - initialize immediately
let initializationPromise = null;
export async function initializeApp() {
    if (initializationPromise)
        return initializationPromise;
    initializationPromise = (async () => {
        try {
            console.log('[Server] Starting initialization...');
            await registerRoutes(app);
            app.use((err, _req, res, _next) => {
                const status = err.status || err.statusCode || 500;
                const message = err.message || "Internal Server Error";
                console.error('[Server] Error:', err);
                res.status(status).json({ message });
            });
            // Only serve static in development
            if (!process.env.VERCEL) {
                serveStatic(app);
            }
            console.log('[Server] Initialization complete');
        }
        catch (error) {
            console.error('[Server] Initialization failed:', error);
            initializationPromise = null;
            throw error;
        }
    })();
    return initializationPromise;
}
// Only run for local development
if (!process.env.VERCEL) {
    initializeApp().then(() => {
        const port = parseInt(process.env.PORT || "5001", 10);
        const server = app.listen(port, "0.0.0.0", () => {
            log(`serving on port ${port}`);
        });
        return server;
    }).catch((err) => {
        console.error('Failed to start server:', err);
        process.exit(1);
    });
}
// Export app and initialization function
export { app };
export default app;
