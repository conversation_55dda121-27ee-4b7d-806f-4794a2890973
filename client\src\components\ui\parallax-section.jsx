import { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";
export default function ParallaxSection({ children, className, speed = 0.5, offset = 0, }) {
    const [offsetY, setOffsetY] = useState(0);
    const ref = useRef(null);
    useEffect(() => {
        const handleScroll = () => {
            if (ref.current) {
                const rect = ref.current.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const elementTop = rect.top + scrollTop;
                const elementHeight = rect.height;
                const windowHeight = window.innerHeight;
                // Calculate how much of the element is visible
                const visibleHeight = Math.min(Math.max(0, windowHeight - Math.max(0, rect.top)), Math.min(elementHeight, windowHeight - Math.max(0, rect.bottom - windowHeight)));
                // Calculate the parallax offset based on scroll position
                const scrollPosition = scrollTop - elementTop + windowHeight;
                const maxOffset = elementHeight + windowHeight;
                const scrollPercentage = scrollPosition / maxOffset;
                // Apply the parallax effect
                const newOffsetY = scrollPercentage * speed * elementHeight;
                setOffsetY(newOffsetY);
            }
        };
        // Throttle scroll events for better performance
        let ticking = false;
        const throttledHandleScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        };
        // Initial calculation
        handleScroll();
        // Add scroll event listener
        window.addEventListener("scroll", throttledHandleScroll);
        // Clean up
        return () => window.removeEventListener("scroll", throttledHandleScroll);
    }, [speed]);
    return (<div ref={ref} className={cn("mi-parallax-section", className)} style={{
            "--mi-parallax-offset": `${offsetY + offset}px`,
        }}>
      {children}
    </div>);
}
