# Domain Setup: try2work.com

This guide explains how to set up your custom domain `try2work.com` on Vercel.

## Prerequisites

- Deployed application on Vercel
- Access to your domain registrar (where you purchased try2work.com)
- Vercel account with access to the project

## Step 1: Add Domain to Vercel

1. Go to your Vercel project dashboard
2. Navigate to **Settings** > **Domains**
3. Add your domains:
   - Primary: `try2work.com`
   - WWW variant: `www.try2work.com` (optional)

## Step 2: Configure DNS Records

Vercel will provide DNS records to configure. Add these to your domain registrar:

### Option A: Using Vercel Nameservers (Recommended)

If your registrar supports custom nameservers, use Vercel's nameservers:

```
ns1.vercel-dns.com
ns2.vercel-dns.com
```

This is the easiest option as Vercel manages all DNS automatically.

### Option B: Using A and CNAME Records

If you can't change nameservers, add these DNS records at your registrar:

**For root domain (try2work.com):**
```
Type: A
Name: @
Value: 76.76.21.21
```

**For www subdomain (www.try2work.com):**
```
Type: CNAME
Name: www
Value: cname.vercel-dns.com
```

## Step 3: Verify Domain

1. After adding DNS records, wait 5-60 minutes for propagation
2. Vercel will automatically verify the domain
3. Check status in Vercel dashboard under **Domains**

## Step 4: Update Environment Variables

Make sure these environment variables are set in Vercel:

```bash
APP_URL=https://try2work.com
```

You can update this in:
- Vercel Dashboard > Settings > Environment Variables

## Step 5: Configure SSL Certificate

Vercel automatically provisions SSL certificates for your domain:
- SSL/TLS certificate is issued by Let's Encrypt
- Auto-renewal is handled by Vercel
- HTTPS is enforced by default

## Step 6: Update Stripe Webhook

After domain is active, update your Stripe webhook endpoint:

1. Go to [Stripe Dashboard > Webhooks](https://dashboard.stripe.com/webhooks)
2. Update or create webhook endpoint:
   ```
   https://try2work.com/api/stripe/webhook
   ```
3. Select events:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `checkout.session.completed`
4. Copy the webhook signing secret to Vercel environment variables

## Step 7: Test Your Domain

1. Visit `https://try2work.com`
2. Verify HTTPS is working (padlock icon in browser)
3. Test authentication flow
4. Test payment flow if applicable

## Domain Redirect Configuration

If you want to redirect `www.try2work.com` to `try2work.com` (or vice versa):

1. Add both domains in Vercel
2. Set one as primary in Vercel dashboard
3. Vercel will automatically redirect non-primary to primary

## DNS Propagation Check

Check if your DNS changes have propagated:

```bash
# Check A record
nslookup try2work.com

# Check CNAME record
nslookup www.try2work.com

# Or use online tools
# https://dnschecker.org
```

## Troubleshooting

### Domain Not Verifying

- Wait longer (up to 48 hours in rare cases)
- Double-check DNS records are correct
- Ensure no conflicting DNS records exist
- Try removing and re-adding the domain in Vercel

### SSL Certificate Issues

- Wait 5-10 minutes after domain verification
- Vercel provisions SSL automatically
- Check Vercel dashboard for certificate status

### Redirect Loop

- Clear browser cache and cookies
- Check `APP_URL` environment variable is correct
- Ensure no conflicting redirects in your code

### 404 Errors

- Verify Vercel deployment is successful
- Check `vercel.json` rewrites configuration
- Test with Vercel's default domain first

## Additional Configuration

### Email with Custom Domain

If you want to set up email for try2work.com:

1. Add MX records from your email provider
2. Common providers:
   - Google Workspace
   - Microsoft 365
   - Zoho Mail

### Subdomain Configuration

To add subdomains (e.g., `api.try2work.com`, `app.try2work.com`):

1. Add subdomain in Vercel dashboard
2. Create CNAME record pointing to `cname.vercel-dns.com`
3. Configure routing in your application

## Production Checklist

Before going live:

- [ ] Domain added to Vercel
- [ ] DNS records configured and propagated
- [ ] SSL certificate active
- [ ] `APP_URL` environment variable updated
- [ ] Stripe webhook updated with production domain
- [ ] Test all critical user flows
- [ ] Monitor error logs in Vercel dashboard
- [ ] Set up monitoring/analytics

## Support Resources

- [Vercel Domains Documentation](https://vercel.com/docs/concepts/projects/domains)
- [Vercel DNS Records Guide](https://vercel.com/docs/concepts/projects/domains/dns-records)
- [SSL Certificate Issues](https://vercel.com/docs/concepts/projects/domains/troubleshooting)
