import { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { useMutation } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FileText, Upload, Link2, Sparkles, Check, ArrowRight } from "lucide-react";
import { useLocation } from "wouter";

export default function Home() {
  const { toast } = useToast();
  const { user, isAuthenticated, isLoading } = useAuth();
  const [, navigate] = useLocation();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      window.location.href = "/api/login";
      return;
    }
  }, [isAuthenticated, isLoading]);

  // Create new resume and navigate to builder
  const createResumeMutation = useMutation({
    mutationFn: async () => {
      return await apiRequest('POST', '/api/resumes', {
        title: `Resume - ${new Date().toLocaleDateString()}`,
        status: 'draft'
      });
    },
    onSuccess: (data: any) => {
      navigate(`/resume-builder/${data.id}`);
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        window.location.href = "/api/login";
        return;
      }
      toast({
        title: "Error",
        description: "Failed to start. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleStartOptimization = () => {
    createResumeMutation.mutate();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background to-primary/5">
      {/* Hero Section */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-6">
            <Sparkles className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-5xl md:text-6xl font-bold text-foreground mb-6">
            Get Your Resume <span className="text-primary">Job-Ready</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
            Tailor your resume to any job posting in minutes. Our AI analyzes the job requirements
            and optimizes your resume for maximum impact.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="text-lg px-8 py-6"
              onClick={handleStartOptimization}
              disabled={createResumeMutation.isPending}
            >
              {createResumeMutation.isPending ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Starting...
                </>
              ) : (
                <>
                  Start Optimizing
                  <ArrowRight className="ml-2 h-5 w-5" />
                </>
              )}
            </Button>
          </div>
        </div>

        {/* How It Works */}
        <div className="mb-20">
          <h2 className="text-3xl font-bold text-center mb-12">How It Works</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-2 border-border hover:border-primary/50 transition-colors">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Upload className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">1. Upload or Enter Your Resume</h3>
                <p className="text-sm text-muted-foreground">
                  Upload your existing resume (PDF/DOCX) or quickly enter your experience manually
                </p>
              </CardContent>
            </Card>

            <Card className="border-2 border-border hover:border-primary/50 transition-colors">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Link2 className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">2. Add Job Posting</h3>
                <p className="text-sm text-muted-foreground">
                  Paste the job posting URL or description you're applying to
                </p>
              </CardContent>
            </Card>

            <Card className="border-2 border-border hover:border-primary/50 transition-colors">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">3. Get Optimized Resume</h3>
                <p className="text-sm text-muted-foreground">
                  AI tailors your resume to match the job requirements. Download PDF - Free for a limited time!
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Features */}
        <Card className="mb-16 border-2">
          <CardContent className="p-8">
            <h2 className="text-2xl font-bold mb-6 text-center">Why Choose Our Resume Optimizer?</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="flex gap-3">
                <Check className="h-6 w-6 text-green-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-semibold mb-1">ATS-Optimized</h4>
                  <p className="text-sm text-muted-foreground">
                    Formatted to pass Applicant Tracking Systems used by most companies
                  </p>
                </div>
              </div>
              <div className="flex gap-3">
                <Check className="h-6 w-6 text-green-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-semibold mb-1">Industry-Specific</h4>
                  <p className="text-sm text-muted-foreground">
                    Tailored language and keywords for your target role
                  </p>
                </div>
              </div>
              <div className="flex gap-3">
                <Check className="h-6 w-6 text-green-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-semibold mb-1">Instant Results</h4>
                  <p className="text-sm text-muted-foreground">
                    Get your optimized resume in minutes, not hours
                  </p>
                </div>
              </div>
              <div className="flex gap-3">
                <Check className="h-6 w-6 text-green-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-semibold mb-1">Free for Limited Time</h4>
                  <p className="text-sm text-muted-foreground">
                    Get your optimized resume completely free - limited time offer!
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA */}
        <div className="text-center">
          <Card className="border-2 border-primary/20 bg-primary/5">
            <CardContent className="p-12">
              <h2 className="text-3xl font-bold mb-4">Ready to Land Your Dream Job?</h2>
              <p className="text-lg text-muted-foreground mb-6">
                Start optimizing your resume now. No credit card required to preview.
              </p>
              <Button
                size="lg"
                className="text-lg px-8 py-6"
                onClick={handleStartOptimization}
                disabled={createResumeMutation.isPending}
              >
                {createResumeMutation.isPending ? "Starting..." : "Get Started Now"}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
