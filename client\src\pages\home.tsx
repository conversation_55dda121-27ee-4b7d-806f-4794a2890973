import { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import UserDashboard from "@/components/UserDashboard";
import { FileText, Plus, User, LogOut, Menu } from "lucide-react";
import { Link } from "wouter";
import type { Resume } from "@shared/schema";

export default function Home() {
  const { toast } = useToast();
  const { user, isAuthenticated, isLoading } = useAuth();
  const [showDashboard, setShowDashboard] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  const toDate = (value: unknown): Date | null => {
    if (!value) return null;
    if (value instanceof Date) return value;
    const parsed = new Date(value as string);
    return Number.isNaN(parsed.getTime()) ? null : parsed;
  };

  const formatDate = (value: Date | string | null | undefined) => {
    const date = toDate(value);
    if (!date) {
      return "Not available";
    }
    return date.toLocaleDateString();
  };


  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  // Fetch user's resumes
  const { data: resumes, isLoading: resumesLoading, error } = useQuery<Resume[]>({
    queryKey: ["/api/resumes"],
    enabled: isAuthenticated,
  });

  // Handle unauthorized errors
  useEffect(() => {
    if (error && isUnauthorizedError(error as Error)) {
      toast({
        title: "Unauthorized", 
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
    }
  }, [error, toast]);

  // Create new resume mutation
  const createResumeMutation = useMutation({
    mutationFn: async () => {
      return await apiRequest('POST', '/api/resumes', {
        title: `New Resume ${new Date().toLocaleDateString()}`,
        status: 'draft'
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/resumes'] });
      toast({
        title: "Success",
        description: "New resume created successfully!",
      });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to create new resume. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleLogout = () => {
    window.location.href = "/api/logout";
  };

  const handleCreateNewResume = () => {
    createResumeMutation.mutate();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <FileText className="h-4 w-4 text-primary-foreground" />
              </div>
              <span className="text-xl font-bold text-foreground">ResumeAI</span>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => setShowDashboard(true)}
                data-testid="button-dashboard"
              >
                <User className="h-4 w-4 mr-2" />
                Dashboard
              </Button>
              <Button 
                onClick={handleLogout}
                variant="ghost"
                data-testid="button-logout"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
              {user?.profileImageUrl && (
                <img
                  src={user.profileImageUrl}
                  alt="Profile"
                  className="w-8 h-8 rounded-full object-cover"
                />
              )}
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setShowMobileMenu(!showMobileMenu)}
              data-testid="button-mobile-menu"
            >
              <Menu className="h-5 w-5" />
            </Button>
          </div>

          {/* Mobile Menu */}
          {showMobileMenu && (
            <div className="md:hidden border-t border-border py-4">
              <div className="flex flex-col space-y-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDashboard(true);
                    setShowMobileMenu(false);
                  }}
                  className="justify-start"
                  data-testid="button-dashboard-mobile"
                >
                  <User className="h-4 w-4 mr-2" />
                  Dashboard
                </Button>
                <Button 
                  onClick={handleLogout}
                  variant="ghost"
                  className="justify-start"
                  data-testid="button-logout-mobile"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </Button>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Welcome Section */}
        <div className="mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Welcome back, {user?.firstName || "there"}! 👋
          </h1>
          <p className="text-xl text-muted-foreground">
            Ready to create your next winning resume?
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <Card className="border-2 border-dashed border-border hover:border-primary transition-colors cursor-pointer" onClick={handleCreateNewResume} data-testid="card-create-resume">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Plus className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Create New Resume</h3>
              <p className="text-sm text-muted-foreground">
                Build a tailored resume for your next opportunity
              </p>
            </CardContent>
          </Card>

          <Card className="border-border hover:shadow-lg transition-shadow">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Resume Templates</h3>
              <p className="text-sm text-muted-foreground">
                Choose from professional templates
              </p>
            </CardContent>
          </Card>

          <Card className="border-border hover:shadow-lg transition-shadow">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <User className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">My Account</h3>
              <p className="text-sm text-muted-foreground">
                Manage your profile and settings
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Resumes */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-foreground">Your Resumes</h2>
            <Button
              onClick={() => setShowDashboard(true)}
              variant="outline"
              data-testid="button-view-all"
            >
              View All
            </Button>
          </div>

          {resumesLoading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="border-border">
                  <CardContent className="p-6">
                    <div className="animate-pulse">
                      <div className="w-12 h-16 bg-gray-200 rounded mb-4"></div>
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded mb-4 w-2/3"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : resumes && resumes.length > 0 ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {resumes.slice(0, 6).map((resume) => (
                <Card key={resume.id} className="border-border hover:shadow-lg transition-shadow" data-testid={`card-resume-${resume.id}`}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="w-12 h-16 bg-gradient-to-b from-blue-100 to-blue-200 rounded border-2 border-blue-300 flex-shrink-0"></div>
                      <div className="text-xs text-muted-foreground">
                        {formatDate(resume.updatedAt ?? resume.createdAt)}
                      </div>
                    </div>
                    
                    <h3 className="font-semibold mb-2" data-testid={`text-resume-title-${resume.id}`}>{resume.title}</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      {resume.targetJobTitle || 'General Resume'}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                      <span className={`px-2 py-1 rounded-full ${
                        resume.status === 'complete' 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-yellow-100 text-yellow-700'
                      }`} data-testid={`status-${resume.id}`}>
                        {resume.status === 'complete' ? 'Complete' : 'Draft'}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Link href={`/resume-builder/${resume.id}`}>
                        <Button size="sm" className="flex-1" data-testid={`button-edit-${resume.id}`}>
                          Edit
                        </Button>
                      </Link>
                      <Button size="sm" variant="outline" data-testid={`button-download-${resume.id}`}>
                        PDF
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="border-border">
              <CardContent className="p-12 text-center">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">No resumes yet</h3>
                <p className="text-muted-foreground mb-6">
                  Create your first resume to get started
                </p>
                <Button onClick={handleCreateNewResume} data-testid="button-create-first-resume">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Resume
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </main>

      {/* User Dashboard Modal */}
      {showDashboard && (
        <UserDashboard
          onClose={() => setShowDashboard(false)}
          resumes={resumes || []}
        />
      )}
    </div>
  );
}
