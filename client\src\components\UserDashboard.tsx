import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  FileText, 
  Plus, 
  Edit, 
  Download, 
  Share, 
  MoreVertical, 
  X, 
  BarChart3, 
  Settings,
  CheckCircle,
  Edit3,
  Trash2
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { isUnauthorizedError } from "@/lib/authUtils";
import { Link } from "wouter";
import type { Resume } from "@shared/schema";

interface UserDashboardProps {
  onClose: () => void;
  resumes: Resume[];
}

export default function UserDashboard({ onClose, resumes }: UserDashboardProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("resumes");

  const toDate = (value: unknown): Date | null => {
    if (!value) return null;
    if (value instanceof Date) return value;
    const parsed = new Date(value as string);
    return Number.isNaN(parsed.getTime()) ? null : parsed;
  };


  // Delete resume mutation
  const deleteResumeMutation = useMutation({
    mutationFn: async (resumeId: string) => {
      return await apiRequest('DELETE', `/api/resumes/${resumeId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/resumes'] });
      toast({
        title: "Success",
        description: "Resume deleted successfully.",
      });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to delete resume. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Create new resume mutation
  const createResumeMutation = useMutation({
    mutationFn: async () => {
      return await apiRequest('POST', '/api/resumes', {
        title: `New Resume ${new Date().toLocaleDateString()}`,
        status: 'draft'
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/resumes'] });
      onClose();
      toast({
        title: "Success",
        description: "New resume created successfully!",
      });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to create new resume. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleDownloadResume = async (resumeId: string, title: string) => {
    try {
      const response = await fetch(`/api/resumes/${resumeId}/pdf`, {
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error('Failed to download resume');
      }
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${title.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      toast({
        title: "Success",
        description: "Resume downloaded successfully!",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to download resume. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteResume = (resumeId: string) => {
    if (window.confirm("Are you sure you want to delete this resume? This action cannot be undone.")) {
      deleteResumeMutation.mutate(resumeId);
    }
  };

  const handleCreateNewResume = () => {
    createResumeMutation.mutate();
  };

  const formatDate = (value: Date | string | null | undefined) => {
    const date = toDate(value);
    if (!date) {
      return "Not available";
    }
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getInitials = (firstName?: string | null, lastName?: string | null) => {
    if (!firstName && !lastName) return "U";
    return `${firstName?.[0] || ""}${lastName?.[0] || ""}`.toUpperCase();
  };

  const getRecentActivity = () => {
    const sorted = [...resumes].sort((a, b) => {
      const dateB = toDate(b.updatedAt) ?? toDate(b.createdAt);
      const dateA = toDate(a.updatedAt) ?? toDate(a.createdAt);
      return (dateB?.getTime() ?? 0) - (dateA?.getTime() ?? 0);
    });
    return sorted.slice(0, 5).map((resume) => ({
      id: resume.id,
      action: resume.status === "complete" ? "completed" : "updated",
      title: resume.title,
      date: toDate(resume.updatedAt) ?? toDate(resume.createdAt),
    }));
  };
  const avatarSrc: string | undefined = user?.profileImageUrl ?? undefined;


  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" data-testid="user-dashboard">
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="bg-card rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
          <div className="flex h-full max-h-[90vh]">
            {/* Sidebar */}
            <div className="w-64 bg-secondary/50 p-6 border-r border-border">
              <div className="flex items-center space-x-3 mb-8">
                <Avatar className="w-12 h-12">
                  <AvatarImage src={avatarSrc} alt="Profile" />
                  <AvatarFallback>
                    {getInitials(user?.firstName, user?.lastName)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-semibold" data-testid="user-name">
                    {user?.firstName} {user?.lastName}
                  </h3>
                  <p className="text-sm text-muted-foreground" data-testid="user-email">
                    {user?.email}
                  </p>
                </div>
              </div>
              <nav className="space-y-2">
                <button
                  onClick={() => setActiveTab("resumes")}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg w-full transition-colors ${
                    activeTab === "resumes" 
                      ? "bg-primary/10 text-primary font-medium" 
                      : "text-muted-foreground hover:bg-secondary"
                  }`}
                  data-testid="tab-resumes"
                >
                  <FileText className="h-4 w-4" />
                  <span>My Resumes</span>
                </button>
                <button
                  onClick={() => setActiveTab("analytics")}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg w-full transition-colors ${
                    activeTab === "analytics" 
                      ? "bg-primary/10 text-primary font-medium" 
                      : "text-muted-foreground hover:bg-secondary"
                  }`}
                  data-testid="tab-analytics"
                >
                  <BarChart3 className="h-4 w-4" />
                  <span>Analytics</span>
                </button>
                <button
                  onClick={() => setActiveTab("settings")}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg w-full transition-colors ${
                    activeTab === "settings" 
                      ? "bg-primary/10 text-primary font-medium" 
                      : "text-muted-foreground hover:bg-secondary"
                  }`}
                  data-testid="tab-settings"
                >
                  <Settings className="h-4 w-4" />
                  <span>Settings</span>
                </button>
              </nav>
            </div>
            {/* Main Content */}
            <div className="flex-1 p-6 overflow-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold">
                  {activeTab === "resumes" && "My Resumes"}
                  {activeTab === "analytics" && "Analytics"}
                  {activeTab === "settings" && "Settings"}
                </h2>
                <div className="flex items-center space-x-3">
                  {activeTab === "resumes" && (
                    <Button
                      onClick={handleCreateNewResume}
                      disabled={createResumeMutation.isPending}
                      data-testid="button-create-new-resume"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create New Resume
                    </Button>
                  )}
                  <Button variant="ghost" size="sm" onClick={onClose} data-testid="button-close-dashboard">
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              {/* Resumes Tab */}
              {activeTab === "resumes" && (
                <div className="space-y-6">
                  {resumes.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {resumes.map((resume) => (
                        <Card key={resume.id} className="border-border hover:shadow-lg transition-shadow" data-testid={`resume-card-${resume.id}`}>
                          <CardContent className="p-6">
                            <div className="flex items-start justify-between mb-4">
                              <div className="w-12 h-16 bg-gradient-to-b from-blue-100 to-blue-200 rounded border-2 border-blue-300 flex-shrink-0"></div>
                              <div className="relative">
                                <Button variant="ghost" size="sm" data-testid={`menu-${resume.id}`}>
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <h3 className="font-semibold mb-2" data-testid={`resume-title-${resume.id}`}>
                              {resume.title}
                            </h3>
                            <p className="text-sm text-muted-foreground mb-4">
                              {resume.targetJobTitle || 'General Resume'}
                              {resume.targetCompany && ` • ${resume.targetCompany}`}
                            </p>
                            <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                              <span>Updated: {formatDate(resume.updatedAt ?? resume.createdAt)}</span>
                              <Badge
                                variant={resume.status === 'complete' ? 'default' : 'secondary'}
                                className={resume.status === 'complete' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'}
                                data-testid={`status-${resume.id}`}
                              >
                                {resume.status === 'complete' ? 'Complete' : 'Draft'}
                              </Badge>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Link href={`/resume-builder/${resume.id}`}>
                                <Button size="sm" className="flex-1" data-testid={`button-edit-${resume.id}`}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </Button>
                              </Link>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDownloadResume(resume.id, resume.title)}
                                data-testid={`button-download-${resume.id}`}
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDeleteResume(resume.id)}
                                data-testid={`button-delete-${resume.id}`}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                      {/* Create New Resume Card */}
                      <Card className="border-2 border-dashed border-border hover:border-primary transition-colors cursor-pointer" onClick={handleCreateNewResume} data-testid="card-create-new">
                        <CardContent className="p-6 flex flex-col items-center justify-center text-center h-full min-h-[250px]">
                          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                            <Plus className="h-6 w-6 text-primary" />
                          </div>
                          <h3 className="font-semibold mb-2">Create New Resume</h3>
                          <p className="text-sm text-muted-foreground">
                            Build a tailored resume for your next opportunity
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                  ) : (
                    <Card className="border-border">
                      <CardContent className="p-12 text-center">
                        <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                          <FileText className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <h3 className="text-lg font-semibold mb-2">No resumes yet</h3>
                        <p className="text-muted-foreground mb-6">
                          Create your first resume to get started
                        </p>
                        <Button onClick={handleCreateNewResume} data-testid="button-create-first">
                          <Plus className="h-4 w-4 mr-2" />
                          Create Your First Resume
                        </Button>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}

              {/* Analytics Tab */}
              {activeTab === "analytics" && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm font-medium">Total Resumes</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{resumes.length}</div>
                        <p className="text-xs text-muted-foreground">
                          {resumes.filter(r => r.status === 'complete').length} completed
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm font-medium">This Month</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {resumes.filter((r) => {
                            const created = toDate(r.createdAt);
                            if (!created) {
                              return false;
                            }
                            const now = new Date();
                            return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear();
                          }).length}
                        </div>
                        <p className="text-xs text-muted-foreground">Resumes created</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {resumes.length > 0 ? Math.round((resumes.filter(r => r.status === 'complete').length / resumes.length) * 100) : 0}%
                        </div>
                        <p className="text-xs text-muted-foreground">Completion rate</p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Recent Activity */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Activity</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {getRecentActivity().length > 0 ? (
                        <div className="space-y-4">
                          {getRecentActivity().map((activity) => (
                            <div key={activity.id} className="flex items-center space-x-4">
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                activity.action === 'completed' ? 'bg-green-100' : 'bg-blue-100'
                              }`}>
                                {activity.action === 'completed' ? (
                                  <CheckCircle className="h-4 w-4 text-green-600" />
                                ) : (
                                  <Edit3 className="h-4 w-4 text-blue-600" />
                                )}
                              </div>
                              <div className="flex-1">
                                <p className="text-sm font-medium">
                                  {activity.action === 'completed' ? 'Completed' : 'Updated'} "{activity.title}"
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {formatDate(activity.date)}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground text-center py-8">
                          No recent activity
                        </p>
                      )}
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Settings Tab */}
              {activeTab === "settings" && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Account Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium">First Name</label>
                          <p className="text-sm text-muted-foreground">{user?.firstName || "Not provided"}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Last Name</label>
                          <p className="text-sm text-muted-foreground">{user?.lastName || "Not provided"}</p>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Email</label>
                        <p className="text-sm text-muted-foreground">{user?.email}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Member Since</label>
                        <p className="text-sm text-muted-foreground">
                          {user?.createdAt ? formatDate(user?.createdAt) : "Recently"}
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Preferences</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Account preferences and settings will be available in a future update.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
