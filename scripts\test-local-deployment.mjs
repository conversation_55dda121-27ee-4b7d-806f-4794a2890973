/**
 * Test script for ResumeAI local deployment using <PERSON>wright
 * This script tests:
 * 1. Landing page loads correctly
 * 2. Sign in button works
 * 3. Get Started button works
 */

import { chromium } from 'playwright';

// Simple expect function for testing
function expect(actual) {
  return {
    toContain: (expected) => {
      if (!actual.includes(expected)) {
        throw new Error(`Expected "${actual}" to contain "${expected}"`);
      }
    }
  };
}

async function testLocalDeployment() {
  console.log('🧪 Starting local deployment tests...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Test 1: Landing page loads correctly
    console.log('📄 Testing landing page...');
    await page.goto('http://localhost:5001');
    
    // Check if the page title is correct
    const title = await page.title();
    console.log(`Page title: ${title}`);
    
    // Check if the main heading is present
    const heading = await page.locator('h1').textContent();
    console.log(`Main heading: ${heading}`);
    
    // Check if the sign in button is present (get the first one)
    const signInButton = await page.locator('a[href*="/api/login"]').first().isVisible();
    console.log(`Sign in button visible: ${signInButton}`);
    
    // Check if the get started button is present (get the second one)
    const getStartedButton = await page.locator('a.cta-button').nth(1).textContent();
    console.log(`Get started button text: ${getStartedButton}`);
    
    // Check if feature cards are present
    const featureCards = await page.locator('.feature-card').count();
    console.log(`Number of feature cards: ${featureCards}`);
    
    // Take a screenshot
    await page.screenshot({ path: 'test-results/landing-page.png' });
    console.log('✅ Landing page test completed');
    
    // Test 2: Sign in button works
    console.log('🔐 Testing sign in button...');
    await page.click('a[href*="/api/login"]');
    
    // Wait for navigation
    await page.waitForTimeout(2000);
    
    // Take a screenshot
    await page.screenshot({ path: 'test-results/after-sign-in.png' });
    
    // Check the current URL
    const signInUrl = page.url();
    console.log(`URL after sign in: ${signInUrl}`);
    
    // Verify we're on the app page
    expect(signInUrl).toContain('/app.html');
    console.log('✅ Sign in button test completed');
    
    // Test 3: Get Started button works
    console.log('🚀 Testing get started button...');
    await page.goto('http://localhost:5001'); // Go back to landing page
    await page.click('a.cta-button:has-text("Get Started")');
    
    // Wait for navigation
    await page.waitForTimeout(2000);
    
    // Take a screenshot
    await page.screenshot({ path: 'test-results/after-get-started.png' });
    
    // Check the current URL
    const getStartedUrl = page.url();
    console.log(`URL after get started: ${getStartedUrl}`);
    
    // Verify we're on the app page
    expect(getStartedUrl).toContain('/app.html');
    console.log('✅ Get started button test completed');
    
    console.log('🎉 All tests completed successfully!');
  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Take a screenshot of the error state
    await page.screenshot({ path: 'test-results/error-state.png' });
  } finally {
    await browser.close();
  }
}

// Run the tests
testLocalDeployment().catch(console.error);