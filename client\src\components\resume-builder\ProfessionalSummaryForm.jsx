import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowR<PERSON>, ArrowLeft, Wand2, RefreshCw } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
export default function ProfessionalSummaryForm({ data, onDataChange, onNext, onPrevious, }) {
    const { toast } = useToast();
    const [summary, setSummary] = useState("");
    useEffect(() => {
        if (data.professionalSummary) {
            setSummary(data.professionalSummary);
        }
    }, [data]);
    const handleSummaryChange = (value) => {
        setSummary(value);
        onDataChange({ professionalSummary: value });
    };
    const generateSummaryMutation = useMutation({
        mutationFn: async () => {
            return await apiRequest("POST", "/api/ai/professional-summary", {
                personalInfo: data.personalInfo,
                experience: data.workExperience || [],
                skills: data.skills || [],
                targetJob: data.targetJobTitle,
                jobKeywords: data.extractedKeywords,
            });
        },
        onSuccess: ({ summary: generatedSummary }) => {
            setSummary(generatedSummary);
            onDataChange({ professionalSummary: generatedSummary });
            toast({
                title: "Success",
                description: "Professional summary generated successfully!",
            });
        },
        onError: (error) => {
            if (isUnauthorizedError(error)) {
                toast({
                    title: "Unauthorized",
                    description: "You are logged out. Logging in again...",
                    variant: "destructive",
                });
                setTimeout(() => {
                    window.location.href = "/api/login";
                }, 500);
                return;
            }
            toast({
                title: "Error",
                description: "Failed to generate summary. Please try again.",
                variant: "destructive",
            });
        },
    });
    const handleGenerateSummary = () => {
        if (!data.personalInfo) {
            toast({
                title: "Missing information",
                description: "Please complete your personal information first.",
                variant: "destructive",
            });
            return;
        }
        generateSummaryMutation.mutate();
    };
    const canGenerateAI = Boolean(data.personalInfo &&
        data.personalInfo.firstName &&
        data.personalInfo.lastName);
    const isValidForm = () => summary.trim().length > 50;
    const handleNext = () => {
        if (isValidForm()) {
            onNext();
        }
    };
    return (<div className="space-y-6">
      <div className="hidden lg:block">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Professional Summary</h1>
          <span className="text-sm text-muted-foreground bg-secondary px-3 py-1 rounded-full">
            Step 2 of 6
          </span>
        </div>
        <p className="text-muted-foreground">
          Write a compelling professional summary that highlights your key achievements and value proposition.
        </p>
      </div>

      <Card className="border-accent/20 bg-accent/5">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 mt-1">
              <Wand2 className="h-4 w-4 text-accent"/>
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-foreground">AI Summary Generator</h4>
              <p className="text-sm text-muted-foreground mt-1 mb-3">
                Let our AI create a compelling summary using your background, skills, and target role.
              </p>
              <Button onClick={handleGenerateSummary} disabled={!canGenerateAI || generateSummaryMutation.isPending} size="sm" variant="outline" data-testid="button-generate-summary">
                {generateSummaryMutation.isPending ? (<RefreshCw className="h-4 w-4 mr-2 animate-spin"/>) : (<Wand2 className="h-4 w-4 mr-2"/>)}
                {generateSummaryMutation.isPending ? "Generating..." : "Generate AI Summary"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="summary">Professional Summary *</Label>
          <Textarea id="summary" placeholder="Write a 3-4 sentence summary highlighting your professional experience, key skills, and career achievements. Focus on what makes you unique and valuable to potential employers..." value={summary} onChange={(e) => handleSummaryChange(e.target.value)} className="min-h-[150px] resize-vertical" data-testid="textarea-summary"/>
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Minimum 50 characters recommended</span>
            <span>{summary.length} characters</span>
          </div>
        </div>

        <Card>
          <CardContent className="p-4">
            <h4 className="text-sm font-medium mb-2">Writing Tips</h4>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>- Start with your years of experience and domain expertise</li>
              <li>- Highlight 2-3 achievements with quantifiable impact</li>
              <li>- Mention signature skills and tools relevant to your target role</li>
              <li>- Close with the value you bring to an employer</li>
              <li>- Keep it concise: 3-4 sentences or 50-100 words</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between items-center pt-6 border-t border-border">
        <Button variant="ghost" onClick={onPrevious} data-testid="button-previous">
          <ArrowLeft className="h-4 w-4 mr-2"/>
          Previous
        </Button>

        <Button onClick={handleNext} disabled={!isValidForm()} data-testid="button-next">
          Continue
          <ArrowRight className="h-4 w-4 ml-2"/>
        </Button>
      </div>
    </div>);
}
