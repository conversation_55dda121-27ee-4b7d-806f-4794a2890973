import { sql } from "drizzle-orm";
import {
  integer,
  sqliteTable,
  text,
} from "drizzle-orm/sqlite-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const sessions = sqliteTable("sessions", {
  sid: text("sid").primaryKey(),
  sess: text("sess", { mode: "json" }).notNull(),
  expire: integer("expire", { mode: "timestamp" }).notNull(),
});

export const users = sqliteTable("users", {
  id: text("id").primaryKey(),
  email: text("email").unique(),
  firstName: text("first_name"),
  lastName: text("last_name"),
  profileImageUrl: text("profile_image_url"),
  createdAt: integer("created_at", { mode: "timestamp" }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer("updated_at", { mode: "timestamp" }).default(sql`CURRENT_TIMESTAMP`),
});

export const resumes = sqliteTable("resumes", {
  id: text("id").primary<PERSON>ey(),
  userId: text("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  title: text("title").notNull(),
  templateId: text("template_id").default("modern"),
  industry: text("industry"),
  status: text("status").notNull().default("draft"),
  paymentStatus: text("payment_status").notNull().default("unpaid"),
  stripeSessionId: text("stripe_session_id"),
  personalInfo: text("personal_info", { mode: "json" }),
  professionalSummary: text("professional_summary"),
  workExperience: text("work_experience", { mode: "json" }),
  education: text("education", { mode: "json" }),
  skills: text("skills", { mode: "json" }),
  targetJobTitle: text("target_job_title"),
  targetCompany: text("target_company"),
  jobDescription: text("job_description"),
  jobUrl: text("job_url"),
  extractedKeywords: text("extracted_keywords", { mode: "json" }),
  aiEnhanced: integer("ai_enhanced", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer("updated_at", { mode: "timestamp" }).default(sql`CURRENT_TIMESTAMP`),
});

export const jobAnalyses = sqliteTable("job_analyses", {
  id: text("id").primaryKey(),
  resumeId: text("resume_id").notNull().references(() => resumes.id, { onDelete: "cascade" }),
  jobDescription: text("job_description").notNull(),
  extractedKeywords: text("extracted_keywords", { mode: "json" }),
  requirements: text("requirements", { mode: "json" }),
  suggestedImprovements: text("suggested_improvements", { mode: "json" }),
  matchScore: text("match_score"),
  createdAt: integer("created_at", { mode: "timestamp" }).default(sql`CURRENT_TIMESTAMP`),
});

export const usersRelations = relations(users, ({ many }) => ({
  resumes: many(resumes),
}));

export const resumesRelations = relations(resumes, ({ one, many }) => ({
  user: one(users, {
    fields: [resumes.userId],
    references: [users.id],
  }),
  jobAnalyses: many(jobAnalyses),
}));

export const jobAnalysesRelations = relations(jobAnalyses, ({ one }) => ({
  resume: one(resumes, {
    fields: [jobAnalyses.resumeId],
    references: [resumes.id],
  }),
}));

export const insertUserSchema = createInsertSchema(users).omit({
  createdAt: true,
  updatedAt: true,
});

export const insertResumeSchema = createInsertSchema(resumes).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  stripeSessionId: true,
});

export const updateResumeSchema = insertResumeSchema.partial().extend({
  id: z.string(),
});

export const insertJobAnalysisSchema = createInsertSchema(jobAnalyses).omit({
  id: true,
  createdAt: true,
});

export type UpsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertResume = z.infer<typeof insertResumeSchema>;
export type UpdateResume = z.infer<typeof updateResumeSchema>;
export type Resume = typeof resumes.$inferSelect;
export type InsertJobAnalysis = z.infer<typeof insertJobAnalysisSchema>;
export type JobAnalysis = typeof jobAnalyses.$inferSelect;

export type PersonalInfo = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  linkedinUrl?: string;
};

export type WorkExperience = {
  id: string;
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  description: string;
  achievements: string[];
};

export type Education = {
  id: string;
  institution: string;
  degree: string;
  field: string;
  graduationDate: string;
  gpa?: string;
};

export type Skill = {
  id: string;
  name: string;
  level: string;
  category: string;
};
