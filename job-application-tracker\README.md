# My Job Application Tracker

A personal job application tracking website inspired by Trackplicant.com. This project helps you organize, track, and manage your job applications in one place.

## Features

- **Application Tracking**: Keep track of all your job applications with status updates, deadlines, and follow-up reminders
- **Analytics & Insights**: Get detailed analytics about your job search progress
- **Smart Reminders**: Never miss a follow-up with intelligent reminders
- **Document Management**: Store and organize your resumes, cover letters, and other job search documents
- **AI-Powered Insights**: Get personalized recommendations to improve your resume and interview performance
- **Networking Tools**: Manage your professional contacts and networking opportunities

## Technologies Used

- **HTML5**: Semantic markup for accessibility and SEO
- **Tailwind CSS**: Utility-first CSS framework for styling
- **Vanilla JavaScript**: For interactivity and dynamic content
- **Responsive Design**: Mobile-first approach for all screen sizes

## Getting Started

1. Clone or download this repository
2. Open the `index.html` file in your web browser
3. Start tracking your job applications!

No registration or setup required - everything runs locally in your browser.

## Project Structure

```
job-application-tracker/
├── index.html          # Main HTML file
├── styles.css          # Custom CSS styles
├── script.js           # JavaScript functionality
└── README.md           # Project documentation
```

## Customization

### Personalizing Your Tracker

This is your personal job application tracker. Here are some ways to make it your own:

#### Changing Colors

You can customize the color scheme by modifying the CSS variables in `styles.css`:

```css
:root {
    --primary-color: #4f46e5;
    --secondary-color: #f3f4f6;
    --accent-color: #6366f1;
    /* Add more color variables as needed */
}
```

### Adding New Features

1. Add new HTML elements to `index.html`
2. Style them in `styles.css`
3. Add functionality in `script.js`

### Customizing Content

1. Update the title and branding in the header
2. Modify the hero section text to match your personal style
3. Customize the features to highlight what's most important to you
4. Add your own job application examples in the dashboard preview

## Browser Support

This website supports all modern browsers:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Possible Enhancements

- [ ] Local storage to persist your data between sessions
- [ ] Export/import functionality for your application data
- [ ] Calendar integration for interview reminders
- [ ] Advanced analytics and reporting
- [ ] Mobile app version
- [ ] Integration with job boards like LinkedIn, Indeed, etc.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this project.

## License

This project is open source and available under the [MIT License](LICENSE).

## Acknowledgments

- Inspired by [Trackplicant.com](https://trackplicant.com/)
- Built with [Tailwind CSS](https://tailwindcss.com/)
- Icons from [Heroicons](https://heroicons.com/)

## Privacy Notice

This is a personal job application tracker that runs entirely in your browser. No data is sent to any external servers, ensuring your job search information remains private.