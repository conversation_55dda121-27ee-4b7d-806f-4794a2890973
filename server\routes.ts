import type { Express } from "express";
import { createServer, type Server } from "http";
import multer from "multer";
import { z } from "zod";
import { storage } from "./storage.js";
import { setupAuth, isAuthenticated } from "./googleAuth.js";
import {
  analyzeJobDescription,
  generateProfessionalSummary,
  enhanceExperienceDescription,
  suggestSkillsForJob,
  generateResumeFromBrief,
  synthesizeResumeWithExisting,
  type ResumeDraft,
} from "./services/openai.js";
import { parseUploadedResume, normalizeResumeDraft } from "./services/resumeParser.js";
import { scrapeJobFromUrl } from "./services/jobScraper.js";
import { generateResumePDF } from "./services/pdfGenerator.js";
import {
  createPaymentIntent,
  createCheckoutSession,
  handleWebhookEvent,
  checkPaymentStatus,
  refundPayment
} from "./services/stripe.js";
import Stripe from 'stripe';

import {
  insertResumeSchema,
  updateResumeSchema,
  insertJobAnalysisSchema,
  type PersonalInfo,
  type WorkExperience,
  type Skill,
} from "@shared/schema";

// Initialize Stripe for webhook handling (reusing the stripe instance from stripe service)
import { stripe } from "./services/stripe.js";

const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 8 * 1024 * 1024 },
});

const resumeBriefSchema = z.object({
  resumeId: z.string().uuid().optional(),
  role: z.string().min(2),
  yearsExperience: z.coerce.number().int().nonnegative().max(60),
  topSkills: z.array(z.string()).max(20).default([]),
  accomplishments: z.array(z.string()).max(10).default([]),
  industry: z.string().optional(),
  merge: z.boolean().optional().default(true),
});

function buildResumeUpdatePayload(id: string, draft: ResumeDraft) {
  return {
    id,
    personalInfo: draft.personalInfo,
    professionalSummary: draft.professionalSummary,
    workExperience: draft.workExperience,
    education: draft.education,
    skills: draft.skills,
    targetJobTitle: draft.targetJobTitle,
    industry: draft.industry,
  };
}

export async function registerRoutes(app: Express): Promise<Server> {
  setupAuth(app);

  // Health check endpoint
  app.get("/api/health", (req, res) => {
    res.json({ status: "ok", timestamp: new Date().toISOString() });
  });

  app.get("/api/auth/user", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const user = await storage.getUser(userId);
      res.json(user ?? {});
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  app.get("/api/resumes", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const resumes = await storage.getUserResumes(userId);
      res.json(resumes);
    } catch (error) {
      console.error("Error fetching resumes:", error);
      res.status(500).json({ message: "Failed to fetch resumes" });
    }
  });

  app.get("/api/resumes/:id", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const resumeId = req.params.id;
      const resume = await storage.getResume(resumeId, userId);

      if (!resume) {
        return res.status(404).json({ message: "Resume not found" });
      }

      res.json(resume);
    } catch (error) {
      console.error("Error fetching resume:", error);
      res.status(500).json({ message: "Failed to fetch resume" });
    }
  });

  app.post("/api/resumes", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const resumeData = insertResumeSchema.parse({
        status: "draft",
        templateId: "modern",
        paymentStatus: "unpaid",
        ...req.body,
        userId,
      });

      const resume = await storage.createResume(resumeData);
      res.json(resume);
    } catch (error) {
      console.error("Error creating resume:", error);
      res.status(400).json({ message: "Failed to create resume" });
    }
  });

  app.put("/api/resumes/:id", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const resumeId = req.params.id;
      const existingResume = await storage.getResume(resumeId, userId);
      if (!existingResume) {
        return res.status(404).json({ message: "Resume not found" });
      }

      const resumeData = updateResumeSchema.parse({
        ...req.body,
        id: resumeId,
      });

      const updatedResume = await storage.updateResume(resumeData);
      res.json(updatedResume);
    } catch (error) {
      console.error("Error updating resume:", error);
      res.status(400).json({ message: "Failed to update resume" });
    }
  });

  app.delete("/api/resumes/:id", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const resumeId = req.params.id;
      await storage.deleteResume(resumeId, userId);
      res.json({ message: "Resume deleted successfully" });
    } catch (error) {
      console.error("Error deleting resume:", error);
      res.status(500).json({ message: "Failed to delete resume" });
    }
  });

  app.post("/api/resumes/upload", isAuthenticated, upload.single("resume"), async (req: any, res) => {
    try {
      const userId = req.user.id;
      const resumeId = req.body.resumeId as string | undefined;
      if (!req.file) {
        return res.status(400).json({ message: "Resume file is required" });
      }

      const draft = await parseUploadedResume(req.file);
      let normalized = normalizeResumeDraft(draft);

      if (resumeId) {
        const existing = await storage.getResume(resumeId, userId);
        if (!existing) {
          return res.status(404).json({ message: "Resume not found" });
        }
        const merged = synthesizeResumeWithExisting(existing, normalized);
        normalized = normalizeResumeDraft(merged);
        await storage.updateResume(buildResumeUpdatePayload(resumeId, normalized));
      }

      res.json({ draft: normalized });
    } catch (error) {
      console.error("Error parsing resume upload:", error);
      res.status(400).json({ message: (error as Error).message || "Failed to parse resume" });
    }
  });

  app.post("/api/ai/resume-brief", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const payload = resumeBriefSchema.parse(req.body);

      const draft = await generateResumeFromBrief(payload);
      let normalized = normalizeResumeDraft(draft);

      if (payload.resumeId) {
        const existing = await storage.getResume(payload.resumeId, userId);
        if (!existing) {
          return res.status(404).json({ message: "Resume not found" });
        }
        const merged = payload.merge ? synthesizeResumeWithExisting(existing, normalized) : normalized;
        normalized = normalizeResumeDraft(merged);
        await storage.updateResume(buildResumeUpdatePayload(payload.resumeId, normalized));
      }

      res.json({ draft: normalized });
    } catch (error) {
      console.error("Error generating resume from brief:", error);
      res.status(400).json({ message: "Failed to generate resume draft" });
    }
  });

  app.post("/api/ai/professional-summary", isAuthenticated, async (req, res) => {
    try {
      const { personalInfo, experience, skills, targetJob, jobKeywords } = req.body;
      const summary = await generateProfessionalSummary(
        personalInfo as PersonalInfo,
        experience as WorkExperience[],
        skills as Skill[],
        targetJob,
        jobKeywords
      );
      res.json({ summary });
    } catch (error) {
      console.error("Error generating professional summary:", error);
      res.status(500).json({ message: "Failed to generate professional summary" });
    }
  });

  app.post("/api/ai/enhance-experience", isAuthenticated, async (req, res) => {
    try {
      const { experience, jobKeywords } = req.body;
      const enhancedDescription = await enhanceExperienceDescription(
        experience as WorkExperience,
        jobKeywords
      );
      res.json({ enhancedDescription });
    } catch (error) {
      console.error("Error enhancing experience:", error);
      res.status(500).json({ message: "Failed to enhance experience description" });
    }
  });

  app.post("/api/jobs/analyze", isAuthenticated, async (req: any, res) => {
    try {
      const { jobDescription, resumeId } = req.body;
      const userId = req.user.id;

      if (!jobDescription) {
        return res.status(400).json({ message: "Job description is required" });
      }

      if (resumeId) {
        const resume = await storage.getResume(resumeId, userId);
        if (!resume) {
          return res.status(404).json({ message: "Resume not found" });
        }
      }

      const analysis = await analyzeJobDescription(jobDescription);

      if (resumeId) {
        const analysisData = insertJobAnalysisSchema.parse({
          resumeId,
          jobDescription,
          extractedKeywords: analysis.keywords,
          requirements: analysis.requirements,
          suggestedImprovements: analysis.suggestedImprovements,
          matchScore: analysis.matchScore,
        });
        await storage.createJobAnalysis(analysisData);
      }

      res.json(analysis);
    } catch (error) {
      console.error("Error analyzing job description:", error);
      res.status(500).json({ message: "Failed to analyze job description" });
    }
  });

  app.post("/api/jobs/scrape", isAuthenticated, async (req, res) => {
    try {
      const { url } = req.body;
      if (!url) {
        return res.status(400).json({ message: "URL is required" });
      }
      const scrapedData = await scrapeJobFromUrl(url);
      res.json(scrapedData);
    } catch (error) {
      console.error("Error scraping job URL:", error);
      res.status(500).json({ message: "Failed to scrape job posting" });
    }
  });

  app.post("/api/ai/suggest-skills", isAuthenticated, async (req, res) => {
    try {
      const { jobDescription, currentSkills } = req.body;
      const suggestedSkills = await suggestSkillsForJob(
        jobDescription,
        currentSkills || []
      );
      res.json({ suggestedSkills });
    } catch (error) {
      console.error("Error suggesting skills:", error);
      res.status(500).json({ message: "Failed to suggest skills" });
    }
  });

  app.get("/api/resumes/:id/pdf", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const resumeId = req.params.id;
      const resume = await storage.getResume(resumeId, userId);
      if (!resume) {
        return res.status(404).json({ message: "Resume not found" });
      }

      // Check if payment is required and if it's been paid
      if (resume.paymentStatus !== 'paid') {
        return res.status(402).json({
          message: "Payment required",
          paymentRequired: true,
          amount: resume.paymentAmount || 199,
          currency: resume.paymentCurrency || 'usd'
        });
      }

      const pdfBuffer = await generateResumePDF(resume);
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader("Content-Disposition", `attachment; filename=\"${resume.title}.pdf\"`);
      res.send(pdfBuffer);
    } catch (error) {
      console.error("Error generating PDF:", error);
      res.status(500).json({ message: "Failed to generate PDF" });
    }
  });

  // Payment endpoints
  app.post("/api/payments/create-payment-intent", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { resumeId } = req.body;
      
      if (!resumeId) {
        return res.status(400).json({ message: "Resume ID is required" });
      }

      const result = await createPaymentIntent({ resumeId, userId });
      res.json(result);
    } catch (error) {
      console.error("Error creating payment intent:", error);
      res.status(500).json({ message: "Failed to create payment intent" });
    }
  });

  app.post("/api/payments/create-checkout-session", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { resumeId, successUrl, cancelUrl } = req.body;
      
      if (!resumeId || !successUrl || !cancelUrl) {
        return res.status(400).json({
          message: "Resume ID, success URL, and cancel URL are required"
        });
      }

      const result = await createCheckoutSession({
        resumeId,
        userId,
        successUrl,
        cancelUrl
      });
      res.json(result);
    } catch (error) {
      console.error("Error creating checkout session:", error);
      res.status(500).json({ message: "Failed to create checkout session" });
    }
  });

  app.get("/api/payments/status/:resumeId", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { resumeId } = req.params;
      
      const isPaid = await checkPaymentStatus(resumeId, userId);
      res.json({ paid: isPaid });
    } catch (error) {
      console.error("Error checking payment status:", error);
      res.status(500).json({ message: "Failed to check payment status" });
    }
  });

  // Stripe webhook endpoint
  app.post("/api/webhooks/stripe", async (req, res) => {
    const sig = req.headers['stripe-signature'];
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!sig || !webhookSecret) {
      return res.status(400).json({ message: "Stripe signature or webhook secret missing" });
    }

    try {
      const event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
      await handleWebhookEvent(event);
      res.json({ received: true });
    } catch (error) {
      console.error("Webhook signature verification failed:", error);
      return res.status(400).json({ message: "Webhook signature verification failed" });
    }
  });

  // Invoice API endpoints
  // Note: For now, these will use in-memory storage
  // In production, you should add database tables for invoices
  const invoiceStore = new Map<string, any>();

  // Get all invoices for user
  app.get("/api/invoices", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const userInvoices = Array.from(invoiceStore.values()).filter(
        (invoice) => invoice.userId === userId
      );
      res.json(userInvoices);
    } catch (error) {
      console.error("Error fetching invoices:", error);
      res.status(500).json({ message: "Failed to fetch invoices" });
    }
  });

  // Get single invoice
  app.get("/api/invoices/:id", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { id } = req.params;
      const invoice = invoiceStore.get(id);

      if (!invoice || invoice.userId !== userId) {
        return res.status(404).json({ message: "Invoice not found" });
      }

      res.json(invoice);
    } catch (error) {
      console.error("Error fetching invoice:", error);
      res.status(500).json({ message: "Failed to fetch invoice" });
    }
  });

  // Create invoice
  app.post("/api/invoices", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const invoiceData = {
        ...req.body,
        id: crypto.randomUUID(),
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      invoiceStore.set(invoiceData.id, invoiceData);
      res.status(201).json(invoiceData);
    } catch (error) {
      console.error("Error creating invoice:", error);
      res.status(500).json({ message: "Failed to create invoice" });
    }
  });

  // Update invoice
  app.put("/api/invoices/:id", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { id } = req.params;
      const existingInvoice = invoiceStore.get(id);

      if (!existingInvoice || existingInvoice.userId !== userId) {
        return res.status(404).json({ message: "Invoice not found" });
      }

      const updatedInvoice = {
        ...existingInvoice,
        ...req.body,
        id,
        userId,
        updatedAt: new Date(),
      };

      invoiceStore.set(id, updatedInvoice);
      res.json(updatedInvoice);
    } catch (error) {
      console.error("Error updating invoice:", error);
      res.status(500).json({ message: "Failed to update invoice" });
    }
  });

  // Delete invoice
  app.delete("/api/invoices/:id", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { id } = req.params;
      const invoice = invoiceStore.get(id);

      if (!invoice || invoice.userId !== userId) {
        return res.status(404).json({ message: "Invoice not found" });
      }

      invoiceStore.delete(id);
      res.json({ message: "Invoice deleted successfully" });
    } catch (error) {
      console.error("Error deleting invoice:", error);
      res.status(500).json({ message: "Failed to delete invoice" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
