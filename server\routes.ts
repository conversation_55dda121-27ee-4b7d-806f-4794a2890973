import type { Express } from "express";
import { createServer, type Server } from "http";
import multer from "multer";
import { z } from "zod";
import { storage } from "./storage";
import { setupAuth, isAuthenticated } from "./replitAuth";
import {
  analyzeJobDescription,
  generateProfessionalSummary,
  enhanceExperienceDescription,
  suggestSkillsForJob,
  generateResumeFromBrief,
  synthesizeResumeWithExisting,
  type ResumeDraft,
} from "./services/openai";
import { parseUploadedResume, normalizeResumeDraft } from "./services/resumeParser";
import { scrapeJobFromUrl } from "./services/jobScraper";
import { generateResumePDF } from "./services/pdfGenerator";
import {
  insertResumeSchema,
  updateResumeSchema,
  insertJobAnalysisSchema,
  type PersonalInfo,
  type WorkExperience,
  type Skill,
} from "@shared/schema";

const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 8 * 1024 * 1024 },
});

const resumeBriefSchema = z.object({
  resumeId: z.string().uuid().optional(),
  role: z.string().min(2),
  yearsExperience: z.coerce.number().int().nonnegative().max(60),
  topSkills: z.array(z.string()).max(20).default([]),
  accomplishments: z.array(z.string()).max(10).default([]),
  industry: z.string().optional(),
  merge: z.boolean().optional().default(true),
});

function buildResumeUpdatePayload(id: string, draft: ResumeDraft) {
  return {
    id,
    personalInfo: draft.personalInfo,
    professionalSummary: draft.professionalSummary,
    workExperience: draft.workExperience,
    education: draft.education,
    skills: draft.skills,
    targetJobTitle: draft.targetJobTitle,
    industry: draft.industry,
  };
}

export async function registerRoutes(app: Express): Promise<Server> {
  await setupAuth(app);

  app.get("/api/auth/user", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      res.json(user ?? {});
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  app.get("/api/resumes", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const resumes = await storage.getUserResumes(userId);
      res.json(resumes);
    } catch (error) {
      console.error("Error fetching resumes:", error);
      res.status(500).json({ message: "Failed to fetch resumes" });
    }
  });

  app.get("/api/resumes/:id", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const resumeId = req.params.id;
      const resume = await storage.getResume(resumeId, userId);

      if (!resume) {
        return res.status(404).json({ message: "Resume not found" });
      }

      res.json(resume);
    } catch (error) {
      console.error("Error fetching resume:", error);
      res.status(500).json({ message: "Failed to fetch resume" });
    }
  });

  app.post("/api/resumes", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const resumeData = insertResumeSchema.parse({
        status: "draft",
        templateId: "modern",
        paymentStatus: "unpaid",
        ...req.body,
        userId,
      });

      const resume = await storage.createResume(resumeData);
      res.json(resume);
    } catch (error) {
      console.error("Error creating resume:", error);
      res.status(400).json({ message: "Failed to create resume" });
    }
  });

  app.put("/api/resumes/:id", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const resumeId = req.params.id;
      const existingResume = await storage.getResume(resumeId, userId);
      if (!existingResume) {
        return res.status(404).json({ message: "Resume not found" });
      }

      const resumeData = updateResumeSchema.parse({
        ...req.body,
        id: resumeId,
      });

      const updatedResume = await storage.updateResume(resumeData);
      res.json(updatedResume);
    } catch (error) {
      console.error("Error updating resume:", error);
      res.status(400).json({ message: "Failed to update resume" });
    }
  });

  app.delete("/api/resumes/:id", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const resumeId = req.params.id;
      await storage.deleteResume(resumeId, userId);
      res.json({ message: "Resume deleted successfully" });
    } catch (error) {
      console.error("Error deleting resume:", error);
      res.status(500).json({ message: "Failed to delete resume" });
    }
  });

  app.post("/api/resumes/upload", isAuthenticated, upload.single("resume"), async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const resumeId = req.body.resumeId as string | undefined;
      if (!req.file) {
        return res.status(400).json({ message: "Resume file is required" });
      }

      const draft = await parseUploadedResume(req.file);
      let normalized = normalizeResumeDraft(draft);

      if (resumeId) {
        const existing = await storage.getResume(resumeId, userId);
        if (!existing) {
          return res.status(404).json({ message: "Resume not found" });
        }
        const merged = synthesizeResumeWithExisting(existing, normalized);
        normalized = normalizeResumeDraft(merged);
        await storage.updateResume(buildResumeUpdatePayload(resumeId, normalized));
      }

      res.json({ draft: normalized });
    } catch (error) {
      console.error("Error parsing resume upload:", error);
      res.status(400).json({ message: (error as Error).message || "Failed to parse resume" });
    }
  });

  app.post("/api/ai/resume-brief", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const payload = resumeBriefSchema.parse(req.body);

      const draft = await generateResumeFromBrief(payload);
      let normalized = normalizeResumeDraft(draft);

      if (payload.resumeId) {
        const existing = await storage.getResume(payload.resumeId, userId);
        if (!existing) {
          return res.status(404).json({ message: "Resume not found" });
        }
        const merged = payload.merge ? synthesizeResumeWithExisting(existing, normalized) : normalized;
        normalized = normalizeResumeDraft(merged);
        await storage.updateResume(buildResumeUpdatePayload(payload.resumeId, normalized));
      }

      res.json({ draft: normalized });
    } catch (error) {
      console.error("Error generating resume from brief:", error);
      res.status(400).json({ message: "Failed to generate resume draft" });
    }
  });

  app.post("/api/ai/professional-summary", isAuthenticated, async (req, res) => {
    try {
      const { personalInfo, experience, skills, targetJob, jobKeywords } = req.body;
      const summary = await generateProfessionalSummary(
        personalInfo as PersonalInfo,
        experience as WorkExperience[],
        skills as Skill[],
        targetJob,
        jobKeywords
      );
      res.json({ summary });
    } catch (error) {
      console.error("Error generating professional summary:", error);
      res.status(500).json({ message: "Failed to generate professional summary" });
    }
  });

  app.post("/api/ai/enhance-experience", isAuthenticated, async (req, res) => {
    try {
      const { experience, jobKeywords } = req.body;
      const enhancedDescription = await enhanceExperienceDescription(
        experience as WorkExperience,
        jobKeywords
      );
      res.json({ enhancedDescription });
    } catch (error) {
      console.error("Error enhancing experience:", error);
      res.status(500).json({ message: "Failed to enhance experience description" });
    }
  });

  app.post("/api/jobs/analyze", isAuthenticated, async (req: any, res) => {
    try {
      const { jobDescription, resumeId } = req.body;
      const userId = req.user.claims.sub;

      if (!jobDescription) {
        return res.status(400).json({ message: "Job description is required" });
      }

      if (resumeId) {
        const resume = await storage.getResume(resumeId, userId);
        if (!resume) {
          return res.status(404).json({ message: "Resume not found" });
        }
      }

      const analysis = await analyzeJobDescription(jobDescription);

      if (resumeId) {
        const analysisData = insertJobAnalysisSchema.parse({
          resumeId,
          jobDescription,
          extractedKeywords: analysis.keywords,
          requirements: analysis.requirements,
          suggestedImprovements: analysis.suggestedImprovements,
          matchScore: analysis.matchScore,
        });
        await storage.createJobAnalysis(analysisData);
      }

      res.json(analysis);
    } catch (error) {
      console.error("Error analyzing job description:", error);
      res.status(500).json({ message: "Failed to analyze job description" });
    }
  });

  app.post("/api/jobs/scrape", isAuthenticated, async (req, res) => {
    try {
      const { url } = req.body;
      if (!url) {
        return res.status(400).json({ message: "URL is required" });
      }
      const scrapedData = await scrapeJobFromUrl(url);
      res.json(scrapedData);
    } catch (error) {
      console.error("Error scraping job URL:", error);
      res.status(500).json({ message: "Failed to scrape job posting" });
    }
  });

  app.post("/api/ai/suggest-skills", isAuthenticated, async (req, res) => {
    try {
      const { jobDescription, currentSkills } = req.body;
      const suggestedSkills = await suggestSkillsForJob(
        jobDescription,
        currentSkills || []
      );
      res.json({ suggestedSkills });
    } catch (error) {
      console.error("Error suggesting skills:", error);
      res.status(500).json({ message: "Failed to suggest skills" });
    }
  });

  app.get("/api/resumes/:id/pdf", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const resumeId = req.params.id;
      const resume = await storage.getResume(resumeId, userId);
      if (!resume) {
        return res.status(404).json({ message: "Resume not found" });
      }

      const pdfBuffer = await generateResumePDF(resume);
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader("Content-Disposition", `attachment; filename=\"${resume.title}.pdf\"`);
      res.send(pdfBuffer);
    } catch (error) {
      console.error("Error generating PDF:", error);
      res.status(500).json({ message: "Failed to generate PDF" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
