/* Micro-interactions CSS Styles */

/* Hover Effect */
.mi-hover-effect {
  transition: all 0.3s ease-out;
}

.mi-hover-effect.hovered {
  transform: scale(var(--mi-scale, 1.05));
  border-radius: var(--mi-border-radius, 0.5rem);
}

.mi-hover-effect.hovered.shadow {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Tilt Effect */
.mi-tilt-effect {
  position: relative;
  transition: transform 0.2s ease-out;
  transform-style: preserve-3d;
}

.mi-tilt-glare {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

/* Magnetic Effect */
.mi-magnetic-effect {
  transition: transform 0.3s ease-out;
}

/* Ripple Effect */
.mi-ripple-effect {
  position: relative;
  overflow: hidden;
}

.mi-ripple {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  background-color: var(--mi-ripple-color, rgba(255, 255, 255, 0.6));
  animation: mi-ripple-animation var(--mi-duration, 600ms) ease-out forwards;
}

@keyframes mi-ripple-animation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* Typewriter Effect */
.mi-typewriter-cursor {
  opacity: 1;
  transition: opacity 0.2s;
}

.mi-typewriter-cursor.hidden {
  opacity: 0;
}

/* Floating Element */
.mi-floating-element {
  transition: transform var(--mi-duration, 3000ms) ease-in-out;
  animation: mi-float-animation var(--mi-duration, 3000ms) ease-in-out infinite;
  animation-delay: var(--mi-delay, 0ms);
}

@keyframes mi-float-animation {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(var(--mi-distance, 10px));
  }
}

/* Pulse Effect */
.mi-pulse-effect {
  transition: transform 0.1s ease-out;
}

/* Staggered Animation */
.mi-staggered-container {
  position: relative;
}

.mi-staggered-item {
  transition: all var(--mi-duration, 500ms) ease-out;
  opacity: 0;
  transform: var(--mi-transform, translateY(20px));
}

.mi-staggered-item.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Animation delays for staggered effect */
.mi-staggered-item:nth-child(1) { animation-delay: calc(var(--mi-stagger, 100ms) * 1); }
.mi-staggered-item:nth-child(2) { animation-delay: calc(var(--mi-stagger, 100ms) * 2); }
.mi-staggered-item:nth-child(3) { animation-delay: calc(var(--mi-stagger, 100ms) * 3); }
.mi-staggered-item:nth-child(4) { animation-delay: calc(var(--mi-stagger, 100ms) * 4); }
.mi-staggered-item:nth-child(5) { animation-delay: calc(var(--mi-stagger, 100ms) * 5); }
.mi-staggered-item:nth-child(6) { animation-delay: calc(var(--mi-stagger, 100ms) * 6); }
.mi-staggered-item:nth-child(7) { animation-delay: calc(var(--mi-stagger, 100ms) * 7); }
.mi-staggered-item:nth-child(8) { animation-delay: calc(var(--mi-stagger, 100ms) * 8); }
.mi-staggered-item:nth-child(9) { animation-delay: calc(var(--mi-stagger, 100ms) * 9); }
.mi-staggered-item:nth-child(10) { animation-delay: calc(var(--mi-stagger, 100ms) * 10); }

/* Direction variants */
.mi-staggered-up { --mi-transform: translateY(20px); }
.mi-staggered-down { --mi-transform: translateY(-20px); }
.mi-staggered-left { --mi-transform: translateX(20px); }
.mi-staggered-right { --mi-transform: translateX(-20px); }
.mi-staggered-scale { --mi-transform: scale(0.8); }
.mi-staggered-fade { --mi-transform: none; opacity: 0; }

/* Parallax Section */
.mi-parallax-section {
  position: relative;
  overflow: hidden;
  transform: translateY(var(--mi-parallax-offset, 0px));
}

/* Responsive adjustments */
@media (prefers-reduced-motion: reduce) {
  .mi-hover-effect,
  .mi-tilt-effect,
  .mi-magnetic-effect,
  .mi-floating-element,
  .mi-pulse-effect,
  .mi-staggered-item,
  .mi-parallax-section {
    transition: none;
    animation: none;
  }
}