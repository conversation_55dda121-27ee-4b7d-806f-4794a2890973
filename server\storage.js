import { randomUUID } from "node:crypto";
import { users, resumes, jobAnalyses, } from "@shared/schema";
import { db } from "./db.js";
import { eq, desc, and } from "drizzle-orm";
function stripUndefined(input) {
    return Object.fromEntries(Object.entries(input).filter(([, value]) => value !== undefined));
}
export class DatabaseStorage {
    async getUser(id) {
        const [user] = await db.select().from(users).where(eq(users.id, id));
        return user;
    }
    async upsertUser(userData) {
        const [user] = await db
            .insert(users)
            .values({ ...userData, id: userData.id ?? randomUUID() })
            .onConflictDoUpdate({
            target: users.id,
            set: {
                ...userData,
                updatedAt: new Date(),
            },
        })
            .returning();
        return user;
    }
    async getUserResumes(userId) {
        return await db
            .select()
            .from(resumes)
            .where(eq(resumes.userId, userId))
            .orderBy(desc(resumes.updatedAt));
    }
    async getResume(id, userId) {
        const [resume] = await db
            .select()
            .from(resumes)
            .where(and(eq(resumes.id, id), eq(resumes.userId, userId)));
        return resume;
    }
    async createResume(resume) {
        const timestamp = new Date();
        const [newResume] = await db
            .insert(resumes)
            .values({
            ...resume,
            id: randomUUID(),
            createdAt: timestamp,
            updatedAt: timestamp,
        })
            .returning();
        return newResume;
    }
    async updateResume(resume) {
        const { id, ...updateData } = resume;
        const payload = stripUndefined(updateData);
        const [updatedResume] = await db
            .update(resumes)
            .set({
            ...payload,
            updatedAt: new Date(),
        })
            .where(eq(resumes.id, id))
            .returning();
        return updatedResume;
    }
    async deleteResume(id, userId) {
        await db
            .delete(resumes)
            .where(and(eq(resumes.id, id), eq(resumes.userId, userId)));
    }
    async createJobAnalysis(analysis) {
        const [newAnalysis] = await db
            .insert(jobAnalyses)
            .values({
            ...analysis,
            id: randomUUID(),
            createdAt: new Date(),
        })
            .returning();
        return newAnalysis;
    }
    async getJobAnalysisByResumeId(resumeId) {
        const [analysis] = await db
            .select()
            .from(jobAnalyses)
            .where(eq(jobAnalyses.resumeId, resumeId))
            .orderBy(desc(jobAnalyses.createdAt))
            .limit(1);
        return analysis;
    }
}
export const storage = new DatabaseStorage();
