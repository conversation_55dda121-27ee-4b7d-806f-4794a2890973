import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, ArrowLeft, Wand2, RefreshCw, Link, AlertCircle, CheckCircle } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
export default function JobTargetingForm({ data, onDataChange, onNext, onPrevious, }) {
    const { toast } = useToast();
    const [inputMethod, setInputMethod] = useState("paste");
    const [jobDescription, setJobDescription] = useState("");
    const [jobUrl, setJobUrl] = useState("");
    const [targetJobTitle, setTargetJobTitle] = useState("");
    const [targetCompany, setTargetCompany] = useState("");
    const [analysisResult, setAnalysisResult] = useState(null);
    // Initialize form data
    useEffect(() => {
        if (data.jobDescription) {
            setJobDescription(data.jobDescription);
        }
        if (data.jobUrl) {
            setJobUrl(data.jobUrl);
            setInputMethod("url");
        }
        if (data.targetJobTitle) {
            setTargetJobTitle(data.targetJobTitle);
        }
        if (data.targetCompany) {
            setTargetCompany(data.targetCompany);
        }
        if (data.extractedKeywords && Array.isArray(data.extractedKeywords)) {
            setAnalysisResult({
                keywords: data.extractedKeywords,
                requirements: [],
                suggestedImprovements: [],
                matchScore: "mid"
            });
        }
    }, [data]);
    const updateData = () => {
        onDataChange({
            jobDescription,
            jobUrl: inputMethod === "url" ? jobUrl : undefined,
            targetJobTitle,
            targetCompany,
            extractedKeywords: analysisResult?.keywords || []
        });
    };
    // Scrape job from URL mutation
    const scrapeJobMutation = useMutation({
        mutationFn: async (url) => {
            return await apiRequest('POST', '/api/jobs/scrape', { url });
        },
        onSuccess: (scrapedData) => {
            setJobDescription(scrapedData.description);
            if (scrapedData.title)
                setTargetJobTitle(scrapedData.title);
            if (scrapedData.company)
                setTargetCompany(scrapedData.company);
            toast({
                title: "Success",
                description: "Job description extracted successfully!",
            });
        },
        onError: (error) => {
            if (isUnauthorizedError(error)) {
                toast({
                    title: "Unauthorized",
                    description: "You are logged out. Logging in again...",
                    variant: "destructive",
                });
                setTimeout(() => {
                    window.location.href = "/api/login";
                }, 500);
                return;
            }
            toast({
                title: "Error",
                description: "Failed to extract job description. Please try pasting it manually.",
                variant: "destructive",
            });
        },
    });
    // Analyze job description mutation
    const analyzeJobMutation = useMutation({
        mutationFn: async () => {
            return await apiRequest('POST', '/api/jobs/analyze', {
                jobDescription,
                resumeId: data.id
            });
        },
        onSuccess: (response) => {
            setAnalysisResult(response);
            toast({
                title: "Analysis Complete",
                description: `Found ${response.keywords.length} key keywords and ${response.requirements.length} requirements.`,
            });
        },
        onError: (error) => {
            if (isUnauthorizedError(error)) {
                toast({
                    title: "Unauthorized",
                    description: "You are logged out. Logging in again...",
                    variant: "destructive",
                });
                setTimeout(() => {
                    window.location.href = "/api/login";
                }, 500);
                return;
            }
            toast({
                title: "Error",
                description: "Failed to analyze job description. Please try again.",
                variant: "destructive",
            });
        },
    });
    const handleScrapeJob = () => {
        if (!jobUrl.trim()) {
            toast({
                title: "Missing URL",
                description: "Please enter a valid job posting URL.",
                variant: "destructive",
            });
            return;
        }
        try {
            new URL(jobUrl);
            scrapeJobMutation.mutate(jobUrl);
        }
        catch {
            toast({
                title: "Invalid URL",
                description: "Please enter a valid URL starting with http:// or https://",
                variant: "destructive",
            });
        }
    };
    const handleAnalyzeJob = () => {
        if (!jobDescription.trim()) {
            toast({
                title: "Missing Job Description",
                description: "Please enter or extract a job description first.",
                variant: "destructive",
            });
            return;
        }
        analyzeJobMutation.mutate();
    };
    const isValidForm = () => {
        return jobDescription.trim().length > 100 && targetJobTitle.trim();
    };
    const handleNext = () => {
        if (isValidForm()) {
            updateData();
            onNext();
        }
    };
    useEffect(() => {
        updateData();
    }, [jobDescription, jobUrl, targetJobTitle, targetCompany, analysisResult]);
    return (<div className="space-y-6">
      {/* Header */}
      <div className="hidden lg:block">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Job Targeting</h1>
          <span className="text-sm text-muted-foreground bg-secondary px-3 py-1 rounded-full">
            Step 5 of 6
          </span>
        </div>
        <p className="text-muted-foreground">
          Paste the job description or provide a URL to automatically tailor your resume with relevant keywords and requirements.
        </p>
      </div>

      {/* Target Job Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="targetJobTitle">Target Job Title *</Label>
          <Input id="targetJobTitle" type="text" placeholder="Software Engineer" value={targetJobTitle} onChange={(e) => setTargetJobTitle(e.target.value)} autoComplete="organization-title" data-testid="input-target-job-title"/>
        </div>
        <div className="space-y-2">
          <Label htmlFor="targetCompany">Target Company (Optional)</Label>
          <Input id="targetCompany" type="text" placeholder="Google Inc." value={targetCompany} onChange={(e) => setTargetCompany(e.target.value)} autoComplete="organization" data-testid="input-target-company"/>
        </div>
      </div>

      {/* Input Method Selection */}
      <Card className="border-border">
        <CardContent className="p-6">
          <div className="space-y-4">
            <Label className="text-base font-medium">How would you like to provide the job description?</Label>
            <RadioGroup value={inputMethod} onValueChange={(value) => setInputMethod(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="paste" id="paste" data-testid="radio-paste"/>
                <Label htmlFor="paste">Paste Job Description</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="url" id="url" data-testid="radio-url"/>
                <Label htmlFor="url">Job Posting URL</Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>

      {/* Paste Text Area */}
      {inputMethod === "paste" && (<div className="space-y-2">
          <Label htmlFor="jobDescription">Job Description *</Label>
          <Textarea id="jobDescription" rows={12} placeholder="Paste the complete job description here. Include responsibilities, requirements, qualifications, and any specific skills mentioned..." value={jobDescription} onChange={(e) => setJobDescription(e.target.value)} className="resize-vertical" autoComplete="off" data-testid="textarea-job-description"/>
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>
              {jobDescription.length > 100 ? "✓" : "⚠"} Minimum 100 characters required
            </span>
            <span>{jobDescription.length} characters</span>
          </div>
        </div>)}

      {/* URL Input */}
      {inputMethod === "url" && (<div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="jobUrl">Job Posting URL</Label>
            <div className="flex space-x-2">
              <div className="relative flex-1">
                <Link className="absolute left-3 top-3 h-4 w-4 text-muted-foreground"/>
                <Input id="jobUrl" type="url" placeholder="https://company.com/careers/job-posting" value={jobUrl} onChange={(e) => setJobUrl(e.target.value)} className="pl-10" autoComplete="url" data-testid="input-job-url"/>
              </div>
              <Button type="button" onClick={handleScrapeJob} disabled={!jobUrl.trim() || scrapeJobMutation.isPending} data-testid="button-scrape-job">
                {scrapeJobMutation.isPending ? (<RefreshCw className="h-4 w-4 animate-spin"/>) : ("Extract")}
              </Button>
            </div>
          </div>
          
          <div className="text-xs text-muted-foreground bg-blue-50 border border-blue-200 rounded-lg p-3">
            <AlertCircle className="h-4 w-4 inline mr-2"/>
            We'll automatically extract the job description from supported job sites like LinkedIn, Indeed, and company career pages.
          </div>

          {/* Extracted Job Description */}
          {jobDescription && (<div className="space-y-2">
              <Label htmlFor="extractedJobDescription">Extracted Job Description</Label>
              <Textarea id="extractedJobDescription" rows={8} value={jobDescription} onChange={(e) => setJobDescription(e.target.value)} className="resize-vertical" autoComplete="off" data-testid="textarea-extracted-description"/>
            </div>)}
        </div>)}

      {/* AI Analysis Section */}
      {jobDescription && (<Card className="border-accent/20 bg-accent/5">
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">AI Analysis</h3>
                  <p className="text-sm text-muted-foreground">
                    Analyze the job description to extract keywords and optimize your resume
                  </p>
                </div>
                <Button onClick={handleAnalyzeJob} disabled={!jobDescription.trim() || analyzeJobMutation.isPending} data-testid="button-analyze-job">
                  {analyzeJobMutation.isPending ? (<RefreshCw className="h-4 w-4 mr-2 animate-spin"/>) : (<Wand2 className="h-4 w-4 mr-2"/>)}
                  {analyzeJobMutation.isPending ? "Analyzing..." : "Analyze Job"}
                </Button>
              </div>

              {/* Analysis Results */}
              {analysisResult && (<div className="space-y-4 pt-4 border-t border-accent/20">
                  <div className="flex items-center space-x-2 text-green-600">
                    <CheckCircle className="h-4 w-4"/>
                    <span className="text-sm font-medium">Analysis Complete</span>
                  </div>

                  {/* Keywords */}
                  {analysisResult.keywords.length > 0 && (<div>
                      <h4 className="text-sm font-medium mb-2">Key Keywords ({analysisResult.keywords.length})</h4>
                      <div className="flex flex-wrap gap-2">
                        {analysisResult.keywords.slice(0, 20).map((keyword, index) => (<Badge key={index} variant="secondary" className="text-xs" data-testid={`keyword-${index}`}>
                            {keyword}
                          </Badge>))}
                        {analysisResult.keywords.length > 20 && (<Badge variant="outline" className="text-xs">
                            +{analysisResult.keywords.length - 20} more
                          </Badge>)}
                      </div>
                    </div>)}

                  {/* Requirements */}
                  {analysisResult.requirements.length > 0 && (<div>
                      <h4 className="text-sm font-medium mb-2">Key Requirements</h4>
                      <ul className="text-xs text-muted-foreground space-y-1">
                        {analysisResult.requirements.slice(0, 5).map((req, index) => (<li key={index} className="flex items-start">
                            <span className="mr-2">•</span>
                            <span>{req}</span>
                          </li>))}
                      </ul>
                    </div>)}

                  {/* Match Score */}
                  <div>
                    <h4 className="text-sm font-medium mb-1">Complexity Level</h4>
                    <span className="text-xs text-muted-foreground capitalize">
                      {analysisResult.matchScore} level position
                    </span>
                  </div>
                </div>)}
            </div>
          </CardContent>
        </Card>)}

      {/* Tips */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <h4 className="text-sm font-medium mb-2">Job Targeting Tips</h4>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• Copy the entire job posting including requirements and responsibilities</li>
            <li>• The AI will identify ATS keywords to include in your resume</li>
            <li>• Review the extracted keywords and ensure they appear naturally in your content</li>
            <li>• Focus on keywords that match your actual experience and skills</li>
            <li>• The analysis helps tailor your resume for this specific position</li>
          </ul>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-border">
        <Button variant="ghost" onClick={onPrevious} data-testid="button-previous">
          <ArrowLeft className="h-4 w-4 mr-2"/>
          Previous
        </Button>
        
        <Button onClick={handleNext} disabled={!isValidForm()} data-testid="button-next">
          Continue
          <ArrowRight className="h-4 w-4 ml-2"/>
        </Button>
      </div>
    </div>);
}
