import passport from "passport";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";
import { Strategy as LocalStrategy } from "passport-local";
import { storage } from "./storage.js";
import { User } from "@shared/schema";

/* Google OAuth Strategy
   Register only when GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET are present
   to avoid startup errors in development environments where these values
   may not be configured. */
if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  passport.use(
    new GoogleStrategy(
      {
        clientID: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        callbackURL: "/api/auth/google/callback",
        scope: ["profile", "email"],
      },
      async (accessToken, refreshToken, profile, done) => {
        try {
          // Extract user information from Google profile
          const googleId = profile.id;
          const email = profile.emails?.[0]?.value || "";
          const firstName = profile.name?.givenName || "";
          const lastName = profile.name?.familyName || "";
          const profileImageUrl = profile.photos?.[0]?.value || "";

          // Get or create user in our database
          let user = await storage.getUser(googleId);

          if (!user) {
            // Create new user
            user = await storage.upsertUser({
              id: googleId,
              email,
              firstName,
              lastName,
              profileImageUrl,
            });
          } else {
            // Update existing user with latest info
            user = await storage.upsertUser({
              id: googleId,
              email,
              firstName,
              lastName,
              profileImageUrl,
            });
          }

          return done(null, user);
        } catch (error) {
          console.error("[Auth] Google OAuth error:", error);
          return done(error, undefined);
        }
      }
    )
  );
} else {
  console.warn(
    "[Auth] GOOGLE_CLIENT_ID or GOOGLE_CLIENT_SECRET is not set; skipping GoogleStrategy registration."
  );
}

// Local Strategy (for future password-based auth)
passport.use(
  new LocalStrategy(
    {
      usernameField: "email",
      passwordField: "password",
    },
    async (email, password, done) => {
      try {
        // For now, we'll just use Google OAuth, but this is where you'd add local auth
        return done(null, false, { message: "Local authentication not implemented yet" });
      } catch (error) {
        console.error("[Auth] Local auth error:", error);
        return done(error, undefined);
      }
    }
  )
);

// Serialize user for session
passport.serializeUser((user: any, done: any) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id: string, done: any) => {
  try {
    const user = await storage.getUser(id);
    done(null, user);
  } catch (error) {
    console.error("[Auth] Deserialize error:", error);
    done(error, undefined);
  }
});

export { passport };
