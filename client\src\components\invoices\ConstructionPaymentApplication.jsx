import React from "react";
export default function ConstructionPaymentApplication({ data, logoUrl }) {
    const contractSumToDate = data.originalContractSum + data.netChangeByChangeOrders;
    const totalCompleted = data.workItems.reduce((sum, item) => sum + item.totalCompleted, 0);
    const totalRetainage = data.retainageOnCompletedWork + data.retainageOnStoredMaterial;
    const totalEarnedLessRetainage = totalCompleted - totalRetainage;
    const currentPaymentDue = totalEarnedLessRetainage - data.lessPreviousCertificates;
    return (<div className="w-full max-w-[8.5in] mx-auto bg-white p-8 print:p-0">
      {/* Header */}
      <div className="grid grid-cols-3 gap-4 mb-6 pb-4 border-b-2 border-black">
        {/* Left Column - To */}
        <div>
          <p className="text-xs font-semibold mb-1">To:</p>
          <p className="text-sm font-bold">{data.owner}</p>
          <p className="text-xs">{data.ownerAddress}</p>
        </div>

        {/* Middle Column - Project */}
        <div>
          <p className="text-xs font-semibold mb-1">Project:</p>
          <p className="text-sm font-bold">{data.project}</p>
          <p className="text-xs">{data.projectAddress}</p>

          <div className="mt-3">
            <p className="text-xs font-semibold mb-1">From Contractor:</p>
            <p className="text-sm font-bold">{data.contractor}</p>
            <p className="text-xs">{data.contractorAddress}</p>
          </div>

          <div className="mt-3">
            <p className="text-xs font-semibold mb-1">Contract For:</p>
            <p className="text-sm">{data.contractFor}</p>
          </div>
        </div>

        {/* Right Column - Application Details & Logo */}
        <div className="text-right">
          <div className="mb-2">
            <p className="text-xs font-semibold">APPLICATION NO. {data.applicationNo}</p>
            <p className="text-xs">APPLICATION DATE: {data.applicationDate}</p>
            <p className="text-xs">PERIOD TO: {data.periodTo}</p>
            <p className="text-xs">PROJECT NO. {data.projectNo}</p>
            <p className="text-xs">CONTRACT DATE: {data.contractDate}</p>
          </div>

          <div className="mt-4">
            <p className="text-xs font-semibold mb-1">Distribution to:</p>
            <div className="text-xs space-y-0.5">
              <label className="flex items-center justify-end gap-1">
                <span>Owner</span>
                <input type="checkbox" checked={data.distributeTo.owner} readOnly className="w-3 h-3"/>
              </label>
              <label className="flex items-center justify-end gap-1">
                <span>Architect</span>
                <input type="checkbox" checked={data.distributeTo.architect} readOnly className="w-3 h-3"/>
              </label>
              <label className="flex items-center justify-end gap-1">
                <span>Contractor</span>
                <input type="checkbox" checked={data.distributeTo.contractor} readOnly className="w-3 h-3"/>
              </label>
              <label className="flex items-center justify-end gap-1">
                <span>Field</span>
                <input type="checkbox" checked={data.distributeTo.field} readOnly className="w-3 h-3"/>
              </label>
            </div>
          </div>

          {logoUrl && (<div className="mt-4">
              <img src={logoUrl} alt="Company Logo" className="w-24 h-24 ml-auto"/>
            </div>)}
        </div>
      </div>

      {/* Application for Payment */}
      <div className="mb-6">
        <h2 className="text-lg font-bold mb-3">Application for Payment</h2>
        <p className="text-xs mb-3">
          Application is made for payment, as shown below, in connection with the Contract.
        </p>

        <div className="border border-black">
          <table className="w-full text-xs">
            <tbody>
              <tr className="border-b border-black">
                <td className="p-2 font-semibold">Original Contract Sum</td>
                <td className="p-2 text-right">${data.originalContractSum.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
              </tr>
              <tr className="border-b border-black">
                <td className="p-2 font-semibold">Net change by Change Orders</td>
                <td className="p-2 text-right">${data.netChangeByChangeOrders.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
              </tr>
              <tr className="border-b border-black bg-gray-100">
                <td className="p-2 font-bold">Contract Sum to Date</td>
                <td className="p-2 text-right font-bold">${contractSumToDate.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
              </tr>
              <tr className="border-b border-black">
                <td className="p-2 font-bold">Total Completed & Stored to Date</td>
                <td className="p-2 text-right font-bold">${totalCompleted.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
              </tr>
              <tr className="border-b border-black">
                <td className="p-2 font-semibold pl-6">Retainage</td>
                <td className="p-2"></td>
              </tr>
              <tr className="border-b border-black">
                <td className="p-2 pl-10">{data.retainagePercent}% of Completed Work</td>
                <td className="p-2 text-right">${data.retainageOnCompletedWork.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
              </tr>
              <tr className="border-b border-black">
                <td className="p-2 pl-10">0.00% of Stored Material</td>
                <td className="p-2 text-right">${data.retainageOnStoredMaterial.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
              </tr>
              <tr className="border-b border-black bg-gray-100">
                <td className="p-2 font-bold">Total Retainage</td>
                <td className="p-2 text-right font-bold">${totalRetainage.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
              </tr>
              <tr className="border-b border-black">
                <td className="p-2 font-bold">Total Earned Less Retainage</td>
                <td className="p-2 text-right font-bold">${totalEarnedLessRetainage.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
              </tr>
              <tr className="border-b border-black">
                <td className="p-2 font-semibold">Less Previous Certificates For Payment</td>
                <td className="p-2 text-right">${data.lessPreviousCertificates.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
              </tr>
              <tr className="bg-yellow-100">
                <td className="p-2 font-bold">Current Payment Due</td>
                <td className="p-2 text-right font-bold">${currentPaymentDue.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Change Order Summary */}
        <div className="mt-4 border border-black">
          <table className="w-full text-xs">
            <thead className="bg-gray-200">
              <tr className="border-b border-black">
                <th className="p-2 text-left font-semibold">Change Order Summary</th>
                <th className="p-2 text-center font-semibold">ADDITIONS</th>
                <th className="p-2 text-center font-semibold">DEDUCTIONS</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-black">
                <td className="p-2">Total changes approved in previous months</td>
                <td className="p-2 text-center">$0.00</td>
                <td className="p-2 text-center">$0.00</td>
              </tr>
              <tr className="border-b border-black">
                <td className="p-2">Total approved this Month</td>
                <td className="p-2 text-center">$0.00</td>
                <td className="p-2 text-center">$0.00</td>
              </tr>
              <tr className="bg-gray-100">
                <td className="p-2 font-bold">TOTALS</td>
                <td className="p-2 text-center font-bold">$0.00</td>
                <td className="p-2 text-center font-bold">$0.00</td>
              </tr>
              <tr>
                <td className="p-2 font-bold">Net Changes By Order:</td>
                <td className="p-2 text-center font-bold" colSpan={2}>$0.00</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Work Completed Table */}
      <div className="mb-6 page-break-before">
        <div className="overflow-x-auto border border-black">
          <table className="w-full text-[10px]">
            <thead className="bg-gray-200">
              <tr className="border-b border-black">
                <th className="p-1 text-center border-r border-black" rowSpan={2}>Item<br />No.</th>
                <th className="p-1 text-center border-r border-black" rowSpan={2}>Description of Work</th>
                <th className="p-1 text-center border-r border-black" rowSpan={2}>Scheduled<br />Value</th>
                <th className="p-1 text-center border-r border-black" colSpan={2}>Work Completed</th>
                <th className="p-1 text-center border-r border-black" colSpan={2}>Materials<br />Presently<br />Stored</th>
                <th className="p-1 text-center border-r border-black" rowSpan={2}>Total<br />Completed<br />& Stored to Date<br />(D+E+F)</th>
                <th className="p-1 text-center border-r border-black" rowSpan={2}>%<br />(G÷C)</th>
                <th className="p-1 text-center border-r border-black" rowSpan={2}>Balance to<br />Finish<br />(C-G)</th>
                <th className="p-1 text-center" rowSpan={2}>Retainage</th>
              </tr>
              <tr className="border-b border-black">
                <th className="p-1 text-center border-r border-black">From Previous<br />Application<br />(D÷E)</th>
                <th className="p-1 text-center border-r border-black">This Period</th>
                <th className="p-1 text-center border-r border-black">(Not in D or E)</th>
                <th className="p-1 text-center border-r border-black">D + E</th>
              </tr>
              <tr className="bg-gray-100 border-b border-black">
                <th className="p-1 text-center">A</th>
                <th className="p-1 text-center">B</th>
                <th className="p-1 text-center">C</th>
                <th className="p-1 text-center">D</th>
                <th className="p-1 text-center">E</th>
                <th className="p-1 text-center">F</th>
                <th className="p-1 text-center">G</th>
                <th className="p-1 text-center">H</th>
                <th className="p-1 text-center">I</th>
                <th className="p-1 text-center">J</th>
                <th className="p-1 text-center">K</th>
              </tr>
            </thead>
            <tbody>
              {data.workItems.map((item, index) => (<tr key={index} className="border-b border-black">
                  <td className="p-1 text-center border-r border-black">{item.itemNo}</td>
                  <td className="p-1 border-r border-black">{item.description}</td>
                  <td className="p-1 text-right border-r border-black">${item.scheduledValue.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                  <td className="p-1 text-right border-r border-black">${item.fromPreviousApplication.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                  <td className="p-1 text-right border-r border-black">${item.thisPeriod.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                  <td className="p-1 text-right border-r border-black">${item.materialsPresently.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                  <td className="p-1 text-right border-r border-black">${item.totalCompleted.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                  <td className="p-1 text-right border-r border-black">{item.percent.toFixed(1)}%</td>
                  <td className="p-1 text-right border-r border-black">${item.balanceToFinish.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                  <td className="p-1 text-right">${item.retainage.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                </tr>))}
              <tr className="bg-gray-100 font-bold">
                <td className="p-1 border-r border-black" colSpan={2}>GRAND TOTAL</td>
                <td className="p-1 text-right border-r border-black">
                  ${data.workItems.reduce((sum, item) => sum + item.scheduledValue, 0).toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </td>
                <td className="p-1 text-right border-r border-black">
                  ${data.workItems.reduce((sum, item) => sum + item.fromPreviousApplication, 0).toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </td>
                <td className="p-1 text-right border-r border-black">
                  ${data.workItems.reduce((sum, item) => sum + item.thisPeriod, 0).toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </td>
                <td className="p-1 text-right border-r border-black">
                  ${data.workItems.reduce((sum, item) => sum + item.materialsPresently, 0).toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </td>
                <td className="p-1 text-right border-r border-black">
                  ${data.workItems.reduce((sum, item) => sum + item.totalCompleted, 0).toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </td>
                <td className="p-1 text-center border-r border-black">--</td>
                <td className="p-1 text-right border-r border-black">
                  ${data.workItems.reduce((sum, item) => sum + item.balanceToFinish, 0).toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </td>
                <td className="p-1 text-right">
                  ${data.workItems.reduce((sum, item) => sum + item.retainage, 0).toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Certification Text */}
      <div className="grid grid-cols-2 gap-4 text-xs mt-6">
        <div className="border border-black p-3">
          <p className="mb-2 text-justify">
            The undersigned Contractor certifies that to the best of the Contractor's knowledge, information and belief the
            Work covered by this Application for Payment has been completed in accordance with the Contract Documents,
            that all amounts have been paid by the Contractor for Work for which previous Certificates for Payment were
            issued and payments received from the Owner, and that current payment shown herein is now due.
          </p>
          <div className="mt-4">
            <p className="font-bold mb-2">CONTRACTOR: {data.contractor}</p>
            <div className="grid grid-cols-2 gap-4">
              <div className="border-b border-black pt-8">BY _______________</div>
              <div className="border-b border-black pt-8">DATE _______________</div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-2">
              <div className="border-b border-black pt-4">STATE OF _______________</div>
              <div className="border-b border-black pt-4">COUNTY OF _______________</div>
            </div>
            <p className="mt-2 text-[10px]">SUBSCRIBED AND SWORN TO BEFORE ME THIS DAY OF</p>
            <p className="mt-1 text-[10px]">NOTARY PUBLIC</p>
          </div>
        </div>

        <div className="border border-black p-3">
          <h3 className="font-bold mb-2">General Certificate of Payment</h3>
          <p className="mb-2 text-justify text-[10px]">
            In accordance with the Contract Documents, based on on-site observations and the data comprising this
            application, the Architect certifies to the Owner that to the best of the Architect's knowledge, information and
            belief the Work has progressed as indicated, the quality of the Work is in accordance with the Contract
            Documents, and the Contractor is entitled to payment of the AMOUNT CERTIFIED.
          </p>

          <div className="mt-4 bg-gray-100 p-2 border border-black">
            <p className="font-bold">Amount Certified _______________</p>
            <p className="text-[10px] mt-1">
              (Attach explanation if amount certified differs from amount applied for, label as Figures in this Application and on the
              Continuation Sheet that changed to conform to the amount certified)
            </p>
          </div>

          <div className="mt-4">
            <p className="font-bold mb-2">General Contractor</p>
            <div className="grid grid-cols-2 gap-4">
              <div className="border-b border-black pt-4">BY _______________</div>
              <div className="border-b border-black pt-4">DATE _______________</div>
            </div>
            <p className="text-[10px] mt-2">
              This Certificate is not negotiable. The AMOUNT CERTIFIED is payable only to the Contractor named herein. Issuance,
              payment and acceptance of payment are without prejudice to any rights of the Owner or Contractor under this Contract.
            </p>
          </div>
        </div>

        <div className="border border-black p-3 col-span-2">
          <h3 className="font-bold mb-2">Owner of Applicatio</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="border-b border-black pt-8">BY _______________</div>
            <div className="border-b border-black pt-8">DATE _______________</div>
          </div>
        </div>
      </div>
    </div>);
}
