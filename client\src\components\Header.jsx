import React from "react";
import { useLocation } from "wouter";
import Logo from "./Logo";
import { Button } from "./ui/button";
import { FileText, LogOut, Menu } from "lucide-react";
import { useAuth, signOut } from "@/hooks/useAuth";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger, } from "@/components/ui/dropdown-menu";
export default function Header() {
    const [location, navigate] = useLocation();
    const { user, isAuthenticated, isLoading } = useAuth();
    const handleLogout = async () => {
        await signOut();
        navigate("/");
    };
    if (isLoading) {
        return null; // Or loading spinner
    }
    if (!isAuthenticated) {
        return null;
    }
    return (<header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between px-4">
        {/* Logo - Left Side */}
        <button onClick={() => navigate("/")} className="flex items-center transition-opacity hover:opacity-80" title="Home" aria-label="Home">
          <Logo size="md"/>
        </button>

        {/* Navigation - Center */}
        <nav className="hidden md:flex items-center gap-6">
          <Button variant={location === "/" ? "default" : "ghost"} onClick={() => navigate("/")} className="text-sm">
            Home
          </Button>
          <Button variant={location.startsWith("/resume-builder") ? "default" : "ghost"} onClick={() => navigate("/resume-builder")} className="text-sm">
            <FileText className="h-4 w-4 mr-2"/>
            Resume Builder
          </Button>
          <Button variant={location === "/invoice-templates" ? "default" : "ghost"} onClick={() => navigate("/invoice-templates")} className="text-sm">
            Invoices
          </Button>
        </nav>

        {/* User Menu - Right Side */}
        <div className="flex items-center gap-4">
          {/* Desktop Menu */}
          <div className="hidden md:flex items-center gap-3">
            {user && (<div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <span className="text-sm font-semibold text-primary">
                    {user.firstName?.[0]?.toUpperCase() || user.email?.[0]?.toUpperCase() || "U"}
                  </span>
                </div>
              <span className="text-sm text-muted-foreground max-w-[150px] truncate">
                {user.firstName} {user.lastName}
              </span>
              </div>)}
            <Button variant="outline" size="sm" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2"/>
              Logout
            </Button>
          </div>

          {/* Mobile Menu */}
          <div className="md:hidden">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <Menu className="h-5 w-5"/>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {user && (<>
                    <div className="px-2 py-2">
                      <p className="text-sm font-medium">{user.firstName} {user.lastName}</p>
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    </div>
                    <DropdownMenuSeparator />
                  </>)}
                <DropdownMenuItem onClick={() => navigate("/")}>
                  Home
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate("/resume-builder")}>
                  <FileText className="h-4 w-4 mr-2"/>
                  Resume Builder
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate("/invoice-templates")}>
                  Invoices
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="h-4 w-4 mr-2"/>
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>);
}
