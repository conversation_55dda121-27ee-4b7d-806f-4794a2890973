import Stripe from 'stripe';
import { storage } from '../storage';
import type { Resume } from '@shared/schema';

// Initialize Stripe with your secret key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY || '';
let stripe: Stripe;

// Only initialize Stripe if the secret key is provided and valid
if (stripeSecretKey && stripeSecretKey !== 'sk_test_your_stripe_secret_key_here') {
  stripe = new Stripe(stripeSecretKey, {
    apiVersion: '2025-09-30.clover',
  });
} else {
  console.warn('Stripe secret key not configured or is using placeholder value. Payment features will be disabled.');
  // Create a mock Stripe object for development
  stripe = {
    paymentIntents: {
      create: async () => {
        throw new Error('Stripe is not configured. Please set STRIPE_SECRET_KEY in your environment variables.');
      }
    },
    checkout: {
      sessions: {
        create: async () => {
          throw new Error('Stripe is not configured. Please set STRIPE_SECRET_KEY in your environment variables.');
        }
      }
    },
    webhooks: {
      constructEvent: () => {
        throw new Error('Stripe is not configured. Please set STRIPE_SECRET_KEY in your environment variables.');
      }
    }
  } as any;
  }
  
  // Export the stripe instance for use in other modules
  export { stripe };

export interface CreatePaymentIntentParams {
  resumeId: string;
  userId: string;
  amount?: number; // in cents, default 199 ($1.99)
  currency?: string; // default 'usd'
}

export interface CreateCheckoutSessionParams {
  resumeId: string;
  userId: string;
  successUrl: string;
  cancelUrl: string;
  amount?: number; // in cents, default 199 ($1.99)
  currency?: string; // default 'usd'
}

/**
 * Create a payment intent for a resume download
 */
export async function createPaymentIntent({
  resumeId,
  userId,
  amount = 199, // $1.99 in cents
  currency = 'usd'
}: CreatePaymentIntentParams) {
  try {
    // Verify the resume belongs to the user
    const resume = await storage.getResume(resumeId, userId);
    if (!resume) {
      throw new Error('Resume not found');
    }

    // If already paid, return the existing payment info
    if (resume.paymentStatus === 'paid') {
      return {
        paymentIntentId: resume.stripePaymentIntentId,
        status: 'succeeded',
        amount: resume.paymentAmount,
        currency: resume.paymentCurrency
      };
    }

    // Create a payment intent with Stripe
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      metadata: {
        resumeId,
        userId
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    // Update the resume with the payment intent ID
    await storage.updateResume({
      id: resumeId,
      stripePaymentIntentId: paymentIntent.id,
      paymentAmount: amount,
      paymentCurrency: currency,
    });

    return {
      paymentIntentId: paymentIntent.id,
      clientSecret: paymentIntent.client_secret,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: paymentIntent.status
    };
  } catch (error) {
    console.error('Error creating payment intent:', error);
    throw error;
  }
}

/**
 * Create a checkout session for a resume download
 */
export async function createCheckoutSession({
  resumeId,
  userId,
  successUrl,
  cancelUrl,
  amount = 199, // $1.99 in cents
  currency = 'usd'
}: CreateCheckoutSessionParams) {
  try {
    // Verify the resume belongs to the user
    const resume = await storage.getResume(resumeId, userId);
    if (!resume) {
      throw new Error('Resume not found');
    }

    // If already paid, return success
    if (resume.paymentStatus === 'paid') {
      return {
        sessionId: resume.stripeSessionId,
        status: 'complete',
        url: successUrl
      };
    }

    // Create a checkout session with Stripe
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency,
            product_data: {
              name: 'Resume PDF Download',
              description: `Download optimized PDF for "${resume.title}"`,
            },
            unit_amount: amount,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        resumeId,
        userId
      },
      customer_email: resume.personalInfo ? (resume.personalInfo as any).email : undefined,
    });

    // Update the resume with the session ID
    await storage.updateResume({
      id: resumeId,
      stripeSessionId: session.id,
      paymentAmount: amount,
      paymentCurrency: currency,
    });

    return {
      sessionId: session.id,
      url: session.url,
      status: session.status
    };
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw error;
  }
}

/**
 * Handle Stripe webhook events
 */
export async function handleWebhookEvent(event: Stripe.Event) {
  try {
    switch (event.type) {
      case 'checkout.session.completed':
        const checkoutSession = event.data.object as Stripe.Checkout.Session;
        await handleCheckoutSessionCompleted(checkoutSession);
        break;
      
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        await handlePaymentIntentSucceeded(paymentIntent);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  } catch (error) {
    console.error('Error handling webhook event:', error);
    throw error;
  }
}

/**
 * Handle successful checkout session
 */
async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  const resumeId = session.metadata?.resumeId;
  const userId = session.metadata?.userId;
  
  if (!resumeId || !userId) {
    console.error('Missing metadata in checkout session');
    return;
  }

  // Update the resume payment status
  await storage.updateResume({
    id: resumeId,
    paymentStatus: 'paid',
    paidAt: new Date(),
    stripeSessionId: session.id,
    stripeCustomerId: session.customer as string,
  });

  console.log(`Payment completed for resume ${resumeId} by user ${userId}`);
}

/**
 * Handle successful payment intent
 */
async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  const resumeId = paymentIntent.metadata?.resumeId;
  const userId = paymentIntent.metadata?.userId;
  
  if (!resumeId || !userId) {
    console.error('Missing metadata in payment intent');
    return;
  }

  // Update the resume payment status
  await storage.updateResume({
    id: resumeId,
    paymentStatus: 'paid',
    paidAt: new Date(),
    stripePaymentIntentId: paymentIntent.id,
    stripeCustomerId: paymentIntent.customer as string,
  });

  console.log(`Payment completed for resume ${resumeId} by user ${userId}`);
}

/**
 * Check if a resume has been paid for
 */
export async function checkPaymentStatus(resumeId: string, userId: string): Promise<boolean> {
  const resume = await storage.getResume(resumeId, userId);
  if (!resume) {
    throw new Error('Resume not found');
  }
  
  return resume.paymentStatus === 'paid';
}

/**
 * Refund a payment (admin function)
 */
export async function refundPayment(resumeId: string, userId: string) {
  const resume = await storage.getResume(resumeId, userId);
  if (!resume || !resume.stripePaymentIntentId) {
    throw new Error('Payment not found');
  }

  const refund = await stripe.refunds.create({
    payment_intent: resume.stripePaymentIntentId,
  });

  // Update the resume payment status
  await storage.updateResume({
    id: resumeId,
    paymentStatus: 'refunded',
  });

  return refund;
}