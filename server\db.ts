import dotenv from "dotenv";
dotenv.config();

import fs from "node:fs";
import path from "node:path";
import * as schema from "@shared/schema";

// Determine database type based on DATABASE_URL
const DATABASE_URL = process.env.DATABASE_URL;
const isProduction = process.env.NODE_ENV === "production";

let db: any;

if (DATABASE_URL && !DATABASE_URL.startsWith("file:")) {
  // PostgreSQL for production (Vercel/Neon)
  const { drizzle } = await import("drizzle-orm/neon-http");
  const { neon } = await import("@neondatabase/serverless");

  const sql = neon(DATABASE_URL);
  db = drizzle(sql, { schema });

  console.log("Database initialized - PostgreSQL (Neon)");
} else {
  // SQLite for local development
  try {
    const Database = (await import("better-sqlite3")).default;
    const { drizzle } = await import("drizzle-orm/better-sqlite3");

    const defaultPath = path.join(process.cwd(), "data", "app.db");
    const databasePath = process.env.DATABASE_PATH || defaultPath;

    fs.mkdirSync(path.dirname(databasePath), { recursive: true });

    const sqlite = new Database(databasePath);
    sqlite.pragma("journal_mode = WAL");

    db = drizzle(sqlite, { schema });

    console.log("Database initialized - SQLite (local development)");
  } catch (error) {
    console.error("Failed to initialize SQLite database. Please set DATABASE_URL to use PostgreSQL instead.");
    console.error("Error:", error);
    throw new Error("Database initialization failed. Set DATABASE_URL environment variable to use PostgreSQL.");
  }
}

export { db };
