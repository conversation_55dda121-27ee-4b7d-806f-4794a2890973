import Database from "better-sqlite3";
import fs from "node:fs";
import path from "node:path";
import { drizzle } from "drizzle-orm/better-sqlite3";
import { migrate } from "drizzle-orm/better-sqlite3/migrator";
import * as schema from "@shared/schema";

const defaultPath = path.join(process.cwd(), "data", "app.db");
const databasePath = process.env.DATABASE_PATH || defaultPath;

fs.mkdirSync(path.dirname(databasePath), { recursive: true });

const sqlite = new Database(databasePath);
sqlite.pragma("journal_mode = WAL");

export const db = drizzle(sqlite, { schema });

// Skip migrations for now - use drizzle-kit push instead
console.log("Database initialized - using drizzle-kit push for schema management");
