interface PaymentModalProps {
    isOpen: boolean;
    onClose: () => void;
    resumeId: string;
    resumeTitle: string;
    amount: number;
    currency: string;
    onSuccess: () => void;
}
export default function PaymentModal({ isOpen, onClose, resumeId, resumeTitle, amount, currency, onSuccess }: PaymentModalProps): import("react").JSX.Element;
export {};
//# sourceMappingURL=PaymentModal.d.ts.map