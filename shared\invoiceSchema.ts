import { z } from "zod";

// Construction Payment Application Schema
export const constructionWorkItemSchema = z.object({
  itemNo: z.number(),
  description: z.string(),
  scheduledValue: z.number(),
  fromPreviousApplication: z.number(),
  thisPeriod: z.number(),
  materialsPresently: z.number(),
  totalCompleted: z.number(),
  percent: z.number(),
  balanceToFinish: z.number(),
  retainage: z.number(),
});

export const constructionPaymentApplicationSchema = z.object({
  id: z.string().uuid().optional(),
  userId: z.string(),
  applicationNo: z.string(),
  applicationDate: z.string(),
  periodTo: z.string(),
  projectNo: z.string(),
  contractDate: z.string(),
  project: z.string(),
  projectAddress: z.string(),
  owner: z.string(),
  ownerAddress: z.string(),
  contractor: z.string(),
  contractorAddress: z.string(),
  contractFor: z.string(),
  originalContractSum: z.number(),
  netChangeByChangeOrders: z.number(),
  workItems: z.array(constructionWorkItemSchema),
  retainagePercent: z.number(),
  retainageOnCompletedWork: z.number(),
  retainageOnStoredMaterial: z.number(),
  lessPreviousCertificates: z.number(),
  distributeTo: z.object({
    owner: z.boolean(),
    architect: z.boolean(),
    contractor: z.boolean(),
    field: z.boolean(),
  }),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export const insertConstructionPaymentApplicationSchema = constructionPaymentApplicationSchema.omit({ id: true, createdAt: true, updatedAt: true });
export const updateConstructionPaymentApplicationSchema = constructionPaymentApplicationSchema.partial().required({ id: true });

export type ConstructionPaymentApplication = z.infer<typeof constructionPaymentApplicationSchema>;
export type InsertConstructionPaymentApplication = z.infer<typeof insertConstructionPaymentApplicationSchema>;
export type UpdateConstructionPaymentApplication = z.infer<typeof updateConstructionPaymentApplicationSchema>;

// Consulting Invoice Schema
export const consultingLineItemSchema = z.object({
  description: z.string(),
  feeValue: z.number(),
  percentDone: z.number(),
  feeToDate: z.number(),
  prevInvoice: z.number(),
  thisInvoice: z.number(),
});

export const consultingInvoiceSchema = z.object({
  id: z.string().uuid().optional(),
  userId: z.string(),
  companyName: z.string(),
  companyLogoUrl: z.string().optional(),
  clientName: z.string(),
  clientAddress: z.array(z.string()),
  attention: z.string().optional(),
  invoiceNumber: z.string(),
  invoiceDate: z.string(),
  gst: z.string(),
  jobNo: z.string(),
  orderNumber: z.string(),
  totalAmount: z.number(),
  projectReference: z.string(),
  progressClaimNo: z.string(),
  lineItems: z.array(consultingLineItemSchema),
  gstRate: z.number(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export const insertConsultingInvoiceSchema = consultingInvoiceSchema.omit({ id: true, createdAt: true, updatedAt: true });
export const updateConsultingInvoiceSchema = consultingInvoiceSchema.partial().required({ id: true });

export type ConsultingInvoice = z.infer<typeof consultingInvoiceSchema>;
export type InsertConsultingInvoice = z.infer<typeof insertConsultingInvoiceSchema>;
export type UpdateConsultingInvoice = z.infer<typeof updateConsultingInvoiceSchema>;
