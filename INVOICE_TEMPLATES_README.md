# Invoice Templates Feature

This document describes the invoice template system that has been added to your application.

## Features

### 1. Two Professional Invoice Templates

#### Construction Payment Application (AIA Style)
- Based on standard AIA billing format
- Includes:
  - Project and contractor information
  - Original contract sum and change orders
  - Detailed work items table (Columns A-K)
  - Retainage calculations
  - Balance tracking
  - Certification sections for contractor, architect, and owner
  - Change order summary

#### Consulting Invoice
- Professional tax invoice format
- Includes:
  - Company branding with logo support
  - Client information
  - Progress billing with line items
  - Fee values and percentage completion
  - Previous invoice tracking
  - GST/Tax calculations
  - Running totals

### 2. Full CRUD API

**Endpoints:**
- `GET /api/invoices` - Get all invoices for the authenticated user
- `GET /api/invoices/:id` - Get a single invoice
- `POST /api/invoices` - Create a new invoice
- `PUT /api/invoices/:id` - Update an existing invoice
- `DELETE /api/invoices/:id` - Delete an invoice

**Note:** Currently uses in-memory storage. For production, you should migrate to database storage.

### 3. PDF Generation

Uses `jspdf` and `html2canvas` to generate high-quality PDFs:
- Letter-size format (8.5" × 11")
- High resolution (2x scale)
- Professional quality suitable for printing
- Automatic filename generation

### 4. Print Functionality

- Optimized print styles
- Proper page breaks
- Color-accurate printing
- Professional formatting

## File Structure

```
client/src/
├── components/
│   └── invoices/
│       ├── ConstructionPaymentApplication.tsx  # Construction invoice component
│       └── ConsultingInvoice.tsx               # Consulting invoice component
├── pages/
│   └── InvoiceTemplates.tsx                     # Main page with tabs and controls
├── lib/
│   └── pdfGenerator.ts                          # PDF generation utilities
└── App.tsx                                      # Updated with /invoice-templates route

server/
└── routes.ts                                    # Invoice API endpoints added

shared/
└── invoiceSchema.ts                             # TypeScript schemas and validation
```

## How to Use

### 1. Access the Invoice Templates

Navigate to `/invoice-templates` after logging in.

### 2. View Templates

Use the tabs to switch between:
- Construction Payment Application
- Consulting Invoice

### 3. Actions Available

**Save** - Save the current invoice to the backend (in-memory storage)
**Print** - Print the invoice using browser print dialog
**Download PDF** - Generate and download a PDF version

### 4. Integrate with Your Application

```typescript
import ConstructionPaymentApplication from '@/components/invoices/ConstructionPaymentApplication';
import ConsultingInvoice from '@/components/invoices/ConsultingInvoice';
import type { ConstructionPaymentApplicationData, ConsultingInvoiceData } from '@shared/invoiceSchema';

// Use in your components
<ConstructionPaymentApplication data={yourData} />
<ConsultingInvoice data={yourData} logoUrl="path/to/logo.png" />
```

### 5. Customize the Data

Modify the sample data in `InvoiceTemplates.tsx`:
- `sampleConstructionData` - Construction invoice data
- `sampleConsultingData` - Consulting invoice data

### 6. API Integration Example

```typescript
import { apiRequest } from '@/lib/queryClient';

// Create an invoice
const invoice = await apiRequest('POST', '/api/invoices', invoiceData);

// Get all invoices
const invoices = await apiRequest('GET', '/api/invoices');

// Update an invoice
const updated = await apiRequest('PUT', `/api/invoices/${id}`, updates);

// Delete an invoice
await apiRequest('DELETE', `/api/invoices/${id}`);
```

## TypeScript Interfaces

### Construction Payment Application

```typescript
interface ConstructionPaymentApplicationData {
  applicationNo: string;
  applicationDate: string;
  periodTo: string;
  projectNo: string;
  contractDate: string;
  project: string;
  projectAddress: string;
  owner: string;
  ownerAddress: string;
  contractor: string;
  contractorAddress: string;
  contractFor: string;
  originalContractSum: number;
  netChangeByChangeOrders: number;
  workItems: Array<{
    itemNo: number;
    description: string;
    scheduledValue: number;
    fromPreviousApplication: number;
    thisPeriod: number;
    materialsPresently: number;
    totalCompleted: number;
    percent: number;
    balanceToFinish: number;
    retainage: number;
  }>;
  retainagePercent: number;
  retainageOnCompletedWork: number;
  retainageOnStoredMaterial: number;
  lessPreviousCertificates: number;
  distributeTo: {
    owner: boolean;
    architect: boolean;
    contractor: boolean;
    field: boolean;
  };
}
```

### Consulting Invoice

```typescript
interface ConsultingInvoiceData {
  companyName: string;
  companyLogoUrl?: string;
  clientName: string;
  clientAddress: string[];
  attention?: string;
  invoiceNumber: string;
  invoiceDate: string;
  gst: string;
  jobNo: string;
  orderNumber: string;
  totalAmount: number;
  projectReference: string;
  progressClaimNo: string;
  lineItems: Array<{
    description: string;
    feeValue: number;
    percentDone: number;
    feeToDate: number;
    prevInvoice: number;
    thisInvoice: number;
  }>;
  gstRate: number;
}
```

## Calculations

### Construction Invoice
- **Contract Sum to Date** = Original Contract Sum + Net Change by Change Orders
- **Total Completed** = Sum of all work items' totalCompleted
- **Total Retainage** = Retainage on Completed Work + Retainage on Stored Material
- **Total Earned Less Retainage** = Total Completed - Total Retainage
- **Current Payment Due** = Total Earned Less Retainage - Less Previous Certificates

### Consulting Invoice
- **Subtotal** = Sum of all line items' thisInvoice
- **GST Amount** = Subtotal × GST Rate
- **Total Now Due** = Subtotal + GST Amount

## Next Steps for Production

### 1. Database Integration
Add database tables for invoices:

```sql
-- Example schema
CREATE TABLE construction_invoices (
  id UUID PRIMARY KEY,
  user_id VARCHAR NOT NULL,
  application_no VARCHAR,
  -- ... other fields
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

CREATE TABLE consulting_invoices (
  id UUID PRIMARY KEY,
  user_id VARCHAR NOT NULL,
  invoice_number VARCHAR,
  -- ... other fields
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

### 2. Add Form Inputs
Create forms to allow users to input their own invoice data instead of using sample data.

### 3. Invoice Management Dashboard
Create a page to list, search, and manage all saved invoices.

### 4. Email Integration
Add functionality to email invoices as PDFs to clients.

### 5. Recurring Invoices
Implement recurring invoice creation for regular clients.

### 6. Payment Tracking
Add payment status tracking and payment reminders.

## Customization

### Styling
All components use Tailwind CSS classes. Modify the classes in the component files to match your brand.

### Logo
Pass `logoUrl` prop to the components to add your company logo.

### Additional Fields
Extend the TypeScript interfaces in `shared/invoiceSchema.ts` to add custom fields.

### Templates
Create additional invoice templates by following the same pattern used in the existing components.

## Support

For questions or issues:
1. Check the TypeScript interfaces in `shared/invoiceSchema.ts`
2. Review the sample data in `InvoiceTemplates.tsx`
3. Inspect the PDF generation code in `lib/pdfGenerator.ts`
4. Review the API endpoints in `server/routes.ts`
