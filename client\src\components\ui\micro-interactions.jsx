import { useState, useEffect, useRef } from "react";
import { cn } from "@/lib/utils";
import "./micro-interactions.css";
export function HoverEffect({ children, className, scale = 1.05, rotation = 5, borderRadius = "0.5rem", shadow = true, }) {
    const [isHovered, setIsHovered] = useState(false);
    return (<div className={cn("mi-hover-effect", isHovered && "hovered", shadow && "shadow", className)} onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
      {children}
    </div>);
}
export function TiltEffect({ children, className, maxTilt = 15, scale = 1.05, glare = false, glareColor = "#ffffff", glareMaxOpacity = 0.8, }) {
    const [transform, setTransform] = useState("");
    const [glareStyle, setGlareStyle] = useState({});
    const ref = useRef(null);
    // Cleanup timeout for performance optimization
    const timeoutRef = useRef();
    const handleMouseMove = (e) => {
        if (!ref.current)
            return;
        // Clear previous timeout
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        const rect = ref.current.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        // Prevent extreme values
        const safeX = Math.max(0, Math.min(rect.width, x));
        const safeY = Math.max(0, Math.min(rect.height, y));
        const rotateX = ((safeY - centerY) / centerY) * -maxTilt;
        const rotateY = ((safeX - centerX) / centerX) * maxTilt;
        setTransform(`perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(${scale})`);
        if (glare) {
            const glareX = (safeX / rect.width) * 100;
            const glareY = (safeY / rect.height) * 100;
            const distance = Math.sqrt(Math.pow(safeX - centerX, 2) + Math.pow(safeY - centerY, 2));
            const maxDistance = Math.sqrt(Math.pow(centerX, 2) + Math.pow(centerY, 2));
            const glareOpacity = (distance / maxDistance) * glareMaxOpacity;
            setGlareStyle({
                background: `radial-gradient(circle at ${glareX}% ${glareY}%, ${glareColor} 0%, transparent 70%)`,
                opacity: Math.min(glareOpacity, glareMaxOpacity),
            });
        }
    };
    const handleMouseLeave = () => {
        // Clear timeout and reset styles
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        timeoutRef.current = setTimeout(() => {
            setTransform("");
            setGlareStyle({});
        }, 50); // Small delay for smooth transition
    };
    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);
    return (<div ref={ref} className={cn("mi-tilt-effect", className)} style={{ transform }} onMouseMove={handleMouseMove} onMouseLeave={handleMouseLeave}>
      {children}
      {glare && (<div className="mi-tilt-glare" style={glareStyle}/>)}
    </div>);
}
export function MagneticEffect({ children, className, strength = 0.3, }) {
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const ref = useRef(null);
    // Throttle mouse move events for performance
    const lastMoveTime = useRef(0);
    const animationFrame = useRef();
    const handleMouseMove = (e) => {
        if (!ref.current)
            return;
        const now = Date.now();
        if (now - lastMoveTime.current < 16) { // ~60fps
            return;
        }
        lastMoveTime.current = now;
        const rect = ref.current.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        // Cancel previous animation frame
        if (animationFrame.current) {
            cancelAnimationFrame(animationFrame.current);
        }
        animationFrame.current = requestAnimationFrame(() => {
            setPosition({
                x: x * strength,
                y: y * strength,
            });
        });
    };
    const handleMouseLeave = () => {
        // Cancel any pending animation frames
        if (animationFrame.current) {
            cancelAnimationFrame(animationFrame.current);
        }
        setPosition({ x: 0, y: 0 });
    };
    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (animationFrame.current) {
                cancelAnimationFrame(animationFrame.current);
            }
        };
    }, []);
    return (<div ref={ref} className={cn("mi-magnetic-effect", className)} style={{
            transform: `translate(${position.x}px, ${position.y}px)`,
        }} onMouseMove={handleMouseMove} onMouseLeave={handleMouseLeave}>
      {children}
    </div>);
}
export function RippleEffect({ children, className, color = "rgba(255, 255, 255, 0.6)", duration = 600, }) {
    const [ripples, setRipples] = useState([]);
    const ref = useRef(null);
    // Use refs to store timeout IDs for cleanup
    const timeoutsRef = useRef(new Map());
    const addRipple = (e) => {
        if (!ref.current)
            return;
        const rect = ref.current.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        const id = Date.now() + Math.random(); // Add randomness to prevent ID conflicts
        setRipples((prevRipples) => [...prevRipples, { id, x, y, size }]);
        // Store timeout for cleanup
        const timeout = setTimeout(() => {
            setRipples((prevRipples) => prevRipples.filter((ripple) => ripple.id !== id));
            timeoutsRef.current.delete(id);
        }, duration);
        timeoutsRef.current.set(id, timeout);
    };
    // Cleanup all timeouts on unmount
    useEffect(() => {
        return () => {
            timeoutsRef.current.forEach((timeout) => clearTimeout(timeout));
            timeoutsRef.current.clear();
        };
    }, []);
    return (<div ref={ref} className={cn("mi-ripple-effect", className)} onClick={addRipple}>
      {children}
      {ripples.map((ripple) => (<span key={ripple.id} className="mi-ripple" style={{
                "--mi-ripple-color": color,
                "--mi-duration": `${duration}ms`,
                width: ripple.size,
                height: ripple.size,
                left: ripple.x,
                top: ripple.y,
            }}/>))}
    </div>);
}
export function TypewriterEffect({ text, className, speed = 50, delay = 0, cursor = true, cursorChar = "|", onComplete, }) {
    const [displayedText, setDisplayedText] = useState("");
    const [showCursor, setShowCursor] = useState(true);
    useEffect(() => {
        let timeout;
        if (delay > 0) {
            timeout = setTimeout(() => {
                startTyping();
            }, delay);
        }
        else {
            startTyping();
        }
        function startTyping() {
            let index = 0;
            setDisplayedText("");
            const interval = setInterval(() => {
                if (index < text.length) {
                    setDisplayedText((prev) => prev + text[index]);
                    index++;
                }
                else {
                    clearInterval(interval);
                    if (onComplete)
                        onComplete();
                }
            }, speed);
        }
        return () => clearTimeout(timeout);
    }, [text, speed, delay, onComplete]);
    useEffect(() => {
        if (!cursor)
            return;
        const interval = setInterval(() => {
            setShowCursor((prev) => !prev);
        }, 500);
        return () => clearInterval(interval);
    }, [cursor]);
    return (<span className={className}>
      {displayedText}
      {cursor && <span className={showCursor ? "opacity-100" : "opacity-0"}>{cursorChar}</span>}
    </span>);
}
export function FloatingElement({ children, className, duration = 3000, distance = 10, delay = 0, }) {
    const ref = useRef(null);
    return (<div ref={ref} className={cn("mi-floating-element", className)} style={{
            "--mi-duration": `${duration}ms`,
            "--mi-distance": `${distance}px`,
            "--mi-delay": `${delay}ms`,
        }}>
      {children}
    </div>);
}
export function PulseEffect({ children, className, intensity = 0.05, duration = 2000, }) {
    const ref = useRef(null);
    return (<div ref={ref} className={cn("mi-pulse-effect", className)} style={{
            "--mi-intensity": intensity.toString(),
            "--mi-duration": `${duration}ms`,
        }}>
      {children}
    </div>);
}
export function StaggeredAnimation({ children, className, stagger = 100, duration = 500, delay = 0, direction = "up", }) {
    const [isVisible, setIsVisible] = useState(false);
    const ref = useRef(null);
    useEffect(() => {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    // Unobserve after first intersection to improve performance
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px' // Start animation a bit before element is visible
        });
        if (ref.current) {
            observer.observe(ref.current);
        }
        return () => {
            if (ref.current) {
                observer.unobserve(ref.current);
            }
        };
    }, []);
    // Apply CSS classes based on direction
    const getDirectionClass = () => {
        switch (direction) {
            case "up": return "mi-staggered-up";
            case "down": return "mi-staggered-down";
            case "left": return "mi-staggered-left";
            case "right": return "mi-staggered-right";
            case "scale": return "mi-staggered-scale";
            case "fade": return "mi-staggered-fade";
            default: return "mi-staggered-up";
        }
    };
    return (<div ref={ref} className={cn("mi-staggered-container", className)}>
      {Array.isArray(children)
            ? children.map((child, index) => (<div key={index} className={cn("mi-staggered-item", getDirectionClass(), isVisible && "visible")} style={{
                    "--mi-duration": `${duration}ms`,
                    "--mi-stagger": `${stagger}ms`,
                    "--mi-delay": `${delay}ms`,
                }}>
              {child}
            </div>))
            : children}
    </div>);
}
