import { defineConfig } from "drizzle-kit";

const DATABASE_URL = process.env.DATABASE_URL;

// Use PostgreSQL if DATABASE_URL is set and not a file URL
const isPostgres = DATABASE_URL && !DATABASE_URL.startsWith("file:");

export default defineConfig({
  out: "./migrations",
  schema: "./shared/schema.ts",
  dialect: isPostgres ? "postgresql" : "sqlite",
  dbCredentials: isPostgres
    ? { url: DATABASE_URL }
    : { url: process.env.DATABASE_PATH || "./data/app.db" },
});
