/**
 * Playwright test for local deployment
 * This script tests:
 * 1. Landing page loads correctly
 * 2. Sign in button works
 * 3. User can start resume optimization
 * 4. User can navigate to resume builder
 */

const { test, expect } = require('@playwright/test');
const { loginAsDevUser } = require('./auth-helper.cjs');

test.describe('ResumeAI Local Deployment', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the local deployment
    await page.goto('/');
  });

  test('Landing page loads correctly', async ({ page }) => {
    // Check if the page title is correct
    await expect(page).toHaveTitle(/ResumeAI/);

    // Check if the main heading is present
    await expect(page.locator('h1')).toContainText(/Build AI-Powered Resumes/i);

    // Check if the sign in button is present using first() to handle multiple elements
    await expect(page.getByTestId('button-sign-in')).toBeVisible();

    // Check if the get started button is present
    await expect(page.getByTestId('button-get-started')).toBeVisible();

    // Check if feature cards are present
    const featureCards = page.locator('section#features .grid > div');
    await expect(featureCards).toHaveCount(6);
  });

  test('Sign in button works and redirects to home', async ({ page }) => {
    // Click the sign in button
    await page.getByTestId('button-sign-in').click();

    // Wait for redirect after auto-login
    await page.waitForURL('/');
    await page.waitForLoadState('networkidle');

    // Check if we're on the authenticated home page
    await expect(page.locator('h1')).toContainText(/Get Your Resume.*Job-Ready/i);

    // Verify authenticated content is visible
    await expect(page.getByRole('button', { name: /Start Optimizing/i })).toBeVisible();
  });

  test('User can start resume optimization process', async ({ page }) => {
    // First sign in
    await loginAsDevUser(page);

    // Click the "Start Optimizing" button
    const startButton = page.getByRole('button', { name: /Start Optimizing/i }).first();
    await expect(startButton).toBeVisible();
    await startButton.click();

    // Wait for navigation to resume builder
    await page.waitForURL(/\/resume-builder\/[a-z0-9-]+/);
    await page.waitForLoadState('networkidle');

    // Check if we're on the resume builder page
    // The builder should have form elements
    await expect(page.locator('form, [role="form"]')).toBeVisible();
  });

  test('Authenticated home page displays correctly', async ({ page }) => {
    // Login first
    await loginAsDevUser(page);

    // Check main elements are present
    await expect(page.locator('h1')).toContainText(/Get Your Resume.*Job-Ready/i);

    // Check "How It Works" section is present
    await expect(page.getByText('How It Works')).toBeVisible();

    // Check feature highlights are present
    await expect(page.getByText('Why Choose Our Resume Optimizer?')).toBeVisible();

    // Check call to action is present
    await expect(page.getByText('Ready to Land Your Dream Job?')).toBeVisible();
  });

  test('Landing page navigation links work', async ({ page }) => {
    // Test Features link
    await page.getByRole('link', { name: 'Features' }).click();
    await page.waitForTimeout(500);
    // Should scroll to features section
    await expect(page.locator('section#features')).toBeInViewport();

    // Test How it works link
    await page.getByRole('link', { name: 'How it works' }).click();
    await page.waitForTimeout(500);
    // Should scroll to how-it-works section
    await expect(page.locator('section#how-it-works')).toBeInViewport();
  });

  test('Multiple call-to-action buttons work', async ({ page }) => {
    // Test the main CTA button
    await page.getByTestId('button-get-started').click();
    await page.waitForURL('/');
    await page.waitForLoadState('networkidle');

    // Should be on authenticated home page
    await expect(page.locator('h1')).toContainText(/Get Your Resume.*Job-Ready/i);
  });
});
