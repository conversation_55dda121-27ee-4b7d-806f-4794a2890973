import { useState, useEffect, useRef } from "react";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { ArrowRight, Wand2, Linkedin, UploadCloud, Sparkles } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
export default function PersonalInfoForm({ data, onDataChange, onNext, }) {
    const { toast } = useToast();
    const fileInputRef = useRef(null);
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        city: "",
        state: "",
        linkedinUrl: "",
    });
    const [aiEnhanced, setAiEnhanced] = useState(true);
    const [industry, setIndustry] = useState("");
    const [briefRole, setBriefRole] = useState("");
    const [briefYears, setBriefYears] = useState("3");
    const [briefSkills, setBriefSkills] = useState("");
    const [briefAccomplishments, setBriefAccomplishments] = useState("");
    const [briefIndustry, setBriefIndustry] = useState("");
    useEffect(() => {
        if (data.personalInfo) {
            setFormData(data.personalInfo);
        }
        if (typeof data.aiEnhanced === "boolean") {
            setAiEnhanced(data.aiEnhanced);
        }
        if (data.industry) {
            setIndustry(data.industry);
        }
    }, [data]);
    const applyDraft = (draft) => {
        if (draft.personalInfo) {
            setFormData(draft.personalInfo);
        }
        if (draft.industry) {
            setIndustry(draft.industry);
        }
        onDataChange({
            ...draft,
            personalInfo: draft.personalInfo ?? data.personalInfo,
            workExperience: draft.workExperience ?? data.workExperience,
            education: draft.education ?? data.education,
            skills: draft.skills ?? data.skills,
            professionalSummary: draft.professionalSummary ?? data.professionalSummary,
            targetJobTitle: draft.targetJobTitle ?? data.targetJobTitle,
            industry: draft.industry ?? data.industry,
        });
    };
    const uploadResumeMutation = useMutation({
        mutationFn: async (file) => {
            const formData = new FormData();
            formData.append("resume", file);
            if (data.id) {
                formData.append("resumeId", data.id);
            }
            const response = await fetch("/api/resumes/upload", {
                method: "POST",
                body: formData,
                credentials: "include",
            });
            if (!response.ok) {
                throw new Error((await response.text()) || "Failed to import resume");
            }
            return (await response.json());
        },
        onSuccess: ({ draft }) => {
            applyDraft(draft);
            toast({
                title: "Imported",
                description: "We parsed your resume and pre-filled the builder.",
            });
        },
        onError: (error) => {
            toast({
                title: "Import failed",
                description: error.message,
                variant: "destructive",
            });
        },
    });
    const generateResumeMutation = useMutation({
        mutationFn: async () => {
            const skills = briefSkills
                .split(/[,\n]/)
                .map((item) => item.trim())
                .filter(Boolean);
            const accomplishments = briefAccomplishments
                .split(/\n|,/)
                .map((item) => item.trim())
                .filter(Boolean);
            const response = await fetch("/api/ai/resume-brief", {
                method: "POST",
                credentials: "include",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    resumeId: data.id,
                    role: briefRole,
                    yearsExperience: Number(briefYears) || 0,
                    topSkills: skills,
                    accomplishments,
                    industry: briefIndustry || industry || undefined,
                    merge: true,
                }),
            });
            if (!response.ok) {
                throw new Error((await response.text()) || "Failed to generate resume draft");
            }
            return (await response.json());
        },
        onSuccess: ({ draft }) => {
            applyDraft(draft);
            toast({
                title: "Draft ready",
                description: "We generated a tailored resume foundation for you.",
            });
        },
        onError: (error) => {
            toast({
                title: "Generation failed",
                description: error.message,
                variant: "destructive",
            });
        },
    });
    const handleInputChange = (field, value) => {
        const newFormData = { ...formData, [field]: value };
        setFormData(newFormData);
        onDataChange({
            personalInfo: newFormData,
            aiEnhanced,
            industry,
        });
    };
    const handleAiEnhancedChange = (checked) => {
        const enabled = checked === true;
        setAiEnhanced(enabled);
        onDataChange({
            personalInfo: formData,
            aiEnhanced: enabled,
            industry,
        });
    };
    const handleIndustryChange = (value) => {
        setIndustry(value);
        onDataChange({
            personalInfo: formData,
            aiEnhanced,
            industry: value,
        });
    };
    const handleFileSelect = (event) => {
        const file = event.target.files?.[0];
        if (file) {
            uploadResumeMutation.mutate(file);
            event.target.value = "";
        }
    };
    const isValidForm = () => {
        return (formData.firstName.trim().length > 1 &&
            formData.lastName.trim().length > 1 &&
            formData.email.trim().length > 3 &&
            formData.phone.trim().length > 6);
    };
    const handleNext = () => {
        if (isValidForm()) {
            onNext();
        }
    };
    const canGenerateBrief = briefRole.trim().length > 2;
    return (<div className="space-y-6">
      <input ref={fileInputRef} type="file" accept=".pdf,.doc,.docx,.txt" className="hidden" onChange={handleFileSelect} aria-label="Upload resume file"/>

      <div className="hidden lg:block">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Personal Information</h1>
          <span className="text-sm text-muted-foreground bg-secondary px-3 py-1 rounded-full">
            Step 1 of 6
          </span>
        </div>
        <p className="text-muted-foreground">
          Start by uploading an existing resume or telling us about your background.
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card className="border-dashed border-accent/50">
          <CardContent className="p-4 space-y-3">
            <div className="flex items-start space-x-3">
              <UploadCloud className="h-5 w-5 text-accent mt-1"/>
              <div>
                <h3 className="text-sm font-semibold">Import an existing resume</h3>
                <p className="text-xs text-muted-foreground">
                  We will extract your details, experience, and skills in seconds.
                </p>
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={() => fileInputRef.current?.click()} disabled={uploadResumeMutation.isPending} data-testid="button-import-resume">
              {uploadResumeMutation.isPending ? "Importing..." : "Upload resume"}
            </Button>
          </CardContent>
        </Card>

        <Card className="border-dashed border-accent/50">
          <CardContent className="p-4 space-y-3">
            <div className="flex items-start space-x-3">
              <Sparkles className="h-5 w-5 text-accent mt-1"/>
              <div>
                <h3 className="text-sm font-semibold">Generate from a quick brief</h3>
                <p className="text-xs text-muted-foreground">
                  Share your role, experience, and wins. AI will draft the rest.
                </p>
              </div>
            </div>
            <div className="grid gap-2">
              <Input placeholder="Target role (e.g., Senior Product Manager)" value={briefRole} onChange={(event) => setBriefRole(event.target.value)}/>
              <div className="grid grid-cols-2 gap-2">
                <Input placeholder="Years experience" value={briefYears} onChange={(event) => setBriefYears(event.target.value)}/>
                <Input placeholder="Industry" value={briefIndustry} onChange={(event) => setBriefIndustry(event.target.value)}/>
              </div>
              <Textarea placeholder="Top skills (comma or new line separated)" rows={2} value={briefSkills} onChange={(event) => setBriefSkills(event.target.value)}/>
              <Textarea placeholder="Signature accomplishments (one per line)" rows={3} value={briefAccomplishments} onChange={(event) => setBriefAccomplishments(event.target.value)}/>
              <Button size="sm" onClick={() => generateResumeMutation.mutate()} disabled={!canGenerateBrief || generateResumeMutation.isPending} variant="outline" data-testid="button-generate-brief">
                {generateResumeMutation.isPending ? "Generating..." : "Generate starter draft"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <form className="space-y-6" onSubmit={(event) => event.preventDefault()}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name *</Label>
            <Input id="firstName" type="text" placeholder="John" value={formData.firstName} onChange={(e) => handleInputChange("firstName", e.target.value)} autoComplete="given-name" data-testid="input-firstName"/>
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name *</Label>
            <Input id="lastName" type="text" placeholder="Doe" value={formData.lastName} onChange={(e) => handleInputChange("lastName", e.target.value)} autoComplete="family-name" data-testid="input-lastName"/>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email Address *</Label>
          <Input id="email" type="email" placeholder="<EMAIL>" value={formData.email} onChange={(e) => handleInputChange("email", e.target.value)} autoComplete="email" data-testid="input-email"/>
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number *</Label>
          <Input id="phone" type="tel" placeholder="+****************" value={formData.phone} onChange={(e) => handleInputChange("phone", e.target.value)} autoComplete="tel" data-testid="input-phone"/>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city">City</Label>
            <Input id="city" type="text" placeholder="San Francisco" value={formData.city} onChange={(e) => handleInputChange("city", e.target.value)} autoComplete="address-level2" data-testid="input-city"/>
          </div>
          <div className="space-y-2">
            <Label htmlFor="state">State</Label>
            <Input id="state" type="text" placeholder="CA" value={formData.state} onChange={(e) => handleInputChange("state", e.target.value)} autoComplete="address-level1" data-testid="input-state"/>
          </div>
          <div className="space-y-2">
            <Label htmlFor="industry">Industry Focus</Label>
            <Input id="industry" type="text" placeholder="Fintech, Healthcare, SaaS..." value={industry} onChange={(e) => handleIndustryChange(e.target.value)} autoComplete="organization-title" data-testid="input-industry"/>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="linkedin">LinkedIn Profile (Optional)</Label>
          <div className="relative">
            <Linkedin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground"/>
            <Input id="linkedin" type="url" placeholder="linkedin.com/in/johndoe" value={formData.linkedinUrl} onChange={(e) => handleInputChange("linkedinUrl", e.target.value)} className="pl-10" autoComplete="url" data-testid="input-linkedin"/>
          </div>
        </div>

        <Card className="border-accent/20 bg-accent/5">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-1">
                <Wand2 className="h-4 w-4 text-accent"/>
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-foreground">AI Enhancement</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  Let AI refine contact formatting and surface recruiter-friendly insights.
                </p>
                <div className="flex items-center space-x-2 mt-3">
                  <Checkbox id="ai-enhanced" checked={aiEnhanced} onCheckedChange={handleAiEnhancedChange} data-testid="checkbox-ai-enhanced"/>
                  <Label htmlFor="ai-enhanced" className="text-sm">
                    Enable AI optimization
                  </Label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>

      <div className="flex justify-between items-center pt-6 border-t border-border">
        <Button variant="ghost" disabled data-testid="button-previous">
          Previous
        </Button>

        <Button onClick={handleNext} disabled={!isValidForm()} data-testid="button-next">
          Continue
          <ArrowRight className="h-4 w-4 ml-2"/>
        </Button>
      </div>
    </div>);
}
