/**
 * Authentication helper for Playwright tests
 * Uses local login fallback when ALLOW_LOCAL_LOGIN=true
 */

/**
 * <PERSON><PERSON> as a dev user using the local login fallback
 * @param {import('@playwright/test').Page} page
 */
async function loginAsDevUser(page) {
  // Navigate to login endpoint which will automatically log us in
  // with the dev user when ALLOW_LOCAL_LOGIN=true
  await page.goto('/api/login');

  // Wait for redirection to complete (redirects to /)
  await page.waitForURL('/');

  // Wait for authentication to be processed
  await page.waitForLoadState('networkidle');

  // Additional wait to ensure app state is updated
  await page.waitForTimeout(1000);
}

/**
 * Check if user is authenticated
 * @param {import('@playwright/test').Page} page
 * @returns {Promise<boolean>}
 */
async function isAuthenticated(page) {
  try {
    const response = await page.request.get('/api/user', {
      failOnStatusCode: false
    });
    return response.ok();
  } catch (error) {
    return false;
  }
}

module.exports = {
  loginAsDevUser,
  isAuthenticated,
};
