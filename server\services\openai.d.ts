import type { PersonalInfo, WorkExperience, Skill, Education, Resume } from "@shared/schema";
export interface JobAnalysisResult {
    keywords: string[];
    requirements: string[];
    suggestedImprovements: string[];
    matchScore: string;
}
export declare function analyzeJobDescription(jobDescription: string): Promise<JobAnalysisResult>;
export declare function generateProfessionalSummary(personalInfo: PersonalInfo, experience: WorkExperience[], skills: Skill[], targetJob?: string, jobKeywords?: string[]): Promise<string>;
export declare function enhanceExperienceDescription(experience: WorkExperience, jobKeywords?: string[]): Promise<string>;
export declare function suggestSkillsForJob(jobDescription: string, currentSkills: string[]): Promise<string[]>;
export interface ResumeDraft {
    personalInfo?: PersonalInfo;
    professionalSummary?: string;
    workExperience?: WorkExperience[];
    education?: Education[];
    skills?: Skill[];
    targetJobTitle?: string;
    industry?: string;
}
export declare function parseResumeFromText(rawText: string): Promise<ResumeDraft>;
export interface ResumeBriefInput {
    role: string;
    yearsExperience: number;
    topSkills: string[];
    accomplishments: string[];
    industry?: string;
}
export declare function generateResumeFromBrief(brief: ResumeBriefInput): Promise<ResumeDraft>;
export declare function synthesizeResumeWithExisting(current: Partial<Resume>, draft: ResumeDraft): Partial<Resume>;
//# sourceMappingURL=openai.d.ts.map