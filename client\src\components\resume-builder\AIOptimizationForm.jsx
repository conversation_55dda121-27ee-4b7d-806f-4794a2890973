import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, ArrowLeft, Loader2, CheckCircle, Sparkles } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
const OPTIMIZATION_STEPS = [
    { label: "Analyzing job requirements", duration: 2000 },
    { label: "Extracting key keywords", duration: 1500 },
    { label: "Optimizing professional summary", duration: 2500 },
    { label: "Enhancing work experience", duration: 3000 },
    { label: "Matching skills to job posting", duration: 2000 },
    { label: "Finalizing ATS-optimized resume", duration: 1500 },
];
export default function AIOptimizationForm({ data, onDataChange, onNext, onPrevious, resumeId, }) {
    const { toast } = useToast();
    const [currentStepIndex, setCurrentStepIndex] = useState(0);
    const [isOptimizing, setIsOptimizing] = useState(false);
    const [isComplete, setIsComplete] = useState(false);
    // Analyze job description mutation
    const analyzeJobMutation = useMutation({
        mutationFn: async () => {
            return await apiRequest('POST', '/api/jobs/analyze', {
                jobDescription: data.jobDescription,
                resumeId,
            });
        },
        onSuccess: (analysis) => {
            onDataChange({
                extractedKeywords: analysis.keywords,
            });
        },
        onError: (error) => {
            if (isUnauthorizedError(error)) {
                window.location.href = "/api/login";
                return;
            }
            toast({
                title: "Error",
                description: "Failed to analyze job description.",
                variant: "destructive",
            });
        },
    });
    // Generate professional summary mutation
    const generateSummaryMutation = useMutation({
        mutationFn: async () => {
            return await apiRequest('POST', '/api/ai/professional-summary', {
                personalInfo: data.personalInfo,
                experience: data.workExperience || [],
                skills: data.skills || [],
                targetJob: data.targetJobTitle,
                jobKeywords: data.extractedKeywords || [],
            });
        },
        onSuccess: (result) => {
            onDataChange({
                professionalSummary: result.summary,
            });
        },
        onError: (error) => {
            if (isUnauthorizedError(error)) {
                window.location.href = "/api/login";
                return;
            }
            console.error("Failed to generate summary:", error);
        },
    });
    // Suggest skills mutation
    const suggestSkillsMutation = useMutation({
        mutationFn: async () => {
            // Safely derive current skill names (defensive against malformed data)
            const currentSkillsArr = Array.isArray(data.skills) ? data.skills : [];
            const currentSkillNames = currentSkillsArr.map((s) => s?.name).filter(Boolean);
            return await apiRequest("POST", "/api/ai/suggest-skills", {
                jobDescription: data.jobDescription,
                currentSkills: currentSkillNames,
            });
        },
        onSuccess: (result) => {
            const currentSkillsArr = Array.isArray(data.skills) ? data.skills : [];
            const newSkills = (result.suggestedSkills || [])
                .slice(0, 10)
                .map((name, index) => ({
                id: `skill-suggested-${index}`,
                name,
                level: "proficient",
                category: "suggested",
            }));
            onDataChange({
                skills: [...currentSkillsArr, ...newSkills],
            });
        },
        onError: (error) => {
            if (isUnauthorizedError(error)) {
                window.location.href = "/api/login";
                return;
            }
            console.error("Failed to suggest skills:", error);
        },
    });
    // Run optimization
    const runOptimization = async () => {
        setIsOptimizing(true);
        setCurrentStepIndex(0);
        try {
            // Step through the visual progress
            for (let i = 0; i < OPTIMIZATION_STEPS.length; i++) {
                setCurrentStepIndex(i);
                // Execute actual AI calls at specific steps
                if (i === 1 && data.jobDescription) {
                    await analyzeJobMutation.mutateAsync();
                }
                if (i === 2 && data.personalInfo) {
                    await generateSummaryMutation.mutateAsync();
                }
                if (i === 4 && data.jobDescription) {
                    await suggestSkillsMutation.mutateAsync();
                }
                // Wait for step duration
                await new Promise((resolve) => setTimeout(resolve, OPTIMIZATION_STEPS[i].duration));
            }
            // Mark resume as complete
            if (resumeId) {
                await apiRequest('PUT', `/api/resumes/${resumeId}`, {
                    status: 'complete',
                    aiEnhanced: true,
                });
            }
            setIsComplete(true);
            toast({
                title: "Optimization Complete!",
                description: "Your resume has been optimized for the job posting.",
            });
        }
        catch (error) {
            setIsOptimizing(false);
            toast({
                title: "Optimization Failed",
                description: "Please try again or go back to edit your information.",
                variant: "destructive",
            });
        }
    };
    // Auto-start optimization when component mounts
    useEffect(() => {
        if (!isOptimizing && !isComplete && data.jobDescription) {
            runOptimization();
        }
    }, []);
    return (<div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">AI Optimization</h2>
        <p className="text-muted-foreground">
          Our AI is tailoring your resume to match the job posting
        </p>
      </div>

      <Card>
        <CardContent className="p-8">
          {!isComplete ? (<div className="space-y-6">
              <div className="flex items-center justify-center mb-8">
                <div className="relative">
                  <Sparkles className="h-16 w-16 text-primary animate-pulse"/>
                  <div className="absolute inset-0 bg-primary/20 rounded-full blur-xl"></div>
                </div>
              </div>

              <div className="space-y-4">
                {OPTIMIZATION_STEPS.map((step, index) => (<div key={index} className={`flex items-center space-x-3 p-3 rounded-lg transition-all ${index === currentStepIndex
                    ? "bg-primary/10 border-2 border-primary"
                    : index < currentStepIndex
                        ? "bg-green-50 border-2 border-green-200"
                        : "bg-muted border-2 border-transparent"}`}>
                    {index < currentStepIndex ? (<CheckCircle className="h-5 w-5 text-green-600"/>) : index === currentStepIndex ? (<Loader2 className="h-5 w-5 text-primary animate-spin"/>) : (<div className="h-5 w-5 rounded-full border-2 border-muted-foreground/30"></div>)}
                    <span className={`text-sm font-medium ${index <= currentStepIndex
                    ? "text-foreground"
                    : "text-muted-foreground"}`}>
                      {step.label}
                    </span>
                  </div>))}
              </div>

              <div className="pt-4">
                <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
                  <span>Progress</span>
                  <span>
                    {Math.round(((currentStepIndex + 1) / OPTIMIZATION_STEPS.length) * 100)}
                    %
                  </span>
                </div>
                <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                  <div className="h-full bg-primary transition-all duration-500" style={{
                width: `${((currentStepIndex + 1) / OPTIMIZATION_STEPS.length) * 100}%`,
            }}/>
                </div>
              </div>
            </div>) : (<div className="text-center py-8">
              <CheckCircle className="h-20 w-20 text-green-600 mx-auto mb-4"/>
              <h3 className="text-2xl font-bold mb-2 text-green-700">
                Optimization Complete!
              </h3>
              <p className="text-muted-foreground mb-6">
                Your resume has been tailored to match the job requirements and optimized
                for ATS systems.
              </p>
              <div className="flex gap-4 justify-center">
                <Button variant="outline" onClick={onPrevious}>
                  <ArrowLeft className="mr-2 h-4 w-4"/>
                  Go Back
                </Button>
                <Button onClick={onNext} size="lg">
                  View Resume
                  <ArrowRight className="ml-2 h-4 w-4"/>
                </Button>
              </div>
            </div>)}
        </CardContent>
      </Card>

      {/* Navigation - only show if not optimizing */}
      {!isOptimizing && !isComplete && (<div className="flex justify-between">
          <Button variant="outline" onClick={onPrevious}>
            <ArrowLeft className="mr-2 h-4 w-4"/>
            Previous
          </Button>
          <Button onClick={() => runOptimization()} disabled={!data.jobDescription}>
            Start Optimization
            <ArrowRight className="ml-2 h-4 w-4"/>
          </Button>
        </div>)}
    </div>);
}
