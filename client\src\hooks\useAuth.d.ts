export interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    profileImageUrl: string;
}
export interface AuthState {
    user: User | null;
    isLoading: boolean;
    isAuthenticated: boolean;
}
export declare function useAuth(): AuthState;
export declare function signInWithGoogle(): Promise<void>;
export declare function signInWithDemo(): Promise<void>;
export declare function signOut(): Promise<void>;
//# sourceMappingURL=useAuth.d.ts.map