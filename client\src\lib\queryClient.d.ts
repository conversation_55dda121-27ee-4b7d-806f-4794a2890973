import { QueryClient, QueryFunction } from "@tanstack/react-query";
export declare function apiRequest(method: string, url: string, data?: unknown | undefined): Promise<any>;
type UnauthorizedBehavior = "returnNull" | "throw";
export declare function getQueryFn<T>({ on401 }: {
    on401: UnauthorizedBehavior;
}): QueryFunction<T>;
export declare const queryClient: QueryClient;
export {};
//# sourceMappingURL=queryClient.d.ts.map