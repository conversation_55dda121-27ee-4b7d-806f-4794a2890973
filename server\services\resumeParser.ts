import { parseUploadedResume as parseEnhancedResume, getSupportedFileTypes, validateFileSize } from "./enhancedResumeParser.js";
import type { Resume, PersonalInfo, WorkExperience, Education, Skill } from "@shared/schema";
import type { Express } from "express";

interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  buffer: Buffer;
}

export type ResumeDraft = {
  personalInfo?: PersonalInfo;
  professionalSummary?: string;
  workExperience?: WorkExperience[];
  education?: Education[];
  skills?: Skill[];
  targetJobTitle?: string;
  industry?: string;
};

function ensureIds<T extends { id?: string }>(items: T[] | undefined, prefix: string): T[] | undefined {
  if (!items) return undefined;

  // If the incoming value is not an array, try to coerce it into one so
  // downstream code that expects arrays doesn't crash (defensive).
  // - If it's a single object, wrap it in an array.
  // - Otherwise, return undefined to indicate no valid items.
  if (!Array.isArray(items)) {
    if (typeof items === "object" && items !== null) {
      // wrap single object into array
      items = [items as T];
    } else {
      return undefined;
    }
  }

  return items.map((item, index) => ({
    ...item,
    id: item.id ?? `${prefix}-${index + 1}`,
  }));
}

const toOptionalString = (value: unknown): string | undefined => {
  if (typeof value === "string" && value.trim().length > 0) {
    return value;
  }
  return undefined;
};

export function normalizeResumeDraft(draft: ResumeDraft | Partial<Resume>): ResumeDraft {
  const personalInfo = (draft as ResumeDraft).personalInfo ?? (draft as Resume).personalInfo;
  const workExperience = (draft as ResumeDraft).workExperience ?? (draft as Resume).workExperience;
  const education = (draft as ResumeDraft).education ?? (draft as Resume).education;
  const skills = (draft as ResumeDraft).skills ?? (draft as Resume).skills;

  return {
    personalInfo: personalInfo
      ? {
          firstName: toOptionalString((personalInfo as any).firstName) ?? "",
          lastName: toOptionalString((personalInfo as any).lastName) ?? "",
          email: toOptionalString((personalInfo as any).email) ?? "",
          phone: toOptionalString((personalInfo as any).phone) ?? "",
          city: toOptionalString((personalInfo as any).city) ?? "",
          state: toOptionalString((personalInfo as any).state) ?? "",
          linkedinUrl: toOptionalString((personalInfo as any).linkedinUrl),
        }
      : undefined,
    professionalSummary:
      toOptionalString((draft as ResumeDraft).professionalSummary ?? (draft as Resume).professionalSummary),
    workExperience: ensureIds(workExperience as any, "exp") as any,
    education: ensureIds(education as any, "edu") as any,
    skills: ensureIds(skills as any, "skill") as any,
    targetJobTitle: toOptionalString(
      (draft as ResumeDraft).targetJobTitle ?? (draft as Resume).targetJobTitle,
    ),
    industry: toOptionalString((draft as ResumeDraft).industry ?? (draft as Resume).industry),
  };
}

export async function parseUploadedResume(file: UploadedFile): Promise<ResumeDraft> {
  try {
    // Validate file size (max 10MB)
    validateFileSize(file);
    
    // Use enhanced parser
    const { resume } = await parseEnhancedResume(file);
    return resume;
  } catch (error) {
    console.error("Error parsing uploaded resume:", error);
    throw new Error(`Failed to parse resume: ${(error as Error).message}`);
  }
}

// Export utility functions
export { getSupportedFileTypes } from "./enhancedResumeParser.js";
