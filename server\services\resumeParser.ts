import pdfParse from "pdf-parse";
import { extractRawText } from "mammoth";
import { parseResumeFromText, type ResumeDraft } from "./openai";
import type { Resume } from "@shared/schema";

const TEXT_MIME_TYPES = ["text/plain", "text/markdown"];
const PDF_MIME_TYPES = ["application/pdf"];
const DOCX_MIME_TYPES = [
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "application/msword",
];

type UploadedFile = Express.Multer.File;

function ensureIds<T extends { id?: string }>(items: T[] | undefined, prefix: string): T[] | undefined {
  if (!items) return undefined;
  return items.map((item, index) => ({
    ...item,
    id: item.id ?? `${prefix}-${index + 1}`,
  }));
}

const toOptionalString = (value: unknown): string | undefined => {
  if (typeof value === "string" && value.trim().length > 0) {
    return value;
  }
  return undefined;
};

async function bufferToText(file: UploadedFile): Promise<string> {
  if (PDF_MIME_TYPES.includes(file.mimetype)) {
    const result = await pdfParse(file.buffer);
    return result.text;
  }

  if (DOCX_MIME_TYPES.includes(file.mimetype)) {
    const result = await extractRawText({ buffer: file.buffer });
    return result.value;
  }

  if (TEXT_MIME_TYPES.includes(file.mimetype)) {
    return file.buffer.toString("utf8");
  }

  throw new Error(`Unsupported file type: ${file.mimetype}`);
}

export function normalizeResumeDraft(draft: ResumeDraft | Partial<Resume>): ResumeDraft {
  const personalInfo = (draft as ResumeDraft).personalInfo ?? (draft as Resume).personalInfo;
  const workExperience = (draft as ResumeDraft).workExperience ?? (draft as Resume).workExperience;
  const education = (draft as ResumeDraft).education ?? (draft as Resume).education;
  const skills = (draft as ResumeDraft).skills ?? (draft as Resume).skills;

  return {
    personalInfo: personalInfo
      ? {
          firstName: toOptionalString((personalInfo as any).firstName) ?? "",
          lastName: toOptionalString((personalInfo as any).lastName) ?? "",
          email: toOptionalString((personalInfo as any).email) ?? "",
          phone: toOptionalString((personalInfo as any).phone) ?? "",
          city: toOptionalString((personalInfo as any).city) ?? "",
          state: toOptionalString((personalInfo as any).state) ?? "",
          linkedinUrl: toOptionalString((personalInfo as any).linkedinUrl),
        }
      : undefined,
    professionalSummary:
      toOptionalString((draft as ResumeDraft).professionalSummary ?? (draft as Resume).professionalSummary),
    workExperience: ensureIds(workExperience as any, "exp") as any,
    education: ensureIds(education as any, "edu") as any,
    skills: ensureIds(skills as any, "skill") as any,
    targetJobTitle: toOptionalString(
      (draft as ResumeDraft).targetJobTitle ?? (draft as Resume).targetJobTitle,
    ),
    industry: toOptionalString((draft as ResumeDraft).industry ?? (draft as Resume).industry),
  };
}

export async function parseUploadedResume(file: UploadedFile): Promise<ResumeDraft> {
  const rawText = await bufferToText(file);
  if (!rawText.trim()) {
    throw new Error("Uploaded resume is empty");
  }

  const draft = await parseResumeFromText(rawText);
  return normalizeResumeDraft(draft);
}
