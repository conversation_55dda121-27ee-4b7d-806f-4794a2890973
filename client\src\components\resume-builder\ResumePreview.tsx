import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Palette, ZoomIn, Download } from "lucide-react";
import type { Resume, PersonalInfo, WorkExperience, Education, Skill } from "@shared/schema";

interface ResumePreviewProps {
  resumeData: Partial<Resume>;
  currentStep: number;
}

export default function ResumePreview({ resumeData, currentStep }: ResumePreviewProps) {
  const personalInfo = resumeData.personalInfo as PersonalInfo;
  const workExperience = (resumeData.workExperience as WorkExperience[]) || [];
  const education = (resumeData.education as Education[]) || [];
  const skills = (resumeData.skills as Skill[]) || [];
  const extractedKeywords = (resumeData.extractedKeywords as string[]) || [];

  const getFullName = () => {
    if (!personalInfo) return "Your Name";
    return `${personalInfo.firstName || ""} ${personalInfo.lastName || ""}`.trim() || "Your Name";
  };

  const getContactInfo = () => {
    if (!personalInfo) return [];
    const contact = [];
    if (personalInfo.email) contact.push(personalInfo.email);
    if (personalInfo.phone) contact.push(personalInfo.phone);
    if (personalInfo.city && personalInfo.state) {
      contact.push(`${personalInfo.city}, ${personalInfo.state}`);
    }
    return contact;
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
  };

  const formatDescription = (description: string) => {
    // Convert description to bullet points if not already formatted
    if (!description.includes('•') && !description.includes('\n')) {
      return `• ${description}`;
    }
    return description.split('\n').map(line => line.trim()).filter(line => line).join('\n');
  };

  const getSkillsByCategory = () => {
    const categories: { [key: string]: Skill[] } = {};
    skills.forEach(skill => {
      if (!categories[skill.category]) {
        categories[skill.category] = [];
      }
      categories[skill.category].push(skill);
    });
    return categories;
  };

  const getProgressMessage = () => {
    const messages = [
      "Complete your personal information to see it here",
      "Add a professional summary to make a strong first impression",
      "Your work experience will showcase your career progression",
      "Education and skills demonstrate your qualifications",
      "Job targeting will optimize your resume with relevant keywords",
      "Your complete, ATS-optimized resume is ready for download"
    ];
    return messages[currentStep - 1] || "Building your professional resume...";
  };

  return (
    <div className="p-6 lg:p-8">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Live Preview</h2>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" data-testid="button-template-selector">
              <Palette className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" data-testid="button-zoom">
              <ZoomIn className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          See how your resume looks as you build it
        </p>
      </div>

      {/* Resume Preview */}
      <Card className="border border-border rounded-xl shadow-lg overflow-hidden mb-6">
        <div 
          className="p-8 bg-white text-gray-900 transform scale-90 origin-top-left" 
          style={{ width: '111%', marginBottom: '-8%' }}
          data-testid="resume-preview"
        >
          <div className="space-y-6">
            {/* Header Section */}
            <div className="text-center border-b-2 border-gray-200 pb-6">
              <h1 className="text-3xl font-bold text-gray-900 mb-2" data-testid="preview-name">
                {getFullName()}
              </h1>
              {getContactInfo().length > 0 && (
                <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-600">
                  {getContactInfo().map((info, index) => (
                    <span key={index} data-testid={`preview-contact-${index}`}>
                      {info}
                    </span>
                  ))}
                </div>
              )}
              {personalInfo?.linkedinUrl && (
                <div className="mt-2">
                  <span className="text-sm text-blue-600" data-testid="preview-linkedin">
                    {personalInfo.linkedinUrl}
                  </span>
                </div>
              )}
            </div>

            {/* Professional Summary */}
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-3 border-b border-gray-300 pb-1">
                Professional Summary
              </h2>
              {resumeData.professionalSummary ? (
                <p className="text-gray-700 leading-relaxed" data-testid="preview-summary">
                  {resumeData.professionalSummary}
                </p>
              ) : (
                <div className="space-y-2 text-gray-400 italic">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-4/6"></div>
                  <p className="text-xs text-gray-400 mt-2">
                    ✨ {currentStep >= 2 ? "Add your professional summary" : "Professional summary will appear here"}
                  </p>
                </div>
              )}
            </div>

            {/* Work Experience */}
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-3 border-b border-gray-300 pb-1">
                Professional Experience
              </h2>
              {workExperience.length > 0 ? (
                <div className="space-y-4">
                  {workExperience.map((exp, index) => (
                    <div key={exp.id || index} className="space-y-2" data-testid={`preview-experience-${index}`}>
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-800">
                            {exp.position || "Position Title"}
                          </h3>
                          <p className="text-gray-600 font-medium">
                            {exp.company || "Company Name"}
                          </p>
                        </div>
                        <span className="text-sm text-gray-500">
                          {exp.startDate ? formatDate(exp.startDate) : "Start"} - {
                            exp.current ? "Present" : (exp.endDate ? formatDate(exp.endDate) : "End")
                          }
                        </span>
                      </div>
                      {exp.description && (
                        <div className="text-gray-700 text-sm leading-relaxed whitespace-pre-line">
                          {formatDescription(exp.description)}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4 text-gray-400 italic">
                  <div className="space-y-2">
                    <div className="h-5 bg-gray-200 rounded animate-pulse w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2"></div>
                    <div className="space-y-1">
                      <div className="h-3 bg-gray-200 rounded animate-pulse"></div>
                      <div className="h-3 bg-gray-200 rounded animate-pulse w-5/6"></div>
                    </div>
                  </div>
                  <p className="text-xs text-gray-400 mt-2">
                    {currentStep >= 3 ? "Add your work experience" : "Work experience will appear here"}
                  </p>
                </div>
              )}
            </div>

            {/* Education */}
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-3 border-b border-gray-300 pb-1">
                Education
              </h2>
              {education.length > 0 ? (
                <div className="space-y-3">
                  {education.map((edu, index) => (
                    <div key={edu.id || index} className="space-y-1" data-testid={`preview-education-${index}`}>
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-semibold text-gray-800">
                            {edu.degree} in {edu.field}
                          </h3>
                          <p className="text-gray-600">{edu.institution}</p>
                        </div>
                        <span className="text-sm text-gray-500">
                          {edu.graduationDate ? formatDate(edu.graduationDate) : "Graduation Date"}
                        </span>
                      </div>
                      {edu.gpa && (
                        <p className="text-sm text-gray-600">GPA: {edu.gpa}</p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-2 text-gray-400 italic">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-3/5"></div>
                  <div className="h-3 bg-gray-200 rounded animate-pulse w-2/5"></div>
                  <p className="text-xs text-gray-400 mt-2">
                    {currentStep >= 4 ? "Add your education" : "Education will appear here"}
                  </p>
                </div>
              )}
            </div>

            {/* Skills */}
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-3 border-b border-gray-300 pb-1">
                Skills
              </h2>
              {skills.length > 0 ? (
                <div className="space-y-3" data-testid="preview-skills">
                  {Object.entries(getSkillsByCategory()).map(([category, categorySkills]) => (
                    <div key={category}>
                      <h4 className="text-sm font-medium text-gray-600 mb-2 capitalize">
                        {category === "technical" ? "Technical Skills" : 
                         category === "soft" ? "Soft Skills" :
                         category === "language" ? "Languages" :
                         category}
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {categorySkills.map((skill, index) => (
                          <span
                            key={skill.id || index}
                            className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                            data-testid={`preview-skill-${skill.id}`}
                          >
                            {skill.name}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-wrap gap-2">
                  <span className="h-6 w-20 bg-gray-200 rounded animate-pulse"></span>
                  <span className="h-6 w-16 bg-gray-200 rounded animate-pulse"></span>
                  <span className="h-6 w-24 bg-gray-200 rounded animate-pulse"></span>
                  <p className="text-xs text-gray-400 mt-2 w-full">
                    {currentStep >= 4 ? "Add your skills" : "Skills will appear here"}
                  </p>
                </div>
              )}
            </div>

            {/* Job Targeting Keywords (if available) */}
            {extractedKeywords.length > 0 && (
              <div>
                <h2 className="text-xl font-bold text-gray-900 mb-3 border-b border-gray-300 pb-1">
                  Optimized Keywords
                </h2>
                <div className="flex flex-wrap gap-2" data-testid="preview-keywords">
                  {extractedKeywords.slice(0, 10).map((keyword, index) => (
                    <Badge key={index} variant="outline" className="text-xs border-green-300 text-green-700">
                      {keyword}
                    </Badge>
                  ))}
                  {extractedKeywords.length > 10 && (
                    <Badge variant="outline" className="text-xs border-green-300 text-green-700">
                      +{extractedKeywords.length - 10} more keywords
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-green-600 mt-2">
                  ✓ Resume optimized with job-specific keywords
                </p>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Template Selection */}
      <div>
        <h3 className="text-sm font-medium mb-3">Choose Template</h3>
        <div className="grid grid-cols-3 gap-3">
          <div className="aspect-[3/4] bg-card border-2 border-primary rounded-lg p-2 cursor-pointer" data-testid="template-modern">
            <div className="w-full h-full bg-gradient-to-b from-blue-50 to-white rounded border flex items-center justify-center">
              <span className="text-xs font-medium text-primary">Modern</span>
            </div>
          </div>
          <div className="aspect-[3/4] bg-card border border-border rounded-lg p-2 cursor-pointer opacity-60" data-testid="template-classic">
            <div className="w-full h-full bg-gradient-to-b from-green-50 to-white rounded border flex items-center justify-center">
              <span className="text-xs text-gray-500">Classic</span>
            </div>
          </div>
          <div className="aspect-[3/4] bg-card border border-border rounded-lg p-2 cursor-pointer opacity-60" data-testid="template-creative">
            <div className="w-full h-full bg-gradient-to-b from-purple-50 to-white rounded border flex items-center justify-center">
              <span className="text-xs text-gray-500">Creative</span>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Message */}
      <div className="mt-6 p-4 bg-accent/5 border border-accent/20 rounded-lg">
        <p className="text-sm text-accent-foreground text-center">
          {getProgressMessage()}
        </p>
      </div>
    </div>
  );
}
