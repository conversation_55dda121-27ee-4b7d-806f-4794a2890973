import type { Resume, PersonalInfo, WorkExperience, Education, Skill } from "@shared/schema";
interface UploadedFile {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    size: number;
    destination: string;
    filename: string;
    path: string;
    buffer: Buffer;
}
export type ResumeDraft = {
    personalInfo?: PersonalInfo;
    professionalSummary?: string;
    workExperience?: WorkExperience[];
    education?: Education[];
    skills?: Skill[];
    targetJobTitle?: string;
    industry?: string;
};
export declare function normalizeResumeDraft(draft: ResumeDraft | Partial<Resume>): ResumeDraft;
export declare function parseUploadedResume(file: UploadedFile): Promise<ResumeDraft>;
export { getSupportedFileTypes } from "./enhancedResumeParser.js";
//# sourceMappingURL=resumeParser.d.ts.map