# Production Environment Variables for try2work.com
# Add these to your Vercel project settings at:
# https://vercel.com/your-team/try2work/settings/environment-variables

# Environment (auto-set by Vercel)
NODE_ENV=production

# Database - PostgreSQL from Neon (REQUIRED)
# Get from: https://neon.tech
# Format: ********************************************************
DATABASE_URL=postgresql://your-user:<EMAIL>/try2work?sslmode=require

# Sessions (REQUIRED)
# Generate with: openssl rand -hex 32
# Use a DIFFERENT secret than your local development
SESSION_SECRET=your-production-session-secret-here-min-32-chars

# Application URL (REQUIRED)
APP_URL=https://try2work.com

# OpenAI API Key (REQUIRED)
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# Google AI API Key (REQUIRED)
# Get from: https://makersuite.google.com/app/apikey
GOOGLE_AI_API_KEY=AIza-your-google-ai-api-key-here

# Stripe Configuration (REQUIRED)
# Get from: https://dashboard.stripe.com/apikeys
# For production, use LIVE keys (sk_live_... and pk_live_...)
STRIPE_SECRET_KEY=sk_live_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-publishable-key

# Stripe Webhook Secret (REQUIRED)
# Get from: https://dashboard.stripe.com/webhooks
# Configure webhook endpoint: https://try2work.com/api/stripe/webhook
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# Optional: Only if using Replit Auth (not recommended for production)
# REPL_ID=your-repl-id
# ISSUER_URL=https://replit.com/oidc
# REPLIT_DOMAINS=try2work.com,www.try2work.com
