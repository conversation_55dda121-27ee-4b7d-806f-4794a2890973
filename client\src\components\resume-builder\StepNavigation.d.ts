interface Step {
    id: number;
    title: string;
    description: string;
}
interface StepNavigationProps {
    steps: Step[];
    currentStep: number;
    onStepChange: (step: number) => void;
    progress: number;
}
export default function StepNavigation({ steps, currentStep, onStepChange, progress }: StepNavigationProps): import("react").JSX.Element;
export {};
//# sourceMappingURL=StepNavigation.d.ts.map