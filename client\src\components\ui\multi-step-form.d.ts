import { ReactNode } from "react";
interface Step {
    id: number;
    title: string;
    description?: string;
}
interface MultiStepFormProps {
    steps: Step[];
    currentStep: number;
    children: ReactNode;
    className?: string;
}
interface StepIndicatorProps {
    steps: Step[];
    currentStep: number;
    onStepClick?: (stepId: number) => void;
    className?: string;
}
export declare function MultiStepForm({ steps, currentStep, children, className }: MultiStepFormProps): import("react").JSX.Element;
export declare function StepIndicator({ steps, currentStep, onStepClick, className }: StepIndicatorProps): import("react").JSX.Element;
export declare function StepContent({ children, className }: {
    children: ReactNode;
    className?: string;
}): import("react").JSX.Element;
export declare function StepNavigation({ onPrevious, onNext, previousLabel, nextLabel, isPreviousDisabled, isNextDisabled, className }: {
    onPrevious?: () => void;
    onNext?: () => void;
    previousLabel?: string;
    nextLabel?: string;
    isPreviousDisabled?: boolean;
    isNextDisabled?: boolean;
    className?: string;
}): import("react").JSX.Element;
export {};
//# sourceMappingURL=multi-step-form.d.ts.map