import type { Resume } from "@shared/schema";
interface EducationSkillsFormProps {
    data: Partial<Resume>;
    onDataChange: (data: Partial<Resume>) => void;
    onNext: () => void;
    onPrevious: () => void;
}
export default function EducationSkillsForm({ data, onDataChange, onNext, onPrevious, }: EducationSkillsFormProps): import("react").JSX.Element;
export {};
//# sourceMappingURL=EducationSkillsForm.d.ts.map