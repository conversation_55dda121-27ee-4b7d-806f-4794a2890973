import { type User, type UpsertUser, type Resume, type InsertResume, type UpdateResume, type JobAnalysis, type InsertJobAnalysis } from "@shared/schema";
export interface IStorage {
    getUser(id: string): Promise<User | undefined>;
    upsertUser(user: UpsertUser): Promise<User>;
    getUserResumes(userId: string): Promise<Resume[]>;
    getResume(id: string, userId: string): Promise<Resume | undefined>;
    createResume(resume: InsertResume): Promise<Resume>;
    updateResume(resume: UpdateResume): Promise<Resume>;
    deleteResume(id: string, userId: string): Promise<void>;
    createJobAnalysis(analysis: InsertJobAnalysis): Promise<JobAnalysis>;
    getJobAnalysisByResumeId(resumeId: string): Promise<JobAnalysis | undefined>;
}
export declare class DatabaseStorage implements IStorage {
    getUser(id: string): Promise<User | undefined>;
    upsertUser(userData: UpsertUser): Promise<User>;
    getUserResumes(userId: string): Promise<Resume[]>;
    getResume(id: string, userId: string): Promise<Resume | undefined>;
    createResume(resume: InsertResume): Promise<Resume>;
    updateResume(resume: UpdateResume): Promise<Resume>;
    deleteResume(id: string, userId: string): Promise<void>;
    createJobAnalysis(analysis: InsertJobAnalysis): Promise<JobAnalysis>;
    getJobAnalysisByResumeId(resumeId: string): Promise<JobAnalysis | undefined>;
}
export declare const storage: DatabaseStorage;
//# sourceMappingURL=storage.d.ts.map