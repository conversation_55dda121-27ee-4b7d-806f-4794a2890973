# Environment
NODE_ENV=development

# App Configuration
APP_URL=http://localhost:5001

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/dbname
# Or for local SQLite: file:./data/app.db
SESSION_DB=sessions.db

# Session Secret (generate with: openssl rand -hex 32)
SESSION_SECRET=your-session-secret-here

# Google OAuth (Get from: https://console.cloud.google.com/apis/credentials)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Local Development Login (set to "true" to bypass <PERSON>A<PERSON> in development)
ALLOW_LOCAL_LOGIN=true

# OpenAI API Key (Get from: https://platform.openai.com/api-keys)
OPENAI_API_KEY=your-openai-api-key

# Google AI API Key (Get from: https://makersuite.google.com/app/apikey)
GOOGLE_AI_API_KEY=your-google-ai-api-key

# Stripe Configuration (Get from: https://dashboard.stripe.com/apikeys)
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
