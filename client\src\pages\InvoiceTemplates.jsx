import React, { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import ConstructionPaymentApplication from "@/components/invoices/ConstructionPaymentApplication";
import ConsultingInvoice from "@/components/invoices/ConsultingInvoice";
import { Printer, Download, Save, Loader2 } from "lucide-react";
import { generateAndDownloadPDF } from "@/lib/pdfGenerator";
import { apiRequest } from "@/lib/queryClient";
// Sample data for Construction Payment Application
const sampleConstructionData = {
    applicationNo: "12",
    applicationDate: "7/23/23",
    periodTo: "7/31/23",
    projectNo: "120321",
    contractDate: "12/1/20",
    project: "Jack's Construction Project",
    projectAddress: "1234 Builders Ave, Glendale, CA 91201 USA",
    owner: "<PERSON>",
    ownerAddress: "456 Lakeview Ave, Glendale, CA 91201 USA",
    contractor: "ABC Construction",
    contractorAddress: "1234 Happy Ave, Smithdale, CA 91210",
    contractFor: "ADU Addition",
    originalContractSum: 80000.00,
    netChangeByChangeOrders: 0.00,
    workItems: [
        {
            itemNo: 1,
            description: "Rough-In",
            scheduledValue: 25000.00,
            fromPreviousApplication: 25000.00,
            thisPeriod: 0.00,
            materialsPresently: 0.00,
            totalCompleted: 25000.00,
            percent: 100.0,
            balanceToFinish: 0.00,
            retainage: 0.00,
        },
        {
            itemNo: 2,
            description: "Trim-Out",
            scheduledValue: 45000.00,
            fromPreviousApplication: 45000.00,
            thisPeriod: 0.00,
            materialsPresently: 0.00,
            totalCompleted: 45000.00,
            percent: 100.0,
            balanceToFinish: 0.00,
            retainage: 0.00,
        },
        {
            itemNo: 3,
            description: "Start-Up",
            scheduledValue: 10000.00,
            fromPreviousApplication: 0.00,
            thisPeriod: 10000.00,
            materialsPresently: 0.00,
            totalCompleted: 10000.00,
            percent: 100.0,
            balanceToFinish: 0.00,
            retainage: 1000.00,
        },
    ],
    retainagePercent: 1.25,
    retainageOnCompletedWork: 1000.00,
    retainageOnStoredMaterial: 0.00,
    lessPreviousCertificates: 70000.00,
    distributeTo: {
        owner: false,
        architect: false,
        contractor: true,
        field: false,
    },
};
// Sample data for Consulting Invoice
const sampleConsultingData = {
    companyName: "My Consulting Company",
    clientName: "Howe Family Trust",
    clientAddress: [
        "2 Chiffley Square",
        "Hamilton",
        "New Zealand"
    ],
    attention: "Judith Neely",
    invoiceNumber: "980083",
    invoiceDate: "06 Nov",
    gst: "XXXXXXXXX",
    jobNo: "0403",
    orderNumber: "2177-N",
    totalAmount: 7475.00,
    projectReference: "213 Epping Road North Shore, Retail Warehouse Refurbishment",
    progressClaimNo: "3",
    lineItems: [
        {
            description: "Concept Development",
            feeValue: 60000,
            percentDone: 70,
            feeToDate: 42000,
            prevInvoice: 42000,
            thisInvoice: 0.00,
        },
        {
            description: "Drawings to Consent",
            feeValue: 60001,
            percentDone: 87,
            feeToDate: 52501,
            prevInvoice: 48001,
            thisInvoice: 4500.00,
        },
        {
            description: "Construction Drawings",
            feeValue: 22880,
            percentDone: 9,
            feeToDate: 2000,
            prevInvoice: 0,
            thisInvoice: 2000.00,
        },
        {
            description: "Tenders and Quotes",
            feeValue: 9226,
            percentDone: 0,
            feeToDate: 0,
            prevInvoice: 0,
            thisInvoice: 0.00,
        },
        {
            description: "Project Management",
            feeValue: 30000,
            percentDone: 0,
            feeToDate: 0,
            prevInvoice: 0,
            thisInvoice: 0.00,
        },
        {
            description: "Variation 1 Entry Lobby",
            feeValue: 6000,
            percentDone: 0,
            feeToDate: 0,
            prevInvoice: 0,
            thisInvoice: 0.00,
        },
    ],
    gstRate: 0.15, // 15% GST
};
export default function InvoiceTemplates() {
    const [activeTab, setActiveTab] = useState("construction");
    const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const constructionRef = useRef(null);
    const consultingRef = useRef(null);
    const { toast } = useToast();
    const handlePrint = () => {
        window.print();
    };
    const handleDownloadPDF = async () => {
        try {
            setIsGeneratingPDF(true);
            const elementRef = activeTab === "construction" ? constructionRef : consultingRef;
            const filename = activeTab === "construction"
                ? `Construction-Payment-Application-${sampleConstructionData.applicationNo}.pdf`
                : `Consulting-Invoice-${sampleConsultingData.invoiceNumber}.pdf`;
            if (!elementRef.current) {
                throw new Error("Invoice element not found");
            }
            await generateAndDownloadPDF(elementRef.current, filename);
            toast({
                title: "Success",
                description: "PDF downloaded successfully!",
            });
        }
        catch (error) {
            console.error("Error generating PDF:", error);
            toast({
                title: "Error",
                description: "Failed to generate PDF. Please try again.",
                variant: "destructive",
            });
        }
        finally {
            setIsGeneratingPDF(false);
        }
    };
    const handleSaveInvoice = async () => {
        try {
            setIsSaving(true);
            const invoiceData = activeTab === "construction"
                ? { type: "construction", ...sampleConstructionData }
                : { type: "consulting", ...sampleConsultingData };
            await apiRequest('POST', '/api/invoices', invoiceData);
            toast({
                title: "Success",
                description: "Invoice saved successfully!",
            });
        }
        catch (error) {
            console.error("Error saving invoice:", error);
            toast({
                title: "Error",
                description: "Failed to save invoice. Please try again.",
                variant: "destructive",
            });
        }
        finally {
            setIsSaving(false);
        }
    };
    return (<div className="min-h-screen bg-gray-50 py-8 print:bg-white print:py-0">
      <div className="max-w-7xl mx-auto px-4 print:px-0">
        {/* Header - Hidden when printing */}
        <div className="mb-8 print:hidden">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Invoice Templates</h1>
              <p className="text-gray-600 mt-2">
                Professional invoice templates for construction and consulting businesses
              </p>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleSaveInvoice} variant="outline" className="flex items-center gap-2" disabled={isSaving}>
                {isSaving ? (<Loader2 className="h-4 w-4 animate-spin"/>) : (<Save className="h-4 w-4"/>)}
                {isSaving ? "Saving..." : "Save"}
              </Button>
              <Button onClick={handlePrint} variant="outline" className="flex items-center gap-2">
                <Printer className="h-4 w-4"/>
                Print
              </Button>
              <Button onClick={handleDownloadPDF} className="flex items-center gap-2" disabled={isGeneratingPDF}>
                {isGeneratingPDF ? (<Loader2 className="h-4 w-4 animate-spin"/>) : (<Download className="h-4 w-4"/>)}
                {isGeneratingPDF ? "Generating..." : "Download PDF"}
              </Button>
            </div>
          </div>
        </div>

        {/* Tabs - Hidden when printing */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="print:hidden">
          <TabsList className="grid w-full max-w-md grid-cols-2 mb-6">
            <TabsTrigger value="construction">Construction Payment</TabsTrigger>
            <TabsTrigger value="consulting">Consulting Invoice</TabsTrigger>
          </TabsList>

          <TabsContent value="construction">
            <Card>
              <CardHeader>
                <CardTitle>Construction Payment Application (AIA Style)</CardTitle>
              </CardHeader>
              <CardContent>
                <div ref={constructionRef} className="bg-white">
                  <ConstructionPaymentApplication data={sampleConstructionData}/>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="consulting">
            <Card>
              <CardHeader>
                <CardTitle>Consulting Invoice</CardTitle>
              </CardHeader>
              <CardContent>
                <div ref={consultingRef} className="bg-white">
                  <ConsultingInvoice data={sampleConsultingData}/>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Print View - Only visible when printing */}
        <div className="hidden print:block">
          {activeTab === "construction" && (<ConstructionPaymentApplication data={sampleConstructionData}/>)}
          {activeTab === "consulting" && (<ConsultingInvoice data={sampleConsultingData}/>)}
        </div>

        {/* Usage Instructions - Hidden when printing */}
        <Card className="mt-8 print:hidden">
          <CardHeader>
            <CardTitle>How to Use These Templates</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Integration Options:</h3>
              <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
                <li>Import the components into your React application</li>
                <li>Pass your invoice data to the components using the defined interfaces</li>
                <li>Use the print functionality for physical copies</li>
                <li>Integrate with PDF generation libraries for digital downloads</li>
              </ol>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Customization:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                <li>Modify the styles using Tailwind CSS classes</li>
                <li>Add your company logo by passing the logoUrl prop</li>
                <li>Extend the data interfaces to include additional fields</li>
                <li>Adjust calculations and formulas as needed</li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Data Interfaces:</h3>
              <p className="text-sm text-gray-600">
                Both components export TypeScript interfaces (ConstructionPaymentApplicationData and
                ConsultingInvoiceData) that define the required data structure. Use these interfaces to
                ensure type safety when integrating with your backend systems.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);
}
