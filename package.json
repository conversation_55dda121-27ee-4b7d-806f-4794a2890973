{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development tsx server/index.ts", "build": "vite build && node esbuild.server.js", "build:client": "npx vite build", "start": "cross-env NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push"}, "dependencies": {"@google/genai": "^1.20.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/typography": "^0.5.19", "@tanstack/react-query": "^5.60.5", "@types/jsdom": "^27.0.0", "@types/memoizee": "^0.4.12", "@types/passport-google-oauth20": "^2.0.16", "@types/stripe": "^8.0.416", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "docx": "^9.5.1", "dotenv": "^17.2.3", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "esbuild": "^0.25.11", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "jsdom": "^27.0.0", "jspdf": "^3.0.3", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.453.0", "mammoth": "^1.11.0", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "multer": "^2.0.2", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openai": "^5.22.0", "openid-client": "^6.8.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "pdf2json": "^3.1.4", "pg": "^8.11.3", "postcss": "^8.5.6", "puppeteer": "^24.23.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "sharp": "^0.34.4", "stripe": "^19.1.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.18", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "vite": "^6.4.1", "wouter": "^3.3.5", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@playwright/test": "^1.56.0", "@replit/vite-plugin-cartographer": "^0.3.1", "@replit/vite-plugin-dev-banner": "^0.1.1", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@types/better-sqlite3": "^7.6.13", "@types/connect-pg-simple": "^7.0.3", "@types/express": "^4.17.21", "@types/express-session": "^1.18.0", "@types/multer": "^2.0.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/pdf-parse": "^1.1.5", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "cross-env": "^10.1.0", "drizzle-kit": "^0.31.4", "tsx": "^4.20.5", "typescript": "5.6.3"}, "optionalDependencies": {"better-sqlite3": "^9.6.0", "bufferutil": "^4.0.8", "connect-sqlite3": "^0.9.16"}}