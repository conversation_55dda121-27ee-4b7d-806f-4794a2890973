import fetch from 'node-fetch';
import { JSD<PERSON> } from "jsdom";
// Common job board patterns and their selectors
const JOB_BOARD_SELECTORS = {
    linkedin: {
        title: ".top-card-layout__title",
        company: ".topcard__org-name-link",
        location: ".topcard__flavor-row",
        description: ".show-more-less-html__markup",
        requirements: ".description__text",
        benefits: ".description__text",
        salary: ".compensation__salary",
        jobType: ".description__job-criteria-text",
        postedDate: ".posted-time-ago__text",
    },
    indeed: {
        title: ".jobsearch-JobInfoHeader-title",
        company: ".jobsearch-InlineCompanyInfo",
        location: ".jobsearch-JobComponent-location",
        description: "#jobDescriptionText",
        requirements: "#jobDescriptionText",
        benefits: "#jobDescriptionText",
        salary: ".jobsearch-JobComponent-salary",
        jobType: ".jobsearch-JobComponent-jobType",
        postedDate: ".jobsearch-JobComponent-postedDate",
    },
    glassdoor: {
        title: ".jobTitle",
        company: ".employerName",
        location: ".location",
        description: ".jobDescription",
        requirements: ".jobDescription",
        benefits: ".jobDescription",
        salary: ".salary",
        jobType: ".employmentType",
        postedDate: ".age",
    },
    ziprecruiter: {
        title: ".job_title",
        company: ".company_name",
        location: ".location",
        description: ".job_description",
        requirements: ".job_description",
        benefits: ".job_description",
        salary: ".salary_range",
        jobType: ".job_type",
        postedDate: ".time_since_posted",
    },
    monster: {
        title: ".title",
        company: ".company",
        location: ".location",
        description: ".description",
        requirements: ".description",
        benefits: ".description",
        salary: ".salary",
        jobType: ".job-type",
        postedDate: ".posted-date",
    },
    careerbuilder: {
        title: ".job-title",
        company: ".company",
        location: ".location",
        description: ".job-description",
        requirements: ".job-description",
        benefits: ".job-description",
        salary: ".salary",
        jobType: ".job-type",
        postedDate: ".posted-date",
    },
    // Generic selectors for unknown job boards
    generic: {
        title: ["h1", ".job-title", "[data-testid='job-title']", ".title"],
        company: [".company-name", "[data-testid='company-name']", ".company"],
        location: [".location", "[data-testid='location']", ".job-location"],
        description: [".job-description", "[data-testid='job-description']", ".description", "#job-description"],
        requirements: [".requirements", ".qualifications", "[data-testid='requirements']"],
        benefits: [".benefits", "[data-testid='benefits']", ".job-benefits"],
        salary: [".salary", "[data-testid='salary']", ".salary-info"],
        jobType: [".job-type", "[data-testid='job-type']", ".employment-type"],
        postedDate: [".posted-date", "[data-testid='posted-date']", ".date-posted"],
    }
};
// Detect job board from URL
function detectJobBoard(url) {
    const urlLower = url.toLowerCase();
    if (urlLower.includes('linkedin.com'))
        return 'linkedin';
    if (urlLower.includes('indeed.com'))
        return 'indeed';
    if (urlLower.includes('glassdoor.com'))
        return 'glassdoor';
    if (urlLower.includes('ziprecruiter.com'))
        return 'ziprecruiter';
    if (urlLower.includes('monster.com'))
        return 'monster';
    if (urlLower.includes('careerbuilder.com'))
        return 'careerbuilder';
    return 'generic';
}
// Extract text using multiple selectors
function extractText(document, selectors) {
    if (typeof selectors === 'string') {
        selectors = [selectors];
    }
    for (const selector of selectors) {
        const element = document.querySelector(selector);
        if (element && element.textContent && element.textContent.trim()) {
            return element.textContent.trim();
        }
    }
    return "";
}
// Clean and normalize extracted text
function cleanText(text) {
    return text
        .replace(/\s+/g, " ") // Replace multiple whitespace with single space
        .replace(/\n{3,}/g, "\n\n") // Replace multiple newlines with double newline
        .trim();
}
// Extract structured data from JSON-LD scripts
function extractStructuredData(document) {
    const scripts = document.querySelectorAll('script[type="application/ld+json"]');
    for (const script of scripts) {
        try {
            const data = JSON.parse(script.textContent || "");
            if (data && typeof data === 'object') {
                // Handle multiple formats of structured data
                if (Array.isArray(data)) {
                    for (const item of data) {
                        if (item['@type'] && (item['@type'].includes('JobPosting') || item['@type'].includes('Job'))) {
                            return item;
                        }
                    }
                }
                else if (data['@type'] && (data['@type'].includes('JobPosting') || data['@type'].includes('Job'))) {
                    return data;
                }
            }
        }
        catch (error) {
            console.error("Error parsing structured data:", error);
        }
    }
    return null;
}
export async function scrapeJobFromUrl(url) {
    try {
        // Basic URL validation
        const urlObj = new URL(url);
        const hostname = urlObj.hostname.toLowerCase();
        // Fetch the job posting page with proper headers
        const response = await fetch(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const html = await response.text();
        const dom = new JSDOM(html);
        const document = dom.window.document;
        // Detect job board
        const jobBoard = detectJobBoard(url);
        const selectors = JOB_BOARD_SELECTORS[jobBoard];
        // Try to extract structured data first
        const structuredData = extractStructuredData(document);
        // Extract job information
        let jobTitle = "";
        let companyName = "";
        let location = "";
        let jobDescription = "";
        let requirements = "";
        let benefits = "";
        let salary = "";
        let jobType = "";
        let postedDate = "";
        // If structured data is available, use it
        if (structuredData) {
            jobTitle = structuredData.title || "";
            companyName = structuredData.hiringOrganization?.name || "";
            location = structuredData.jobLocation?.address?.addressLocality ||
                structuredData.jobLocation?.name || "";
            jobDescription = structuredData.description || "";
            requirements = structuredData.qualifications || "";
            benefits = structuredData.benefits || structuredData.jobBenefits || "";
            salary = structuredData.baseSalary?.value?.value ||
                structuredData.baseSalary?.text || "";
            jobType = structuredData.employmentType || "";
            postedDate = structuredData.datePosted || "";
        }
        // Fallback to DOM extraction if structured data is incomplete
        if (!jobTitle)
            jobTitle = extractText(document, selectors.title);
        if (!companyName)
            companyName = extractText(document, selectors.company);
        if (!location)
            location = extractText(document, selectors.location);
        if (!jobDescription)
            jobDescription = extractText(document, selectors.description);
        if (!requirements)
            requirements = extractText(document, selectors.requirements);
        if (!benefits)
            benefits = extractText(document, selectors.benefits);
        if (!salary)
            salary = extractText(document, selectors.salary);
        if (!jobType)
            jobType = extractText(document, selectors.jobType);
        if (!postedDate)
            postedDate = extractText(document, selectors.postedDate);
        // Clean and normalize extracted text
        jobTitle = cleanText(jobTitle);
        companyName = cleanText(companyName);
        location = cleanText(location);
        jobDescription = cleanText(jobDescription);
        requirements = cleanText(requirements);
        benefits = cleanText(benefits);
        salary = cleanText(salary);
        jobType = cleanText(jobType);
        postedDate = cleanText(postedDate);
        // If we still don't have enough content, use the original extraction methods
        if (!jobTitle)
            jobTitle = extractTitle(html) || "";
        if (!companyName)
            companyName = extractCompany(html, hostname) || "";
        if (!location)
            location = extractLocation(html) || "";
        // If we can't extract much content, return the raw HTML as description
        if (!jobDescription || jobDescription.length < 100) {
            // Strip HTML tags and get text content
            jobDescription = html.replace(/<[^>]*>/g, ' ')
                .replace(/\s+/g, ' ')
                .trim()
                .substring(0, 5000); // Limit to reasonable size
        }
        // Parse requirements into an array if it's a string
        let requirementsArray = [];
        if (requirements) {
            requirementsArray = requirements.split(/[.\n;]/).map(req => req.trim()).filter(req => req.length > 0);
        }
        return {
            title: jobTitle,
            company: companyName,
            description: jobDescription,
            location,
            requirements: requirementsArray,
            salary,
            jobType,
            postedDate,
            benefits
        };
    }
    catch (error) {
        console.error('Error scraping job URL:', error);
        throw new Error(`Failed to scrape job posting: ${error.message}`);
    }
}
function extractTitle(html) {
    // Common patterns for job titles
    const titlePatterns = [
        /<title[^>]*>([^<]+)/i,
        /<h1[^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)/i,
        /<h1[^>]*class="[^"]*job[^"]*"[^>]*>([^<]+)/i,
        /<h1[^>]*>([^<]+)</i,
    ];
    for (const pattern of titlePatterns) {
        const match = html.match(pattern);
        if (match && match[1]) {
            return match[1].trim().replace(/\s+/g, ' ');
        }
    }
    return undefined;
}
function extractCompany(html, hostname) {
    // Try to extract company name from various patterns
    const companyPatterns = [
        /<[^>]*class="[^"]*company[^"]*"[^>]*>([^<]+)/i,
        /<[^>]*class="[^"]*employer[^"]*"[^>]*>([^<]+)/i,
    ];
    for (const pattern of companyPatterns) {
        const match = html.match(pattern);
        if (match && match[1]) {
            return match[1].trim().replace(/\s+/g, ' ');
        }
    }
    // Try to infer from hostname
    if (hostname.includes('linkedin')) {
        const linkedinMatch = html.match(/hiring for ([^,<]+)/i);
        if (linkedinMatch)
            return linkedinMatch[1].trim();
    }
    return undefined;
}
function extractDescription(html) {
    // Look for job description patterns
    const descPatterns = [
        /<div[^>]*class="[^"]*description[^"]*"[^>]*>(.*?)<\/div>/is,
        /<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)<\/div>/is,
        /<section[^>]*class="[^"]*description[^"]*"[^>]*>(.*?)<\/section>/is,
    ];
    for (const pattern of descPatterns) {
        const match = html.match(pattern);
        if (match && match[1] && match[1].length > 100) {
            return match[1]
                .replace(/<[^>]*>/g, ' ')
                .replace(/\s+/g, ' ')
                .trim();
        }
    }
    // Fallback: extract all text content
    return html
        .replace(/<script[^>]*>.*?<\/script>/gis, '')
        .replace(/<style[^>]*>.*?<\/style>/gis, '')
        .replace(/<[^>]*>/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();
}
function extractLocation(html) {
    const locationPatterns = [
        /<[^>]*class="[^"]*location[^"]*"[^>]*>([^<]+)/i,
        /<[^>]*class="[^"]*address[^"]*"[^>]*>([^<]+)/i,
    ];
    for (const pattern of locationPatterns) {
        const match = html.match(pattern);
        if (match && match[1]) {
            return match[1].trim().replace(/\s+/g, ' ');
        }
    }
    return undefined;
}
