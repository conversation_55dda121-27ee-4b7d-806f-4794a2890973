# Vercel Deployment Guide

This guide explains how to deploy this application to Vercel with the custom domain `try2work.com`.

> **Note**: For detailed domain configuration instructions, see [DOMAIN_SETUP.md](./DOMAIN_SETUP.md)

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **PostgreSQL Database**: Set up a Neon database at [neon.tech](https://neon.tech) (free tier available)
3. **API Keys**: Ensure you have all required API keys (OpenAI, Google AI, Stripe)

## Environment Variables

### Required for .env.local (Local Development)

Copy `.env.local` to `.env` and update these values:

```bash
# Environment
NODE_ENV=development

# Database - SQLite for local dev
DATABASE_URL=file:./data/app.db
DATABASE_PATH=./data/app.db

# Sessions
SESSION_DB=sessions.db
SESSION_SECRET=your-local-dev-secret-key-change-this

# Auth - Local development bypass
ALLOW_LOCAL_LOGIN=true
APP_URL=http://localhost:5001

# API Keys (REQUIRED)
OPENAI_API_KEY=sk-...
GOOGLE_AI_API_KEY=AIza...

# Stripe (REQUIRED)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

### Required for Vercel (Production)

Add these environment variables in your Vercel project settings:

#### Critical Variables

1. **DATABASE_URL** (REQUIRED)
   - Get from Neon.tech after creating a PostgreSQL database
   - Format: `********************************************************`
   - Example: `postgresql://myuser:<EMAIL>/mydb?sslmode=require`

2. **SESSION_SECRET** (REQUIRED)
   - Generate a strong random string (32+ characters)
   - Example: Use `openssl rand -hex 32` to generate

3. **OPENAI_API_KEY** (REQUIRED)
   - Get from [platform.openai.com](https://platform.openai.com/api-keys)
   - Format: `sk-proj-...`

4. **GOOGLE_AI_API_KEY** (REQUIRED)
   - Get from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Format: `AIza...`

5. **STRIPE_SECRET_KEY** (REQUIRED for payments)
   - Get from [Stripe Dashboard](https://dashboard.stripe.com/apikeys)
   - Use `sk_test_...` for testing, `sk_live_...` for production

6. **STRIPE_PUBLISHABLE_KEY** (REQUIRED for payments)
   - Get from [Stripe Dashboard](https://dashboard.stripe.com/apikeys)
   - Use `pk_test_...` for testing, `pk_live_...` for production

7. **STRIPE_WEBHOOK_SECRET** (REQUIRED for payment webhooks)
   - Get from [Stripe Webhooks](https://dashboard.stripe.com/webhooks)
   - Format: `whsec_...`

#### Optional Variables

8. **APP_URL** (Optional - auto-detected on Vercel)
   - Your production domain URL
   - Production: `https://try2work.com` or `https://www.try2work.com`
   - If not set, defaults to `http://localhost:5001` in dev

9. **NODE_ENV**
   - Automatically set to `production` by Vercel
   - Set to `development` for local testing

#### Authentication Variables (Optional - for Replit Auth)

Only needed if you want to use Replit authentication:

10. **REPL_ID** (Optional)
    - Your Replit app ID
    - Only needed if using Replit OIDC auth

11. **ISSUER_URL** (Optional)
    - Default: `https://replit.com/oidc`
    - Only needed if using Replit OIDC auth

## Deployment Steps

### 1. Set Up PostgreSQL Database

1. Go to [neon.tech](https://neon.tech) and create a free account
2. Create a new project
3. Copy the connection string (starts with `postgresql://`)
4. Save this for the `DATABASE_URL` environment variable

### 2. Deploy to Vercel

#### Option A: Using Vercel CLI

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel

# Add environment variables
vercel env add DATABASE_URL
vercel env add SESSION_SECRET
vercel env add OPENAI_API_KEY
vercel env add GOOGLE_AI_API_KEY
vercel env add STRIPE_SECRET_KEY
vercel env add STRIPE_PUBLISHABLE_KEY
vercel env add STRIPE_WEBHOOK_SECRET

# Deploy to production
vercel --prod
```

#### Option B: Using Vercel Dashboard

1. Go to [vercel.com/new](https://vercel.com/new)
2. Import your Git repository
3. Configure the project:
   - Framework Preset: Other
   - Build Command: `npm run build`
   - Output Directory: `dist/public`
   - Install Command: `npm install`
4. Add all environment variables in the "Environment Variables" section
5. Click "Deploy"

### 3. Initialize Database Schema

After first deployment, run:

```bash
# Using Vercel CLI
vercel env pull .env.production.local
npm run db:push
```

Or set up the database schema manually using Drizzle Studio.

### 4. Configure Stripe Webhooks

1. Go to [Stripe Webhooks](https://dashboard.stripe.com/webhooks)
2. Add endpoint: `https://try2work.com/api/stripe/webhook`
3. Select events to listen for:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `checkout.session.completed`
4. Copy the webhook signing secret to `STRIPE_WEBHOOK_SECRET`

## Local Development

1. Copy `.env.local` to `.env`
2. Update the values with your API keys
3. Install dependencies:
   ```bash
   npm install
   ```
4. Run development server:
   ```bash
   npm run dev
   ```
5. Access at `http://localhost:5001`

## Troubleshooting

### Database Connection Issues

- Ensure `DATABASE_URL` includes `?sslmode=require`
- Verify the connection string is correct
- Check Neon database is active (free tier suspends after inactivity)

### Session Issues

- Ensure `SESSION_SECRET` is set and strong
- Check database has the `sessions` table (auto-created)

### Authentication Issues

- For local dev, ensure `ALLOW_LOCAL_LOGIN=true`
- For production, verify `APP_URL` is set to `https://try2work.com`
- Check browser console for errors

### Build Failures

- Verify all dependencies are in `dependencies`, not `devDependencies`
- Check Node.js version compatibility
- Review build logs in Vercel dashboard

## Database Migration

If you need to migrate data from SQLite to PostgreSQL:

1. Export data from SQLite:
   ```bash
   sqlite3 data/app.db .dump > backup.sql
   ```
2. Convert to PostgreSQL format (manual process required)
3. Import to Neon database

## Environment Variables Checklist

Before deploying, ensure you have:

- [ ] PostgreSQL database URL from Neon
- [ ] Strong session secret (32+ characters)
- [ ] OpenAI API key
- [ ] Google AI API key
- [ ] Stripe secret key
- [ ] Stripe publishable key
- [ ] Stripe webhook secret (after setting up webhook)
- [ ] All variables added to Vercel project settings

## Support

For issues:
- Check Vercel deployment logs
- Review browser console for client-side errors
- Check server logs in Vercel dashboard
