CREATE TABLE IF NOT EXISTS sessions (
  sid TEXT PRIMARY KEY,
  sess TEXT NOT NULL,
  expire INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS sessions_expire_index ON sessions(expire);

CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE,
  first_name TEXT,
  last_name TEXT,
  profile_image_url TEXT,
  created_at INTEGER DEFAULT (strftime('%s','now')),
  updated_at INTEGER DEFAULT (strftime('%s','now'))
);

CREATE TABLE IF NOT EXISTS resumes (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  title TEXT NOT NULL,
  template_id TEXT DEFAULT 'modern',
  industry TEXT,
  status TEXT NOT NULL DEFAULT 'draft',
  payment_status TEXT NOT NULL DEFAULT 'unpaid',
  stripe_session_id TEXT,
  personal_info TEXT,
  professional_summary TEXT,
  work_experience TEXT,
  education TEXT,
  skills TEXT,
  target_job_title TEXT,
  target_company TEXT,
  job_description TEXT,
  job_url TEXT,
  extracted_keywords TEXT,
  ai_enhanced INTEGER DEFAULT 1,
  created_at INTEGER DEFAULT (strftime('%s','now')),
  updated_at INTEGER DEFAULT (strftime('%s','now')),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS job_analyses (
  id TEXT PRIMARY KEY,
  resume_id TEXT NOT NULL,
  job_description TEXT NOT NULL,
  extracted_keywords TEXT,
  requirements TEXT,
  suggested_improvements TEXT,
  match_score TEXT,
  created_at INTEGER DEFAULT (strftime('%s','now')),
  FOREIGN KEY (resume_id) REFERENCES resumes(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS job_analyses_resume_id_index ON job_analyses(resume_id);
