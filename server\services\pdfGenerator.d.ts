import type { Resume } from "@shared/schema";
export interface PDFGenerationOptions {
    template: 'modern' | 'classic' | 'creative';
    includePhoto: boolean;
    colorScheme: string;
}
export declare function generateResumePDF(resume: Resume, options?: PDFGenerationOptions): Promise<Buffer>;
export declare function generateResumePreviewHTML(resume: Resume): string;
//# sourceMappingURL=pdfGenerator.d.ts.map