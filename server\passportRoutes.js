import passport from "passport";
import { storage } from "./storage.js";
// Middleware to require authentication
export function requireAuth(req, res, next) {
    if (req.isAuthenticated()) {
        req.user = req.user;
        next();
    }
    else {
        res.status(401).json({ message: "Unauthorized" });
    }
}
// Google OAuth routes
export function setupPassportRoutes(app) {
    // Google OAuth login
    app.get("/api/auth/google", passport.authenticate("google", {
        scope: ["profile", "email"],
        prompt: "select_account"
    }));
    // Google OAuth callback
    app.get("/api/auth/google/callback", passport.authenticate("google", {
        successRedirect: "/dashboard",
        failureRedirect: "/login",
        failureFlash: true
    }));
    // Logout route
    app.post("/api/auth/logout", (req, res) => {
        req.logout((err) => {
            if (err) {
                return res.status(500).json({ message: "Logout failed" });
            }
            res.json({ message: "Logged out successfully" });
        });
    });
    // Get current user
    app.get("/api/auth/user", requireAuth, async (req, res) => {
        try {
            const userId = req.user.id;
            const user = await storage.getUser(userId);
            res.json(user ?? {});
        }
        catch (error) {
            console.error("Error fetching user:", error);
            res.status(500).json({ message: "Failed to fetch user" });
        }
    });
    console.log("[Auth] Passport.js authentication configured");
}
