import pdfParse from "../lib/pdf-parse-wrapper.js";
import { extractRawText } from "mammoth";
import type { Resume } from "@shared/schema";
import type { Express } from "express";
import * as docx from "docx";
import sharp from "sharp";

type ResumeDraft = {
  personalInfo?: import("@shared/schema").PersonalInfo;
  professionalSummary?: string;
  workExperience?: import("@shared/schema").WorkExperience[];
  education?: import("@shared/schema").Education[];
  skills?: import("@shared/schema").Skill[];
  targetJobTitle?: string;
  industry?: string;
};

const TEXT_MIME_TYPES = ["text/plain", "text/markdown"];
const PDF_MIME_TYPES = ["application/pdf"];
const DOCX_MIME_TYPES = [
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];
const DOC_MIME_TYPES = [
  "application/msword",
];

// File type detection based on file signature
function getFileType(buffer: Buffer): string | null {
  // Check for PDF signature
  if (buffer.length > 4 &&
      buffer[0] === 0x25 &&
      buffer[1] === 0x50 &&
      buffer[2] === 0x44 &&
      buffer[3] === 0x46) {
    return 'pdf';
  }
  
  // Check for DOCX signature (ZIP format with specific content)
  if (buffer.length > 4 &&
      buffer[0] === 0x50 &&
      buffer[1] === 0x4B &&
      (buffer[2] === 0x03 || buffer[2] === 0x05 || buffer[2] === 0x07)) {
    return 'docx';
  }
  
  // Check for DOC signature (OLE2 format)
  if (buffer.length > 8 &&
      buffer[0] === 0xD0 &&
      buffer[1] === 0xCF &&
      buffer[2] === 0x11 &&
      buffer[3] === 0xE0 &&
      buffer[4] === 0xA1 &&
      buffer[5] === 0xB1 &&
      buffer[6] === 0x1A &&
      buffer[7] === 0xE1) {
    return 'doc';
  }
  
  return null;
}

interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  buffer: Buffer;
}

function ensureIds<T extends { id?: string }>(items: T[] | undefined, prefix: string): T[] | undefined {
  if (!items) return undefined;

  // If the incoming value is not an array, try to coerce it into one so
  // downstream code that expects arrays doesn't crash (defensive).
  // - If it's a single object, wrap it in an array.
  // - Otherwise, return undefined to indicate no valid items.
  if (!Array.isArray(items)) {
    if (typeof items === "object" && items !== null) {
      // wrap single object into array
      items = [items as T];
    } else {
      return undefined;
    }
  }

  return items.map((item, index) => ({
    ...item,
    id: item.id ?? `${prefix}-${index + 1}`,
  }));
}

const toOptionalString = (value: unknown): string | undefined => {
  if (typeof value === "string" && value.trim().length > 0) {
    return value;
  }
  return undefined;
};

// Enhanced PDF parsing with OCR fallback for scanned documents
async function extractPDFText(buffer: Buffer): Promise<{ text: string; hasImages: boolean }> {
  try {
    // Use pdf2json for enhanced PDF text extraction
    const pdfResult = await pdfParse(buffer);
    let text = pdfResult.text;
    
    // Check if extracted text is minimal (might be scanned PDF)
    const textLength = text.replace(/\s/g, '').length;
    if (textLength < 100) {
      console.log("[PDF] Low text content detected, checking for images...");
      
      // Check if PDF contains images using sharp
      try {
        const metadata = await sharp(buffer).metadata();
        const hasImages = metadata.width && metadata.height;
        
        if (hasImages) {
          console.log("[PDF] PDF contains images, likely scanned document");
          // For now, return what we have - in production, you'd integrate with OCR service
          return { text, hasImages: true };
        }
      } catch (sharpError) {
        console.log("[PDF] Could not analyze PDF images:", sharpError);
      }
    }
    
    return { text, hasImages: false };
  } catch (error) {
    console.error("[PDF] Error extracting text:", error);
    throw new Error(`PDF extraction failed: ${(error as Error).message}`);
  }
}

// Enhanced DOCX parsing with document structure analysis
async function extractDOCXText(buffer: Buffer): Promise<{ text: string; metadata: any }> {
  try {
    // Method 1: Use mammoth for text extraction (better for complex layouts)
    const mammothResult = await extractRawText({ buffer });
    let text = mammothResult.value;
    
    // For now, we'll rely on mammoth for better compatibility
    // The docx library might need a different approach for loading existing documents
    
    // Extract basic metadata
    const metadata = {
      title: "",
      subject: "",
      creator: "",
      keywords: "",
      description: "",
    };
    
    return {
      text: text.trim(),
      metadata
    };
  } catch (error) {
    console.error("[DOCX] Error extracting text:", error);
    // Fallback to mammoth only
    try {
      const mammothResult = await extractRawText({ buffer });
      return { text: mammothResult.value, metadata: {} };
    } catch (fallbackError) {
      throw new Error(`DOCX extraction failed: ${(error as Error).message}`);
    }
  }
}

// Enhanced DOC parsing with better error handling
async function extractDOCText(buffer: Buffer): Promise<string> {
  try {
    // Try mammoth first (sometimes works with legacy DOC)
    const mammothResult = await extractRawText({ buffer });
    if (mammothResult.value && mammothResult.value.trim().length > 50) {
      return mammothResult.value;
    }
    
    throw new Error("Legacy DOC files are not fully supported. Please convert to DOCX or PDF format.");
  } catch (error) {
    console.error("[DOC] Error parsing DOC file:", error);
    throw new Error(`DOC extraction failed: ${(error as Error).message}`);
  }
}

// Enhanced text extraction with multiple fallback strategies
async function bufferToText(file: UploadedFile): Promise<{ text: string; metadata: any; hasImages: boolean }> {
  let fileType = null;
  let text = "";
  let metadata: any = {};
  let hasImages = false;
  
  // First, try to detect file type from content
  fileType = getFileType(file.buffer);
  
  // If content detection fails, fall back to MIME type
  if (!fileType) {
    if (PDF_MIME_TYPES.includes(file.mimetype)) {
      fileType = "pdf";
    } else if (DOCX_MIME_TYPES.includes(file.mimetype)) {
      fileType = "docx";
    } else if (DOC_MIME_TYPES.includes(file.mimetype)) {
      fileType = "doc";
    } else if (TEXT_MIME_TYPES.includes(file.mimetype)) {
      fileType = "text";
    }
  }

  try {
    // Extract text based on file type using best available method
    switch (fileType) {
      case "pdf":
        const pdfResult = await extractPDFText(file.buffer);
        text = pdfResult.text;
        hasImages = pdfResult.hasImages;
        break;
      case "docx":
        const docxResult = await extractDOCXText(file.buffer);
        text = docxResult.text;
        metadata = docxResult.metadata;
        break;
      case "doc":
        text = await extractDOCText(file.buffer);
        break;
      case "text":
        text = file.buffer.toString("utf8");
        break;
      default:
        throw new Error(`Unsupported file type: ${file.mimetype}`);
    }
    
    // Enhanced text cleaning
    text = text
      .replace(/\s+/g, " ") // Replace multiple whitespace with single space
      .replace(/\n{3,}/g, "\n\n") // Replace multiple newlines with double newline
      .replace(/[^\w\s\n.,;:!?-]/g, "") // Remove special characters but keep basic punctuation
      .trim();
    
    // Validate extracted content
    if (!text || text.trim().length < 50) {
      throw new Error("Could not extract sufficient text from the document. The file may be empty, password-protected, or corrupted.");
    }
    
    // Add metadata about the extraction
    metadata.extractionMethod = fileType;
    metadata.fileSize = file.buffer.length;
    metadata.mimeType = file.mimetype;
    
    return { text, metadata, hasImages };
  } catch (error) {
    console.error("Error extracting text:", error);
    throw new Error(`Failed to extract text: ${(error as Error).message}`);
  }
}

export function normalizeResumeDraft(draft: ResumeDraft | Partial<Resume>): ResumeDraft {
  const personalInfo = (draft as ResumeDraft).personalInfo ?? (draft as Resume).personalInfo;
  const workExperience = (draft as ResumeDraft).workExperience ?? (draft as Resume).workExperience;
  const education = (draft as ResumeDraft).education ?? (draft as Resume).education;
  const skills = (draft as ResumeDraft).skills ?? (draft as Resume).skills;

  return {
    personalInfo: personalInfo
      ? {
          firstName: toOptionalString((personalInfo as any).firstName) ?? "",
          lastName: toOptionalString((personalInfo as any).lastName) ?? "",
          email: toOptionalString((personalInfo as any).email) ?? "",
          phone: toOptionalString((personalInfo as any).phone) ?? "",
          city: toOptionalString((personalInfo as any).city) ?? "",
          state: toOptionalString((personalInfo as any).state) ?? "",
          linkedinUrl: toOptionalString((personalInfo as any).linkedinUrl),
        }
      : undefined,
    professionalSummary:
      toOptionalString((draft as ResumeDraft).professionalSummary ?? (draft as Resume).professionalSummary),
    workExperience: ensureIds(workExperience as any, "exp") as any,
    education: ensureIds(education as any, "edu") as any,
    skills: ensureIds(skills as any, "skill") as any,
    targetJobTitle: toOptionalString(
      (draft as ResumeDraft).targetJobTitle ?? (draft as Resume).targetJobTitle,
    ),
    industry: toOptionalString((draft as ResumeDraft).industry ?? (draft as Resume).industry),
  };
}

export async function parseUploadedResume(file: UploadedFile): Promise<{ resume: ResumeDraft; metadata: any }> {
  try {
    // Validate file size earlier caller should check; bufferToText will throw on unsupported types
    const { text, metadata, hasImages } = await bufferToText(file);

    // If this was a PDF, attempt to use the pdf2json raw output for structured mapping
    let draft: ResumeDraft = {};
    if (metadata.extractionMethod === "pdf") {
      // pdfParse wrapper now returns both text and raw parsed pdf2json object
      const pdfResult = await pdfParse(file.buffer);
      const pdfRaw = (pdfResult as any).raw ?? null;
      const extractedText = (pdfResult as any).text ?? text;

      if (!pdfRaw) {
        // Fallback: if wrapper did not return raw, use plain text mapping
        draft = mapTextToDraft(extractedText);
      } else {
        draft = mapPdf2JsonToDraft(pdfRaw, extractedText);
      }
    } else {
      // Non-PDF fallback: derive fields from extracted text heuristically
      draft = mapTextToDraft(text);
    }

    const normalizedDraft = normalizeResumeDraft(draft);

    return {
      resume: normalizedDraft,
      metadata: {
        ...metadata,
        hasImages,
        extractionDate: new Date().toISOString(),
        wordCount: (text || "").split(/\s+/).filter(Boolean).length,
        characterCount: (text || "").length
      }
    };
  } catch (error) {
    console.error("Error parsing uploaded resume:", error);
    throw new Error(`Failed to parse resume: ${(error as Error).message}`);
  }
}

/**
 * Heuristic mapper: produce a ResumeDraft from plain extracted text
 * This is a best-effort fallback when structured pdf2json output is not available.
 */
function mapTextToDraft(text: string): ResumeDraft {
  const lines = text.split(/\r?\n/).map(l => l.trim()).filter(Boolean);
  const joined = text.replace(/\s+/g, ' ').trim();

  const emailMatch = joined.match(/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i);
  const phoneMatch = joined.match(/(\+?\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?){1,2}\d{3,4}/);
  const firstLine = lines.length ? lines[0] : "";

  const professionalSummary = lines.slice(1, 4).join(' ').trim() || undefined;

  // Simple skills extraction: look for a "Skills" section
  let skillStrings: string[] = [];
  const skillsSection = text.match(/(?:Skills|Technical Skills|Core Competencies)[:\s]*([\s\S]{0,300})/i);
  if (skillsSection && skillsSection[1]) {
    skillStrings = skillsSection[1].split(/[,•\n;|-]/).map(s => s.trim()).filter(Boolean).slice(0, 30);
  }

  // Convert string skills into Skill objects expected by types
  const skills = skillStrings.length
    ? skillStrings.map((s, i) => ({
        id: `skill-${i + 1}`,
        name: s,
        level: "",
        category: "",
      }))
    : undefined;

  const email = emailMatch ? emailMatch[0] : "";
  const phone = phoneMatch ? phoneMatch[0] : "";

  return {
    personalInfo: {
      firstName: firstLine.split(' ')[0] || "",
      lastName: firstLine.split(' ').slice(1).join(' ') || "",
      email,
      phone,
      city: "",
      state: "",
      linkedinUrl: undefined,
    },
    professionalSummary,
    workExperience: undefined,
    education: undefined,
    skills,
    targetJobTitle: undefined,
    industry: undefined,
  };
}

/**
 * Map pdf2json raw output into ResumeDraft.
 * This uses the spatial/structured information when available and falls back to text processing.
 */
function mapPdf2JsonToDraft(pdfData: any, fallbackText: string): ResumeDraft {
  // Build a full-text representation preserving reasonable ordering
  const pageTexts: string[] = [];
  if (Array.isArray(pdfData.Pages)) {
    for (const page of pdfData.Pages) {
      if (Array.isArray(page.Texts)) {
        const runs: string[] = [];
        for (const t of page.Texts) {
          if (Array.isArray(t.R)) {
            for (const r of t.R) {
              if (typeof r.T === 'string') {
                // pdf2json delivers percent-encoded text in some versions; try decodeURI component safely
                try {
                  runs.push(decodeURIComponent(r.T));
                } catch {
                  runs.push(r.T);
                }
              }
            }
          }
        }
        pageTexts.push(runs.join(' '));
      }
    }
  }
  const fullText = pageTexts.join('\n\n').trim() || fallbackText || "";

  // Extract obvious fields
  const emailMatch = fullText.match(/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i);
  const phoneMatch = fullText.match(/(\+?\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?){1,2}\d{3,4}/);
  const lines = fullText.split(/\r?\n/).map(l => l.trim()).filter(Boolean);
  const firstLine = lines.length ? lines[0] : "";

  // Skills: look for heading then parse comma/bullet separated lists
  let skillStrings: string[] = [];
  for (const line of lines) {
    const m = line.match(/^(?:Skills|Technical Skills|Core Competencies)[:\s-]*(.+)$/i);
    if (m) {
      skillStrings = m[1].split(/[,•;|-]/).map(s => s.trim()).filter(Boolean).slice(0, 50);
      break;
    }
  }

  const skills = skillStrings.length
    ? skillStrings.map((s, i) => ({ id: `skill-${i + 1}`, name: s, level: "", category: "" }))
    : undefined;

  // Summary: take first paragraph after name/email header
  let professionalSummary: string | undefined;
  if (lines.length > 1) {
    professionalSummary = lines.slice(1, Math.min(4, lines.length)).join(' ');
  }

  const email = emailMatch ? emailMatch[0] : "";
  const phone = phoneMatch ? phoneMatch[0] : "";

  // Very light-weight attempts to parse Experience and Education could be added here.
  // For now return minimal structured draft so downstream UI can present parsed fields to user for correction.

  return {
    personalInfo: {
      firstName: firstLine.split(' ')[0] || "",
      lastName: firstLine.split(' ').slice(1).join(' ') || "",
      email,
      phone,
      city: "",
      state: "",
      linkedinUrl: undefined,
    },
    professionalSummary,
    workExperience: undefined,
    education: undefined,
    skills,
    targetJobTitle: undefined,
    industry: undefined,
  };
}

// Utility function to get supported file types
export function getSupportedFileTypes(): string[] {
  return [...PDF_MIME_TYPES, ...DOCX_MIME_TYPES, ...DOC_MIME_TYPES, ...TEXT_MIME_TYPES];
}

// Utility function to validate file size (max 10MB)
export function validateFileSize(file: UploadedFile, maxSizeMB: number = 10): void {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  if (file.size > maxSizeBytes) {
    throw new Error(`File size exceeds ${maxSizeMB}MB limit`);
  }
}
