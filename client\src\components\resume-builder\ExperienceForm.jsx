import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, ArrowLeft, Plus, Trash2, Wand2, RefreshCw } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
const createEmptyExperience = () => ({
    id: `${Date.now()}-${Math.random().toString(36).slice(2, 8)}`,
    company: "",
    position: "",
    startDate: "",
    endDate: "",
    current: false,
    description: "",
    achievements: [],
});
export default function ExperienceForm({ data, onDataChange, onNext, onPrevious, }) {
    const { toast } = useToast();
    const [experiences, setExperiences] = useState([createEmptyExperience()]);
    useEffect(() => {
        if (Array.isArray(data.workExperience) && data.workExperience.length > 0) {
            setExperiences(data.workExperience);
        }
    }, [data.workExperience]);
    const updateExperiences = (items) => {
        setExperiences(items);
        onDataChange({ workExperience: items });
    };
    const addExperience = () => updateExperiences([...experiences, createEmptyExperience()]);
    const removeExperience = (id) => {
        const next = experiences.filter((exp) => exp.id !== id);
        updateExperiences(next.length ? next : [createEmptyExperience()]);
    };
    const updateExperienceField = (id, field, value) => {
        updateExperiences(experiences.map((exp) => (exp.id === id ? { ...exp, [field]: value } : exp)));
    };
    const enhanceExperienceMutation = useMutation({
        mutationFn: async (experience) => {
            return await apiRequest("POST", "/api/ai/enhance-experience", {
                experience,
                jobKeywords: data.extractedKeywords,
            });
        },
        onSuccess: ({ enhancedDescription }, experience) => {
            updateExperienceField(experience.id, "description", enhancedDescription);
            toast({
                title: "Success",
                description: "Experience description enhanced successfully!",
            });
        },
        onError: (error) => {
            if (isUnauthorizedError(error)) {
                toast({
                    title: "Unauthorized",
                    description: "You are logged out. Logging in again...",
                    variant: "destructive",
                });
                setTimeout(() => {
                    window.location.href = "/api/login";
                }, 500);
                return;
            }
            toast({
                title: "Error",
                description: "Failed to enhance description. Please try again.",
                variant: "destructive",
            });
        },
    });
    const handleEnhanceDescription = (experience) => {
        if (!experience.description.trim()) {
            toast({
                title: "Missing description",
                description: "Please add a description before enhancing it.",
                variant: "destructive",
            });
            return;
        }
        enhanceExperienceMutation.mutate(experience);
    };
    const isValidForm = () => experiences.every((exp) => exp.company.trim() &&
        exp.position.trim() &&
        exp.startDate.trim() &&
        exp.description.trim().length > 30);
    const handleNext = () => {
        if (isValidForm()) {
            onNext();
        }
        else {
            toast({
                title: "Almost there",
                description: "Please complete each experience entry before moving on.",
                variant: "destructive",
            });
        }
    };
    return (<div className="space-y-6">
      <div className="hidden lg:block">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Professional Experience</h1>
          <span className="text-sm text-muted-foreground bg-secondary px-3 py-1 rounded-full">
            Step 3 of 6
          </span>
        </div>
        <p className="text-muted-foreground">
          Showcase high-impact experiences with measurable achievements and strong action verbs.
        </p>
      </div>

      <div className="space-y-4">
        {experiences.map((experience, index) => (<Card key={experience.id} className="border-border">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="text-base font-semibold">
                  {experience.position || `Role ${index + 1}`}
                </CardTitle>
                <p className="text-xs text-muted-foreground">
                  Focus on outcomes, metrics, and tools you mastered.
                </p>
              </div>
              {experiences.length > 1 && (<Button variant="ghost" size="sm" onClick={() => removeExperience(experience.id)} data-testid={`button-remove-experience-${experience.id}`}>
                  <Trash2 className="h-4 w-4"/>
                </Button>)}
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`company-${experience.id}`}>Company *</Label>
                  <Input id={`company-${experience.id}`} value={experience.company} onChange={(event) => updateExperienceField(experience.id, "company", event.target.value)} placeholder="Acme Corp" autoComplete="organization" data-testid={`input-company-${experience.id}`}/>
                </div>
                <div className="space-y-2">
                  <Label htmlFor={`position-${experience.id}`}>Position *</Label>
                  <Input id={`position-${experience.id}`} value={experience.position} onChange={(event) => updateExperienceField(experience.id, "position", event.target.value)} placeholder="Senior Engineer" autoComplete="organization-title" data-testid={`input-position-${experience.id}`}/>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`start-${experience.id}`}>Start Date *</Label>
                  <Input id={`start-${experience.id}`} type="month" value={experience.startDate} onChange={(event) => updateExperienceField(experience.id, "startDate", event.target.value)} autoComplete="off" data-testid={`input-startDate-${experience.id}`}/>
                </div>
                <div className="space-y-2">
                  <Label htmlFor={`end-${experience.id}`}>End Date</Label>
                  <Input id={`end-${experience.id}`} type="month" value={experience.endDate} onChange={(event) => updateExperienceField(experience.id, "endDate", event.target.value)} disabled={experience.current} autoComplete="off" data-testid={`input-endDate-${experience.id}`}/>
                  <div className="flex items-center space-x-2 mt-2">
                    <Checkbox id={`current-${experience.id}`} checked={experience.current} onCheckedChange={(checked) => {
                const isCurrent = checked === true;
                updateExperienceField(experience.id, "current", isCurrent);
                if (isCurrent) {
                    updateExperienceField(experience.id, "endDate", "");
                }
            }} data-testid={`checkbox-current-${experience.id}`}/>
                    <Label htmlFor={`current-${experience.id}`} className="text-sm">
                      I currently work here
                    </Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor={`description-${experience.id}`}>Job Description *</Label>
                  <Button type="button" variant="outline" size="sm" onClick={() => handleEnhanceDescription(experience)} disabled={!experience.description.trim() || enhanceExperienceMutation.isPending} data-testid={`button-enhance-${experience.id}`}>
                    {enhanceExperienceMutation.isPending ? (<RefreshCw className="h-4 w-4 mr-2 animate-spin"/>) : (<Wand2 className="h-4 w-4 mr-2"/>)}
                    Enhance with AI
                  </Button>
                </div>
                <Textarea id={`description-${experience.id}`} placeholder="Summarize your responsibilities and achievements. Use bullets, quantify impact, and align with your target role." value={experience.description} onChange={(event) => updateExperienceField(experience.id, "description", event.target.value)} className="min-h-[140px] resize-vertical" autoComplete="off" data-testid={`textarea-description-${experience.id}`}/>
              </div>
            </CardContent>
          </Card>))}
      </div>

      <Button type="button" variant="outline" onClick={addExperience} className="w-full border-dashed" data-testid="button-add-experience">
        <Plus className="h-4 w-4 mr-2"/>
        Add another position
      </Button>

      <Card className="border-accent/20 bg-accent/5">
        <CardContent className="p-4">
          <h4 className="text-sm font-medium mb-2">Experience writing tips</h4>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>- Lead with strong action verbs (led, launched, optimized)</li>
            <li>- Add metrics whenever possible (boosted NPS by 18%)</li>
            <li>- Highlight the why, what, and measurable impact</li>
            <li>- Include tools, methodologies, or frameworks you used</li>
            <li>- Mirror high-value keywords from the job description</li>
          </ul>
        </CardContent>
      </Card>

      <div className="flex justify-between items-center pt-6 border-t border-border">
        <Button variant="ghost" onClick={onPrevious} data-testid="button-previous">
          <ArrowLeft className="h-4 w-4 mr-2"/>
          Previous
        </Button>

        <Button onClick={handleNext} disabled={!isValidForm()} data-testid="button-next">
          Continue
          <ArrowRight className="h-4 w-4 ml-2"/>
        </Button>
      </div>
    </div>);
}
