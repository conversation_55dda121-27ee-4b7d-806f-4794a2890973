/**
 * Simple Playwright test for ResumeAI local deployment
 * This script tests:
 * 1. Landing page loads correctly
 * 2. Sign in button is clickable
 * 3. Get Started button is clickable
 */

const { test, expect } = require('@playwright/test');

test.describe('ResumeAI Simple Test', () => {
  test('Landing page loads correctly', async ({ page }) => {
    // Navigate to the local deployment
    await page.goto('http://localhost:5001');
    
    // Check if the page title is correct
    await expect(page).toHaveTitle(/ResumeAI/);
    
    // Check if the main heading is present
    await expect(page.locator('h1')).toContainText('Create Professional Resumes with AI');
    
    // Check if the sign in button is present
    await expect(page.locator('a[href*="/api/login"]')).toBeVisible();
    
    // Check if the get started button is present
    await expect(page.locator('a.cta-button')).toContainText('Get Started');
    
    // Check if feature cards are present
    await expect(page.locator('.feature-card')).toHaveCount(6);
    
    // Take a screenshot to see what the page looks like
    await page.screenshot({ path: 'test-results/landing-page.png' });
  });

  test('Sign in button is clickable', async ({ page }) => {
    // Navigate to the local deployment
    await page.goto('http://localhost:5001');
    
    // Click the sign in button
    await page.click('a[href*="/api/login"]');
    
    // Wait for navigation
    await page.waitForTimeout(2000);
    
    // Take a screenshot to see what happens after clicking sign in
    await page.screenshot({ path: 'test-results/after-sign-in.png' });
    
    // Just check that we're still on a page (not necessarily what page)
    expect(await page.url()).toBeTruthy();
  });

  test('Get Started button is clickable', async ({ page }) => {
    // Navigate to the local deployment
    await page.goto('http://localhost:5001');
    
    // Click the get started button
    await page.click('a.cta-button:has-text("Get Started")');
    
    // Wait for navigation
    await page.waitForTimeout(2000);
    
    // Take a screenshot to see what happens after clicking get started
    await page.screenshot({ path: 'test-results/after-get-started.png' });
    
    // Just check that we're still on a page (not necessarily what page)
    expect(await page.url()).toBeTruthy();
  });
});