import React from "react";
export interface ConstructionPaymentApplicationData {
    applicationNo: string;
    applicationDate: string;
    periodTo: string;
    projectNo: string;
    contractDate: string;
    project: string;
    projectAddress: string;
    owner: string;
    ownerAddress: string;
    contractor: string;
    contractorAddress: string;
    contractFor: string;
    originalContractSum: number;
    netChangeByChangeOrders: number;
    workItems: Array<{
        itemNo: number;
        description: string;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        percent: number;
        balanceToFinish: number;
        retainage: number;
    }>;
    retainagePercent: number;
    retainageOnCompletedWork: number;
    retainageOnStoredMaterial: number;
    lessPreviousCertificates: number;
    distributeTo: {
        owner: boolean;
        architect: boolean;
        contractor: boolean;
        field: boolean;
    };
}
interface ConstructionPaymentApplicationProps {
    data: ConstructionPaymentApplicationData;
    logoUrl?: string;
}
export default function ConstructionPaymentApplication({ data, logoUrl }: ConstructionPaymentApplicationProps): React.JSX.Element;
export {};
//# sourceMappingURL=ConstructionPaymentApplication.d.ts.map