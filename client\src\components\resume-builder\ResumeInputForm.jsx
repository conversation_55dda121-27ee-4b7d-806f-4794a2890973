import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Upload, FileText, Loader2, CheckCircle } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
export default function ResumeInputForm({ data, onDataChange, onNext, resumeId, }) {
    const { toast } = useToast();
    const fileInputRef = useRef(null);
    const [inputMethod, setInputMethod] = useState("upload");
    const [uploadedFile, setUploadedFile] = useState(null);
    const [parseStatus, setParseStatus] = useState("idle");
    // Manual entry state
    const [personalInfo, setPersonalInfo] = useState({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        city: "",
        state: "",
    });
    // Initialize form data
    useEffect(() => {
        if (data.personalInfo) {
            setPersonalInfo(data.personalInfo);
            setInputMethod("manual");
        }
    }, [data]);
    // Upload resume mutation
    const uploadResumeMutation = useMutation({
        mutationFn: async (file) => {
            const formData = new FormData();
            formData.append("resume", file);
            if (resumeId) {
                formData.append("resumeId", resumeId);
            }
            const response = await fetch("/api/resumes/upload", {
                method: "POST",
                credentials: "include",
                body: formData,
            });
            if (!response.ok) {
                throw new Error("Failed to upload resume");
            }
            return await response.json();
        },
        onSuccess: (result) => {
            setParseStatus("success");
            onDataChange(result.draft);
            toast({
                title: "Success",
                description: "Resume uploaded and parsed successfully!",
            });
        },
        onError: (error) => {
            setParseStatus("idle");
            if (isUnauthorizedError(error)) {
                window.location.href = "/api/login";
                return;
            }
            toast({
                title: "Error",
                description: error.message || "Failed to upload resume. Please try again.",
                variant: "destructive",
            });
        },
    });
    const handleFileChange = (event) => {
        const file = event.target.files?.[0];
        if (file) {
            // Validate file type
            const allowedTypes = [
                "application/pdf",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/msword",
            ];
            if (!allowedTypes.includes(file.type)) {
                toast({
                    title: "Invalid file type",
                    description: "Please upload a PDF or DOCX file.",
                    variant: "destructive",
                });
                return;
            }
            // Validate file size (8MB max)
            if (file.size > 8 * 1024 * 1024) {
                toast({
                    title: "File too large",
                    description: "Please upload a file smaller than 8MB.",
                    variant: "destructive",
                });
                return;
            }
            setUploadedFile(file);
            setParseStatus("parsing");
            uploadResumeMutation.mutate(file);
        }
    };
    const handleManualNext = () => {
        if (!personalInfo.firstName || !personalInfo.lastName || !personalInfo.email) {
            toast({
                title: "Missing information",
                description: "Please fill in at least your name and email.",
                variant: "destructive",
            });
            return;
        }
        onDataChange({ personalInfo: personalInfo });
        onNext();
    };
    const handleUploadNext = () => {
        if (parseStatus === "success") {
            onNext();
        }
        else {
            toast({
                title: "Please upload a resume",
                description: "Upload your resume or switch to manual entry.",
                variant: "destructive",
            });
        }
    };
    return (<div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Add Your Resume</h2>
        <p className="text-muted-foreground">
          Upload your existing resume or enter your information manually
        </p>
      </div>

      {/* Input Method Selection */}
      <RadioGroup value={inputMethod} onValueChange={(value) => setInputMethod(value)} className="grid grid-cols-2 gap-4">
        <div>
          <RadioGroupItem value="upload" id="upload" className="peer sr-only"/>
          <Label htmlFor="upload" className="flex flex-col items-center justify-between rounded-lg border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary cursor-pointer">
            <Upload className="mb-3 h-6 w-6"/>
            <div className="text-center">
              <div className="font-semibold">Upload Resume</div>
              <div className="text-xs text-muted-foreground">PDF or DOCX</div>
            </div>
          </Label>
        </div>
        <div>
          <RadioGroupItem value="manual" id="manual" className="peer sr-only"/>
          <Label htmlFor="manual" className="flex flex-col items-center justify-between rounded-lg border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary cursor-pointer">
            <FileText className="mb-3 h-6 w-6"/>
            <div className="text-center">
              <div className="font-semibold">Enter Manually</div>
              <div className="text-xs text-muted-foreground">Quick form</div>
            </div>
          </Label>
        </div>
      </RadioGroup>

      {/* Upload Section */}
      {inputMethod === "upload" && (<Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <input ref={fileInputRef} type="file" accept=".pdf,.docx,.doc" onChange={handleFileChange} className="hidden" aria-label="Upload resume file"/>

              {parseStatus === "idle" && (<div className="border-2 border-dashed border-border rounded-lg p-12 text-center cursor-pointer hover:border-primary/50 transition-colors" onClick={() => fileInputRef.current?.click()}>
                  <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground"/>
                  <h3 className="font-semibold mb-2">Upload your resume</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Drag and drop or click to browse
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Supports PDF and DOCX files up to 8MB
                  </p>
                </div>)}

              {parseStatus === "parsing" && (<div className="border-2 border-primary/50 rounded-lg p-12 text-center">
                  <Loader2 className="h-12 w-12 mx-auto mb-4 text-primary animate-spin"/>
                  <h3 className="font-semibold mb-2">Parsing your resume...</h3>
                  <p className="text-sm text-muted-foreground">
                    {uploadedFile?.name}
                  </p>
                </div>)}

              {parseStatus === "success" && (<div className="border-2 border-green-500 rounded-lg p-12 text-center">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600"/>
                  <h3 className="font-semibold mb-2 text-green-700">Resume parsed successfully!</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    {uploadedFile?.name}
                  </p>
                  <Button variant="outline" size="sm" onClick={() => {
                    setParseStatus("idle");
                    setUploadedFile(null);
                }}>
                    Upload Different File
                  </Button>
                </div>)}
            </div>
          </CardContent>
        </Card>)}

      {/* Manual Entry Section */}
      {inputMethod === "manual" && (<Card>
          <CardContent className="p-6 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name *</Label>
                <Input id="firstName" value={personalInfo.firstName} onChange={(e) => setPersonalInfo({ ...personalInfo, firstName: e.target.value })} placeholder="John" autoComplete="given-name"/>
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name *</Label>
                <Input id="lastName" value={personalInfo.lastName} onChange={(e) => setPersonalInfo({ ...personalInfo, lastName: e.target.value })} placeholder="Doe" autoComplete="family-name"/>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input id="email" type="email" value={personalInfo.email} onChange={(e) => setPersonalInfo({ ...personalInfo, email: e.target.value })} placeholder="<EMAIL>" autoComplete="email"/>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input id="phone" type="tel" value={personalInfo.phone} onChange={(e) => setPersonalInfo({ ...personalInfo, phone: e.target.value })} placeholder="(*************" autoComplete="tel"/>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input id="city" value={personalInfo.city} onChange={(e) => setPersonalInfo({ ...personalInfo, city: e.target.value })} placeholder="San Francisco" autoComplete="address-level2"/>
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input id="state" value={personalInfo.state} onChange={(e) => setPersonalInfo({ ...personalInfo, state: e.target.value })} placeholder="CA" autoComplete="address-level1"/>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="linkedinUrl">LinkedIn URL</Label>
              <Input id="linkedinUrl" value={personalInfo.linkedinUrl || ""} onChange={(e) => setPersonalInfo({ ...personalInfo, linkedinUrl: e.target.value })} placeholder="https://linkedin.com/in/johndoe" autoComplete="url"/>
            </div>

            <p className="text-xs text-muted-foreground">
              * Required fields. You'll be able to add experience and skills in the next steps.
            </p>
          </CardContent>
        </Card>)}

      {/* Navigation */}
      <div className="flex justify-end">
        <Button onClick={inputMethod === "upload" ? handleUploadNext : handleManualNext} size="lg" disabled={uploadResumeMutation.isPending}>
          Next Step
          <ArrowRight className="ml-2 h-4 w-4"/>
        </Button>
      </div>
    </div>);
}
