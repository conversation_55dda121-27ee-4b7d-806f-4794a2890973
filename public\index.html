<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ResumeAI - AI-Powered Resume Builder</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f8fafc;
      color: #1e293b;
      line-height: 1.6;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    header {
      background-color: #fff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      position: sticky;
      top: 0;
      z-index: 100;
    }
    
    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 0;
    }
    
    .logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 1.5rem;
      font-weight: bold;
      color: #3b82f6;
    }
    
    .logo-icon {
      width: 2rem;
      height: 2rem;
      background-color: #3b82f6;
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }
    
    main {
      padding: 2rem 0;
    }
    
    .hero {
      text-align: center;
      padding: 4rem 0;
    }
    
    .hero h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .hero p {
      font-size: 1.25rem;
      margin-bottom: 2rem;
      color: #64748b;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .cta-button {
      display: inline-block;
      background-color: #3b82f6;
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.3s;
    }
    
    .cta-button:hover {
      background-color: #2563eb;
    }
    
    .features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin: 4rem 0;
    }
    
    .feature-card {
      background-color: white;
      padding: 2rem;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s, box-shadow 0.3s;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .feature-icon {
      width: 3rem;
      height: 3rem;
      background-color: #f1f5f9;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }
    
    .feature-card h3 {
      margin-bottom: 0.5rem;
      font-size: 1.25rem;
    }
    
    .feature-card p {
      color: #64748b;
    }
    
    footer {
      background-color: #1e293b;
      color: white;
      padding: 2rem 0;
      text-align: center;
      margin-top: 4rem;
    }
    
    .status-message {
      background-color: #fef3c7;
      border: 1px solid #f59e0b;
      color: #92400e;
      padding: 1rem;
      border-radius: 0.5rem;
      margin-bottom: 2rem;
      text-align: center;
    }
  </style>
</head>
<body>
  <header>
    <div class="container">
      <nav>
        <div class="logo">
          <div class="logo-icon">📄</div>
          ResumeAI
        </div>
        <div>
          <a href="/api/login" class="cta-button">Sign In</a>
        </div>
      </nav>
    </div>
  </header>

  <main>
    <div class="container">
      <div class="status-message">
        <strong>Development Server Running</strong> - The client application is being built. Please wait...
      </div>
      
      <section class="hero">
        <h1>Create Professional Resumes with AI</h1>
        <p>Our AI-powered platform helps you create optimized resumes that get past ATS systems and impress recruiters.</p>
        <a href="/api/login" class="cta-button">Get Started</a>
      </section>

      <section class="features">
        <div class="feature-card">
          <div class="feature-icon">🤖</div>
          <h3>AI-Powered Content</h3>
          <p>Our AI analyzes your experience and generates professional, compelling content tailored to your target job.</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🎯</div>
          <h3>ATS Optimization</h3>
          <p>Resumes are optimized with the right keywords and formatting to pass through Applicant Tracking Systems.</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">📊</div>
          <h3>Job Matching</h3>
          <p>Compare your resume against job descriptions to see how well you match and get improvement suggestions.</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🛡️</div>
          <h3>Industry Templates</h3>
          <p>Choose from professional templates designed for your specific industry and career level.</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">⏱️</div>
          <h3>Save Time</h3>
          <p>Create a professional resume in minutes, not hours. Focus on applying to jobs, not formatting.</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🌐</div>
          <h3>Multiple Formats</h3>
          <p>Export your resume in multiple formats including PDF, DOCX, and more for different application needs.</p>
        </div>
      </section>
    </div>
  </main>

  <footer>
    <div class="container">
      <p>&copy; 2025 ResumeAI. All rights reserved.</p>
    </div>
  </footer>
</body>
</html>