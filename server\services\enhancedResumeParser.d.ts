import type { Resume } from "@shared/schema";
type ResumeDraft = {
    personalInfo?: import("@shared/schema").PersonalInfo;
    professionalSummary?: string;
    workExperience?: import("@shared/schema").WorkExperience[];
    education?: import("@shared/schema").Education[];
    skills?: import("@shared/schema").Skill[];
    targetJobTitle?: string;
    industry?: string;
};
interface UploadedFile {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    size: number;
    destination: string;
    filename: string;
    path: string;
    buffer: Buffer;
}
export declare function normalizeResumeDraft(draft: ResumeDraft | Partial<Resume>): ResumeDraft;
export declare function parseUploadedResume(file: UploadedFile): Promise<{
    resume: ResumeDraft;
    metadata: any;
}>;
export declare function getSupportedFileTypes(): string[];
export declare function validateFileSize(file: UploadedFile, maxSizeMB?: number): void;
export {};
//# sourceMappingURL=enhancedResumeParser.d.ts.map