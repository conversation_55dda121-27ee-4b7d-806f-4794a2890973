import { parseUploadedResume as parseEnhancedResume, validateFileSize } from "./enhancedResumeParser.js";
function ensureIds(items, prefix) {
    if (!items)
        return undefined;
    // If the incoming value is not an array, try to coerce it into one so
    // downstream code that expects arrays doesn't crash (defensive).
    // - If it's a single object, wrap it in an array.
    // - Otherwise, return undefined to indicate no valid items.
    if (!Array.isArray(items)) {
        if (typeof items === "object" && items !== null) {
            // wrap single object into array
            items = [items];
        }
        else {
            return undefined;
        }
    }
    return items.map((item, index) => ({
        ...item,
        id: item.id ?? `${prefix}-${index + 1}`,
    }));
}
const toOptionalString = (value) => {
    if (typeof value === "string" && value.trim().length > 0) {
        return value;
    }
    return undefined;
};
export function normalizeResumeDraft(draft) {
    const personalInfo = draft.personalInfo ?? draft.personalInfo;
    const workExperience = draft.workExperience ?? draft.workExperience;
    const education = draft.education ?? draft.education;
    const skills = draft.skills ?? draft.skills;
    return {
        personalInfo: personalInfo
            ? {
                firstName: toOptionalString(personalInfo.firstName) ?? "",
                lastName: toOptionalString(personalInfo.lastName) ?? "",
                email: toOptionalString(personalInfo.email) ?? "",
                phone: toOptionalString(personalInfo.phone) ?? "",
                city: toOptionalString(personalInfo.city) ?? "",
                state: toOptionalString(personalInfo.state) ?? "",
                linkedinUrl: toOptionalString(personalInfo.linkedinUrl),
            }
            : undefined,
        professionalSummary: toOptionalString(draft.professionalSummary ?? draft.professionalSummary),
        workExperience: ensureIds(workExperience, "exp"),
        education: ensureIds(education, "edu"),
        skills: ensureIds(skills, "skill"),
        targetJobTitle: toOptionalString(draft.targetJobTitle ?? draft.targetJobTitle),
        industry: toOptionalString(draft.industry ?? draft.industry),
    };
}
export async function parseUploadedResume(file) {
    try {
        // Validate file size (max 10MB)
        validateFileSize(file);
        // Use enhanced parser
        const { resume } = await parseEnhancedResume(file);
        return resume;
    }
    catch (error) {
        console.error("Error parsing uploaded resume:", error);
        throw new Error(`Failed to parse resume: ${error.message}`);
    }
}
// Export utility functions
export { getSupportedFileTypes } from "./enhancedResumeParser.js";
