import passport from "passport";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";
import session from "express-session";
import type { Express, RequestHandler } from "express";
import connectPgSimple from "connect-pg-simple";
import pg from "pg";
import { storage } from "./storage.js";

// Environment configuration
const APP_URL = process.env.APP_URL || "http://localhost:5001";
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const NODE_ENV = process.env.NODE_ENV || "development";

export function getSession() {
  const sessionTtl = 7 * 24 * 60 * 60 * 1000;
  const DATABASE_URL = process.env.DATABASE_URL;
  const isProduction = NODE_ENV === "production";

  let sessionStore: any;

  if (DATABASE_URL && !DATABASE_URL.startsWith("file:")) {
    // Use PostgreSQL for sessions in production/serverless
    const PgSessionStore = connectPgSimple(session);
    const { Pool } = pg;
    const pool = new Pool({
      connectionString: DATABASE_URL,
      ssl: isProduction ? { rejectUnauthorized: false } : false
    });

    sessionStore = new PgSessionStore({
      pool,
      tableName: "sessions",
      createTableIfMissing: true,
      ttl: Math.floor(sessionTtl / 1000),
    });
    console.log("Using PostgreSQL session store");
  } else {
    // Use memory store for local development (SQLite doesn't work in serverless)
    console.log("Using memory session store (development only - sessions won't persist)");
    // sessionStore will be undefined, which makes express-session use memory store
  }

  const sessionSecret = process.env.SESSION_SECRET || "dev-secret-key-change-in-production-12345";
  console.log("Session secret configured:", sessionSecret ? "✓" : "✗");

  return session({
    secret: sessionSecret,
    store: sessionStore,
    resave: false,
    saveUninitialized: false,
    cookie: {
      httpOnly: true,
      secure: isProduction,
      maxAge: sessionTtl,
      sameSite: isProduction ? 'lax' : 'lax',
    },
  });
}

async function upsertUser(profile: any) {
  await storage.upsertUser({
    id: profile.id,
    email: profile.emails?.[0]?.value || "",
    firstName: profile.name?.givenName || "",
    lastName: profile.name?.familyName || "",
    profileImageUrl: profile.photos?.[0]?.value || "",
  });
}

export async function setupAuth(app: Express) {
  app.set("trust proxy", 1);
  app.use(getSession());
  app.use(passport.initialize());
  app.use(passport.session());

  // Local development fallback
  const allowLocalLogin = NODE_ENV === "development" && process.env.ALLOW_LOCAL_LOGIN === "true";

  if (allowLocalLogin) {
    console.log("Local login fallback enabled for development");
  }

  // Configure Google OAuth strategy only if credentials are provided
  if (GOOGLE_CLIENT_ID && GOOGLE_CLIENT_SECRET) {
    passport.use(
      new GoogleStrategy(
        {
          clientID: GOOGLE_CLIENT_ID,
          clientSecret: GOOGLE_CLIENT_SECRET,
          callbackURL: `${APP_URL}/api/callback`,
          scope: ["profile", "email"],
        },
        async (accessToken, refreshToken, profile, done) => {
          try {
            await upsertUser(profile);
            done(null, profile);
          } catch (error) {
            console.error("Error in Google OAuth callback:", error);
            done(error as Error);
          }
        }
      )
    );
    console.log("Google OAuth configured");
  } else if (!allowLocalLogin) {
    console.warn("⚠️  Google OAuth credentials not configured. Set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET.");
  }

  passport.serializeUser((user: any, done) => {
    done(null, user.id);
  });

  passport.deserializeUser(async (id: string, done) => {
    try {
      const user = await storage.getUser(id);
      done(null, user);
    } catch (error) {
      done(error);
    }
  });

  // Login route
  app.get("/api/login", async (req, res, next) => {
    try {
      // If explicitly requesting dev login
      if (req.query.dev === "true" && allowLocalLogin) {
        const devUser = {
          id: "dev-user",
          email: "<EMAIL>",
          firstName: "Demo",
          lastName: "User",
          profileImageUrl: "",
        };

        await storage.upsertUser(devUser);
        console.log("Persisted dev user for local development:", devUser.id);
        req.login(devUser, (err) => {
          if (err) return next(err);
          return res.redirect("/");
        });
        return;
      }

      // Production: Use Google OAuth
      if (!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_SECRET) {
        return res.status(503).json({
          message: "Service temporarily unavailable. Authentication not configured.",
        });
      }

      passport.authenticate("google", {
        scope: ["profile", "email"],
      })(req, res, next);
    } catch (error) {
      console.error("Error in /api/login:", error);
      return res.status(503).json({
        message: "Service temporarily unavailable",
      });
    }
  });

  // Explicit dev login route
  app.get("/api/dev-login", (req, res) => {
    if (!allowLocalLogin) {
      return res.status(403).json({ message: "Dev login not enabled" });
    }
    
    res.redirect("/api/login?dev=true");
  });

  // OAuth callback route
  app.get("/api/callback", (req, res, next) => {
    if (allowLocalLogin) {
      return res.redirect("/");
    }

    passport.authenticate("google", {
      successRedirect: "/",
      failureRedirect: "/api/login",
    })(req, res, next);
  });

  // Logout route
  app.get("/api/logout", (req, res) => {
    req.logout((err: any) => {
      if (err) {
        console.error("Logout error:", err);
      }
      
      // Clear the session
      (req as any).session?.destroy((sessionErr: any) => {
        if (sessionErr) {
          console.error("Session destruction error:", sessionErr);
        }
        
        // Clear any cookies
        res.clearCookie('connect.sid', { path: '/' });
        res.redirect("/");
      });
      
      if (!err) {
        // Clear any cookies
        res.clearCookie('connect.sid', { path: '/' });
        res.redirect("/");
      }
    });
  });
}

export const isAuthenticated: RequestHandler = (req, res, next) => {
  if (req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ message: "Unauthorized" });
};
