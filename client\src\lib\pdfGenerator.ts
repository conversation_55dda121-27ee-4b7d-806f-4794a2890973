import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

interface PDFOptions {
  filename?: string;
  orientation?: 'portrait' | 'landscape';
  unit?: 'pt' | 'px' | 'in' | 'mm' | 'cm' | 'ex' | 'em' | 'pc';
  format?: string | number[];
}

/**
 * Generate a PDF from an HTML element
 * @param element - The HTML element to convert to PDF
 * @param options - PDF generation options
 */
export async function generatePDFFromElement(
  element: HTMLElement,
  options: PDFOptions = {}
): Promise<Blob> {
  const {
    filename = 'document.pdf',
    orientation = 'portrait',
    unit = 'pt',
    format = 'letter'
  } = options;

  try {
    // Capture the element as a canvas
    const canvas = await html2canvas(element, {
      scale: 2, // Higher quality
      useCORS: true,
      logging: false,
      backgroundColor: '#ffffff',
    });

    // Get canvas dimensions
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;

    // Create PDF
    const pdf = new jsPDF({
      orientation,
      unit,
      format,
    });

    // Calculate the aspect ratio
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    // Scale to fit
    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
    const scaledWidth = imgWidth * ratio;
    const scaledHeight = imgHeight * ratio;

    // Center the image
    const x = (pdfWidth - scaledWidth) / 2;
    const y = 0;

    // Add image to PDF
    const imgData = canvas.toDataURL('image/png');
    pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);

    // Return as Blob
    return pdf.output('blob');
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF');
  }
}

/**
 * Download a PDF blob
 * @param blob - The PDF blob to download
 * @param filename - The filename for the download
 */
export function downloadPDFBlob(blob: Blob, filename: string): void {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
}

/**
 * Generate and download a PDF from an HTML element
 * @param element - The HTML element to convert to PDF
 * @param filename - The filename for the download
 * @param options - Additional PDF options
 */
export async function generateAndDownloadPDF(
  element: HTMLElement,
  filename: string,
  options: PDFOptions = {}
): Promise<void> {
  try {
    const blob = await generatePDFFromElement(element, { ...options, filename });
    downloadPDFBlob(blob, filename);
  } catch (error) {
    console.error('Error generating and downloading PDF:', error);
    throw error;
  }
}
