-- Optimize database for minimal storage and better performance

-- Add indexes for frequently queried fields
CREATE INDEX IF NOT EXISTS idx_resumes_user_id ON resumes(user_id);
CREATE INDEX IF NOT EXISTS idx_resumes_status ON resumes(status);
CREATE INDEX IF NOT EXISTS idx_resumes_payment_status ON resumes(payment_status);
CREATE INDEX IF NOT EXISTS idx_resumes_created_at ON resumes(created_at);
CREATE INDEX IF NOT EXISTS idx_resumes_updated_at ON resumes(updated_at);

-- Add composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_resumes_user_status ON resumes(user_id, status);
CREATE INDEX IF NOT EXISTS idx_resumes_user_payment ON resumes(user_id, payment_status);

-- Optimize text storage by using appropriate column types
-- For resumes table
ALTER TABLE resumes ALTER COLUMN title TYPE VARCHAR(255);
ALTER TABLE resumes ALTER COLUMN professional_summary TYPE TEXT;
ALTER TABLE resumes ALTER COLUMN target_job_title TYPE VARCHAR(255);
ALTER TABLE resumes ALTER COLUMN target_company TYPE VARCHAR(255);
ALTER TABLE resumes ALTER COLUMN job_description TYPE TEXT;
ALTER TABLE resumes ALTER COLUMN template_id TYPE VARCHAR(50);

-- For personal_info table
ALTER TABLE personal_info ALTER COLUMN first_name TYPE VARCHAR(100);
ALTER TABLE personal_info ALTER COLUMN last_name TYPE VARCHAR(100);
ALTER TABLE personal_info ALTER COLUMN email TYPE VARCHAR(255);
ALTER TABLE personal_info ALTER COLUMN phone TYPE VARCHAR(50);
ALTER TABLE personal_info ALTER COLUMN city TYPE VARCHAR(100);
ALTER TABLE personal_info ALTER COLUMN state TYPE VARCHAR(100);
ALTER TABLE personal_info ALTER COLUMN linkedin_url TYPE VARCHAR(500);

-- For work_experience table
ALTER TABLE work_experience ALTER COLUMN position TYPE VARCHAR(255);
ALTER TABLE work_experience ALTER COLUMN company TYPE VARCHAR(255);
ALTER TABLE work_experience ALTER COLUMN description TYPE TEXT;

-- For education table
ALTER TABLE education ALTER COLUMN degree TYPE VARCHAR(255);
ALTER TABLE education ALTER COLUMN institution TYPE VARCHAR(255);
ALTER TABLE education ALTER COLUMN field_of_study TYPE VARCHAR(255);
ALTER TABLE education ALTER COLUMN description TYPE TEXT;

-- For skills table
ALTER TABLE skills ALTER COLUMN name TYPE VARCHAR(100);
ALTER TABLE skills ALTER COLUMN category TYPE VARCHAR(100);
ALTER TABLE skills ALTER COLUMN level TYPE VARCHAR(50);

-- Add indexes for foreign keys
CREATE INDEX IF NOT EXISTS idx_personal_info_resume_id ON personal_info(resume_id);
CREATE INDEX IF NOT EXISTS idx_work_experience_resume_id ON work_experience(resume_id);
CREATE INDEX IF NOT EXISTS idx_education_resume_id ON education(resume_id);
CREATE INDEX IF NOT EXISTS idx_skills_resume_id ON skills(resume_id);

-- Optimize payments table
CREATE INDEX IF NOT EXISTS idx_payments_resume_id ON payments(resume_id);
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- Add composite indexes for payments
CREATE INDEX IF NOT EXISTS idx_payments_user_status ON payments(user_id, status);
CREATE INDEX IF NOT EXISTS idx_payments_resume_status ON payments(resume_id, status);

-- Create a table for resume analytics (to track usage without bloating main tables)
CREATE TABLE IF NOT EXISTS resume_analytics (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  action VARCHAR(50) NOT NULL, -- 'view', 'download', 'edit', 'share'
  metadata JSONB, -- Additional data about the action
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for analytics table
CREATE INDEX IF NOT EXISTS idx_resume_analytics_resume_id ON resume_analytics(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_analytics_user_id ON resume_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_analytics_action ON resume_analytics(action);
CREATE INDEX IF NOT EXISTS idx_resume_analytics_created_at ON resume_analytics(created_at);

-- Create a table for job description cache (to avoid re-scraping)
CREATE TABLE IF NOT EXISTS job_description_cache (
  id SERIAL PRIMARY KEY,
  url_hash VARCHAR(64) UNIQUE NOT NULL, -- SHA-256 hash of the URL
  url TEXT NOT NULL,
  title VARCHAR(255),
  company VARCHAR(255),
  location VARCHAR(255),
  description TEXT,
  requirements TEXT,
  benefits TEXT,
  salary VARCHAR(255),
  job_type VARCHAR(100),
  posted_date VARCHAR(100),
  scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL '30 days')
);

-- Add indexes for job description cache
CREATE INDEX IF NOT EXISTS idx_job_description_cache_url_hash ON job_description_cache(url_hash);
CREATE INDEX IF NOT EXISTS idx_job_description_cache_expires_at ON job_description_cache(expires_at);

-- Create a function to clean up expired job description cache
CREATE OR REPLACE FUNCTION cleanup_expired_job_cache()
RETURNS void AS $$
BEGIN
  DELETE FROM job_description_cache WHERE expires_at < CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- Add a table for resume templates (to store template metadata)
CREATE TABLE IF NOT EXISTS resume_templates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  industry VARCHAR(100),
  layout VARCHAR(50),
  color_scheme VARCHAR(50),
  font VARCHAR(50),
  preview_url VARCHAR(500),
  keywords TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume templates
CREATE INDEX IF NOT EXISTS idx_resume_templates_industry ON resume_templates(industry);
CREATE INDEX IF NOT EXISTS idx_resume_templates_is_active ON resume_templates(is_active);

-- Create a trigger to update the updated_at column for resume_templates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_resume_templates_updated_at
  BEFORE UPDATE ON resume_templates
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a view for active resume templates
CREATE OR REPLACE VIEW active_resume_templates AS
SELECT * FROM resume_templates WHERE is_active = true;

-- Add a table for user preferences (to store user settings without bloating users table)
CREATE TABLE IF NOT EXISTS user_preferences (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  key VARCHAR(100) NOT NULL,
  value TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, key)
);

-- Add indexes for user preferences
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_key ON user_preferences(key);

-- Create a trigger to update the updated_at column for user_preferences
CREATE TRIGGER update_user_preferences_updated_at
  BEFORE UPDATE ON user_preferences
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a table for resume sharing (to track shared resumes)
CREATE TABLE IF NOT EXISTS resume_shares (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  share_token VARCHAR(255) UNIQUE NOT NULL,
  expires_at TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume shares
CREATE INDEX IF NOT EXISTS idx_resume_shares_resume_id ON resume_shares(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_shares_user_id ON resume_shares(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_shares_share_token ON resume_shares(share_token);
CREATE INDEX IF NOT EXISTS idx_resume_shares_expires_at ON resume_shares(expires_at);

-- Create a function to clean up expired resume shares
CREATE OR REPLACE FUNCTION cleanup_expired_resume_shares()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_shares WHERE expires_at < CURRENT_TIMESTAMP OR is_active = false;
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume feedback (to collect user feedback on resumes)
CREATE TABLE IF NOT EXISTS resume_feedback (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  feedback TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume feedback
CREATE INDEX IF NOT EXISTS idx_resume_feedback_resume_id ON resume_feedback(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_feedback_user_id ON resume_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_feedback_rating ON resume_feedback(rating);

-- Create a table for resume versions (to track changes to resumes)
CREATE TABLE IF NOT EXISTS resume_versions (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  data JSONB NOT NULL, -- Store the entire resume data as JSON
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(resume_id, version_number)
);

-- Add indexes for resume versions
CREATE INDEX IF NOT EXISTS idx_resume_versions_resume_id ON resume_versions(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_versions_created_at ON resume_versions(created_at);

-- Create a function to limit the number of versions per resume
CREATE OR REPLACE FUNCTION limit_resume_versions()
RETURNS TRIGGER AS $$
BEGIN
  -- Keep only the latest 5 versions for each resume
  DELETE FROM resume_versions
  WHERE resume_id = NEW.resume_id
  AND id NOT IN (
    SELECT id FROM resume_versions
    WHERE resume_id = NEW.resume_id
    ORDER BY version_number DESC
    LIMIT 5
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to limit resume versions
CREATE TRIGGER limit_resume_versions_trigger
  AFTER INSERT ON resume_versions
  FOR EACH ROW EXECUTE FUNCTION limit_resume_versions();

-- Create a table for resume exports (to track resume exports)
CREATE TABLE IF NOT EXISTS resume_exports (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  format VARCHAR(50) NOT NULL, -- 'pdf', 'docx', etc.
  file_path VARCHAR(500),
  file_size INTEGER, -- in bytes
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume exports
CREATE INDEX IF NOT EXISTS idx_resume_exports_resume_id ON resume_exports(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_exports_user_id ON resume_exports(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_exports_format ON resume_exports(format);
CREATE INDEX IF NOT EXISTS idx_resume_exports_created_at ON resume_exports(created_at);

-- Create a table for resume imports (to track resume imports)
CREATE TABLE IF NOT EXISTS resume_imports (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  file_type VARCHAR(50) NOT NULL, -- 'pdf', 'docx', etc.
  file_size INTEGER, -- in bytes
  status VARCHAR(50) NOT NULL, -- 'pending', 'processing', 'completed', 'failed'
  error_message TEXT,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE SET NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume imports
CREATE INDEX IF NOT EXISTS idx_resume_imports_user_id ON resume_imports(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_imports_status ON resume_imports(status);
CREATE INDEX IF NOT EXISTS idx_resume_imports_resume_id ON resume_imports(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_imports_created_at ON resume_imports(created_at);

-- Create a trigger to update the updated_at column for resume_imports
CREATE TRIGGER update_resume_imports_updated_at
  BEFORE UPDATE ON resume_imports
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a table for resume AI suggestions (to store AI-generated suggestions)
CREATE TABLE IF NOT EXISTS resume_ai_suggestions (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  suggestion_type VARCHAR(100) NOT NULL, -- 'summary', 'experience', 'skills', etc.
  original_text TEXT,
  suggested_text TEXT,
  is_applied BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume AI suggestions
CREATE INDEX IF NOT EXISTS idx_resume_ai_suggestions_resume_id ON resume_ai_suggestions(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_ai_suggestions_user_id ON resume_ai_suggestions(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_ai_suggestions_type ON resume_ai_suggestions(suggestion_type);
CREATE INDEX IF NOT EXISTS idx_resume_ai_suggestions_is_applied ON resume_ai_suggestions(is_applied);

-- Create a table for resume AI analyses (to store AI analysis results)
CREATE TABLE IF NOT EXISTS resume_ai_analyses (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  analysis_type VARCHAR(100) NOT NULL, -- 'ats_score', 'keyword_match', etc.
  analysis_data JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume AI analyses
CREATE INDEX IF NOT EXISTS idx_resume_ai_analyses_resume_id ON resume_ai_analyses(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_ai_analyses_user_id ON resume_ai_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_ai_analyses_type ON resume_ai_analyses(analysis_type);
CREATE INDEX IF NOT EXISTS idx_resume_ai_analyses_created_at ON resume_ai_analyses(created_at);

-- Create a table for resume keywords (to store extracted keywords)
CREATE TABLE IF NOT EXISTS resume_keywords (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  keyword VARCHAR(255) NOT NULL,
  relevance_score INTEGER DEFAULT 0, -- 0-100
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(resume_id, keyword)
);

-- Add indexes for resume keywords
CREATE INDEX IF NOT EXISTS idx_resume_keywords_resume_id ON resume_keywords(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_keywords_keyword ON resume_keywords(keyword);
CREATE INDEX IF NOT EXISTS idx_resume_keywords_relevance_score ON resume_keywords(relevance_score);

-- Create a table for resume job matches (to store job matching results)
CREATE TABLE IF NOT EXISTS resume_job_matches (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  job_url TEXT NOT NULL,
  job_title VARCHAR(255),
  job_company VARCHAR(255),
  match_score INTEGER DEFAULT 0, -- 0-100
  match_data JSONB, -- Additional matching data
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume job matches
CREATE INDEX IF NOT EXISTS idx_resume_job_matches_resume_id ON resume_job_matches(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_job_matches_user_id ON resume_job_matches(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_job_matches_match_score ON resume_job_matches(match_score);
CREATE INDEX IF NOT EXISTS idx_resume_job_matches_created_at ON resume_job_matches(created_at);

-- Create a table for resume notifications (to store user notifications)
CREATE TABLE IF NOT EXISTS resume_notifications (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  type VARCHAR(100) NOT NULL, -- 'payment_success', 'payment_failed', etc.
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSONB, -- Additional notification data
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume notifications
CREATE INDEX IF NOT EXISTS idx_resume_notifications_user_id ON resume_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_notifications_type ON resume_notifications(type);
CREATE INDEX IF NOT EXISTS idx_resume_notifications_is_read ON resume_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_resume_notifications_created_at ON resume_notifications(created_at);

-- Create a function to clean up old notifications
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_notifications WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume subscriptions (to store user subscription data)
CREATE TABLE IF NOT EXISTS resume_subscriptions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  plan VARCHAR(100) NOT NULL, -- 'free', 'premium', etc.
  status VARCHAR(100) NOT NULL, -- 'active', 'cancelled', 'expired', etc.
  stripe_subscription_id VARCHAR(255),
  stripe_customer_id VARCHAR(255),
  current_period_start TIMESTAMP,
  current_period_end TIMESTAMP,
  cancel_at_period_end BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume subscriptions
CREATE INDEX IF NOT EXISTS idx_resume_subscriptions_user_id ON resume_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_subscriptions_status ON resume_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_resume_subscriptions_stripe_subscription_id ON resume_subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_resume_subscriptions_current_period_end ON resume_subscriptions(current_period_end);

-- Create a trigger to update the updated_at column for resume_subscriptions
CREATE TRIGGER update_resume_subscriptions_updated_at
  BEFORE UPDATE ON resume_subscriptions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a table for resume usage (to track user usage)
CREATE TABLE IF NOT EXISTS resume_usage (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  action VARCHAR(100) NOT NULL, -- 'create', 'edit', 'download', 'share', etc.
  metadata JSONB, -- Additional usage data
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume usage
CREATE INDEX IF NOT EXISTS idx_resume_usage_user_id ON resume_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_usage_resume_id ON resume_usage(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_usage_action ON resume_usage(action);
CREATE INDEX IF NOT EXISTS idx_resume_usage_created_at ON resume_usage(created_at);

-- Create a function to clean up old usage data
CREATE OR REPLACE FUNCTION cleanup_old_usage_data()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_usage WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume limits (to store user limits)
CREATE TABLE IF NOT EXISTS resume_limits (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  limit_type VARCHAR(100) NOT NULL, -- 'resumes', 'downloads', 'shares', etc.
  limit_value INTEGER NOT NULL,
  current_value INTEGER DEFAULT 0,
  reset_period VARCHAR(100), -- 'daily', 'weekly', 'monthly', 'yearly', 'never'
  last_reset_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, limit_type)
);

-- Add indexes for resume limits
CREATE INDEX IF NOT EXISTS idx_resume_limits_user_id ON resume_limits(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_limits_limit_type ON resume_limits(limit_type);

-- Create a trigger to update the updated_at column for resume_limits
CREATE TRIGGER update_resume_limits_updated_at
  BEFORE UPDATE ON resume_limits
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a function to reset resume limits
CREATE OR REPLACE FUNCTION reset_resume_limits()
RETURNS void AS $$
BEGIN
  -- Reset daily limits
  UPDATE resume_limits
  SET current_value = 0, last_reset_at = CURRENT_TIMESTAMP
  WHERE reset_period = 'daily'
  AND last_reset_at < CURRENT_DATE;

  -- Reset weekly limits
  UPDATE resume_limits
  SET current_value = 0, last_reset_at = CURRENT_TIMESTAMP
  WHERE reset_period = 'weekly'
  AND last_reset_at < CURRENT_DATE - INTERVAL '7 days';

  -- Reset monthly limits
  UPDATE resume_limits
  SET current_value = 0, last_reset_at = CURRENT_TIMESTAMP
  WHERE reset_period = 'monthly'
  AND last_reset_at < DATE_TRUNC('month', CURRENT_DATE);

  -- Reset yearly limits
  UPDATE resume_limits
  SET current_value = 0, last_reset_at = CURRENT_TIMESTAMP
  WHERE reset_period = 'yearly'
  AND last_reset_at < DATE_TRUNC('year', CURRENT_DATE);
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume features (to store feature flags)
CREATE TABLE IF NOT EXISTS resume_features (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  is_enabled BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume features
CREATE INDEX IF NOT EXISTS idx_resume_features_name ON resume_features(name);
CREATE INDEX IF NOT EXISTS idx_resume_features_is_enabled ON resume_features(is_enabled);

-- Create a trigger to update the updated_at column for resume_features
CREATE TRIGGER update_resume_features_updated_at
  BEFORE UPDATE ON resume_features
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a table for resume user features (to store user-specific feature access)
CREATE TABLE IF NOT EXISTS resume_user_features (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  feature_id INTEGER REFERENCES resume_features(id) ON DELETE CASCADE,
  is_enabled BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, feature_id)
);

-- Add indexes for resume user features
CREATE INDEX IF NOT EXISTS idx_resume_user_features_user_id ON resume_user_features(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_user_features_feature_id ON resume_user_features(feature_id);

-- Create a trigger to update the updated_at column for resume user features
CREATE TRIGGER update_resume_user_features_updated_at
  BEFORE UPDATE ON resume_user_features
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a table for resume audit logs (to track changes to resumes)
CREATE TABLE IF NOT EXISTS resume_audit_logs (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  action VARCHAR(100) NOT NULL, -- 'create', 'update', 'delete', etc.
  table_name VARCHAR(100) NOT NULL, -- 'resumes', 'personal_info', etc.
  record_id INTEGER NOT NULL,
  old_values JSONB,
  new_values JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume audit logs
CREATE INDEX IF NOT EXISTS idx_resume_audit_logs_resume_id ON resume_audit_logs(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_audit_logs_user_id ON resume_audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_audit_logs_action ON resume_audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_resume_audit_logs_table_name ON resume_audit_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_resume_audit_logs_created_at ON resume_audit_logs(created_at);

-- Create a function to clean up old audit logs
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_audit_logs WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume sessions (to track user sessions)
CREATE TABLE IF NOT EXISTS resume_sessions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume sessions
CREATE INDEX IF NOT EXISTS idx_resume_sessions_user_id ON resume_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_sessions_session_token ON resume_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_resume_sessions_expires_at ON resume_sessions(expires_at);

-- Create a function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_sessions WHERE expires_at < CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume API keys (to store user API keys)
CREATE TABLE IF NOT EXISTS resume_api_keys (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  api_key VARCHAR(255) UNIQUE NOT NULL,
  permissions JSONB, -- List of permissions
  is_active BOOLEAN DEFAULT true,
  expires_at TIMESTAMP,
  last_used_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume API keys
CREATE INDEX IF NOT EXISTS idx_resume_api_keys_user_id ON resume_api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_api_keys_api_key ON resume_api_keys(api_key);
CREATE INDEX IF NOT EXISTS idx_resume_api_keys_is_active ON resume_api_keys(is_active);
CREATE INDEX IF NOT EXISTS idx_resume_api_keys_expires_at ON resume_api_keys(expires_at);

-- Create a trigger to update the updated_at column for resume API keys
CREATE TRIGGER update_resume_api_keys_updated_at
  BEFORE UPDATE ON resume_api_keys
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a function to clean up expired API keys
CREATE OR REPLACE FUNCTION cleanup_expired_api_keys()
RETURNS void AS $$
BEGIN
  UPDATE resume_api_keys SET is_active = false WHERE expires_at < CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume API usage (to track API usage)
CREATE TABLE IF NOT EXISTS resume_api_usage (
  id SERIAL PRIMARY KEY,
  api_key_id INTEGER REFERENCES resume_api_keys(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  endpoint VARCHAR(255) NOT NULL,
  method VARCHAR(10) NOT NULL,
  status_code INTEGER NOT NULL,
  response_time INTEGER, -- in milliseconds
  request_size INTEGER, -- in bytes
  response_size INTEGER, -- in bytes
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume API usage
CREATE INDEX IF NOT EXISTS idx_resume_api_usage_api_key_id ON resume_api_usage(api_key_id);
CREATE INDEX IF NOT EXISTS idx_resume_api_usage_user_id ON resume_api_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_api_usage_endpoint ON resume_api_usage(endpoint);
CREATE INDEX IF NOT EXISTS idx_resume_api_usage_created_at ON resume_api_usage(created_at);

-- Create a function to clean up old API usage data
CREATE OR REPLACE FUNCTION cleanup_old_api_usage_data()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_api_usage WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume webhooks (to store user webhooks)
CREATE TABLE IF NOT EXISTS resume_webhooks (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  url VARCHAR(500) NOT NULL,
  events JSONB, -- List of events to trigger on
  secret VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume webhooks
CREATE INDEX IF NOT EXISTS idx_resume_webhooks_user_id ON resume_webhooks(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_webhooks_is_active ON resume_webhooks(is_active);

-- Create a trigger to update the updated_at column for resume webhooks
CREATE TRIGGER update_resume_webhooks_updated_at
  BEFORE UPDATE ON resume_webhooks
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a table for resume webhook logs (to store webhook delivery logs)
CREATE TABLE IF NOT EXISTS resume_webhook_logs (
  id SERIAL PRIMARY KEY,
  webhook_id INTEGER REFERENCES resume_webhooks(id) ON DELETE CASCADE,
  event VARCHAR(100) NOT NULL,
  payload JSONB,
  response_code INTEGER,
  response_body TEXT,
  delivered_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume webhook logs
CREATE INDEX IF NOT EXISTS idx_resume_webhook_logs_webhook_id ON resume_webhook_logs(webhook_id);
CREATE INDEX IF NOT EXISTS idx_resume_webhook_logs_event ON resume_webhook_logs(event);
CREATE INDEX IF NOT EXISTS idx_resume_webhook_logs_delivered_at ON resume_webhook_logs(delivered_at);

-- Create a function to clean up old webhook logs
CREATE OR REPLACE FUNCTION cleanup_old_webhook_logs()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_webhook_logs WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume integrations (to store third-party integrations)
CREATE TABLE IF NOT EXISTS resume_integrations (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  provider VARCHAR(100) NOT NULL, -- 'linkedin', 'indeed', etc.
  provider_user_id VARCHAR(255),
  access_token TEXT,
  refresh_token TEXT,
  expires_at TIMESTAMP,
  scopes JSONB, -- List of granted scopes
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume integrations
CREATE INDEX IF NOT EXISTS idx_resume_integrations_user_id ON resume_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_integrations_provider ON resume_integrations(provider);
CREATE INDEX IF NOT EXISTS idx_resume_integrations_is_active ON resume_integrations(is_active);

-- Create a trigger to update the updated_at column for resume integrations
CREATE TRIGGER update_resume_integrations_updated_at
  BEFORE UPDATE ON resume_integrations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a function to clean up expired integrations
CREATE OR REPLACE FUNCTION cleanup_expired_integrations()
RETURNS void AS $$
BEGIN
  UPDATE resume_integrations SET is_active = false WHERE expires_at < CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume integration logs (to store integration activity logs)
CREATE TABLE IF NOT EXISTS resume_integration_logs (
  id SERIAL PRIMARY KEY,
  integration_id INTEGER REFERENCES resume_integrations(id) ON DELETE CASCADE,
  action VARCHAR(100) NOT NULL,
  request_data JSONB,
  response_data JSONB,
  status_code INTEGER,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume integration logs
CREATE INDEX IF NOT EXISTS idx_resume_integration_logs_integration_id ON resume_integration_logs(integration_id);
CREATE INDEX IF NOT EXISTS idx_resume_integration_logs_action ON resume_integration_logs(action);
CREATE INDEX IF NOT EXISTS idx_resume_integration_logs_created_at ON resume_integration_logs(created_at);

-- Create a function to clean up old integration logs
CREATE OR REPLACE FUNCTION cleanup_old_integration_logs()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_integration_logs WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume backups (to store resume backups)
CREATE TABLE IF NOT EXISTS resume_backups (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  backup_data JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume backups
CREATE INDEX IF NOT EXISTS idx_resume_backups_resume_id ON resume_backups(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_backups_user_id ON resume_backups(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_backups_created_at ON resume_backups(created_at);

-- Create a function to limit the number of backups per resume
CREATE OR REPLACE FUNCTION limit_resume_backups()
RETURNS TRIGGER AS $$
BEGIN
  -- Keep only the latest 3 backups for each resume
  DELETE FROM resume_backups
  WHERE resume_id = NEW.resume_id
  AND id NOT IN (
    SELECT id FROM resume_backups
    WHERE resume_id = NEW.resume_id
    ORDER BY created_at DESC
    LIMIT 3
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to limit resume backups
CREATE TRIGGER limit_resume_backups_trigger
  AFTER INSERT ON resume_backups
  FOR EACH ROW EXECUTE FUNCTION limit_resume_backups();

-- Create a table for restore points (to store restore points for resumes)
CREATE TABLE IF NOT EXISTS resume_restore_points (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  restore_data JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume restore points
CREATE INDEX IF NOT EXISTS idx_resume_restore_points_resume_id ON resume_restore_points(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_restore_points_user_id ON resume_restore_points(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_restore_points_created_at ON resume_restore_points(created_at);

-- Create a function to limit the number of restore points per resume
CREATE OR REPLACE FUNCTION limit_resume_restore_points()
RETURNS TRIGGER AS $$
BEGIN
  -- Keep only the latest 5 restore points for each resume
  DELETE FROM resume_restore_points
  WHERE resume_id = NEW.resume_id
  AND id NOT IN (
    SELECT id FROM resume_restore_points
    WHERE resume_id = NEW.resume_id
    ORDER BY created_at DESC
    LIMIT 5
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to limit resume restore points
CREATE TRIGGER limit_resume_restore_points_trigger
  AFTER INSERT ON resume_restore_points
  FOR EACH ROW EXECUTE FUNCTION limit_resume_restore_points();

-- Create a table for resume activity logs (to track user activity on resumes)
CREATE TABLE IF NOT EXISTS resume_activity_logs (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  activity_type VARCHAR(100) NOT NULL, -- 'view', 'edit', 'download', 'share', etc.
  activity_data JSONB, -- Additional activity data
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume activity logs
CREATE INDEX IF NOT EXISTS idx_resume_activity_logs_resume_id ON resume_activity_logs(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_activity_logs_user_id ON resume_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_activity_logs_activity_type ON resume_activity_logs(activity_type);
CREATE INDEX IF NOT EXISTS idx_resume_activity_logs_created_at ON resume_activity_logs(created_at);

-- Create a function to clean up old activity logs
CREATE OR REPLACE FUNCTION cleanup_old_activity_logs()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_activity_logs WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume performance metrics (to store performance metrics)
CREATE TABLE IF NOT EXISTS resume_performance_metrics (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  metric_name VARCHAR(100) NOT NULL, -- 'ats_score', 'keyword_match', etc.
  metric_value NUMERIC NOT NULL,
  metric_data JSONB, -- Additional metric data
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume performance metrics
CREATE INDEX IF NOT EXISTS idx_resume_performance_metrics_resume_id ON resume_performance_metrics(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_performance_metrics_user_id ON resume_performance_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_performance_metrics_metric_name ON resume_performance_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_resume_performance_metrics_created_at ON resume_performance_metrics(created_at);

-- Create a function to clean up old performance metrics
CREATE OR REPLACE FUNCTION cleanup_old_performance_metrics()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_performance_metrics WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '6 months';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume recommendations (to store AI-generated recommendations)
CREATE TABLE IF NOT EXISTS resume_recommendations (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  recommendation_type VARCHAR(100) NOT NULL, -- 'skills', 'experience', 'education', etc.
  recommendation_text TEXT NOT NULL,
  recommendation_data JSONB, -- Additional recommendation data
  is_dismissed BOOLEAN DEFAULT false,
  is_applied BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume recommendations
CREATE INDEX IF NOT EXISTS idx_resume_recommendations_resume_id ON resume_recommendations(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_recommendations_user_id ON resume_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_recommendations_type ON resume_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_resume_recommendations_is_dismissed ON resume_recommendations(is_dismissed);
CREATE INDEX IF NOT EXISTS idx_resume_recommendations_is_applied ON resume_recommendations(is_applied);

-- Create a function to clean up old recommendations
CREATE OR REPLACE FUNCTION cleanup_old_recommendations()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_recommendations WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume comparisons (to store resume comparison results)
CREATE TABLE IF NOT EXISTS resume_comparisons (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  resume_id_1 INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  resume_id_2 INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  comparison_data JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume comparisons
CREATE INDEX IF NOT EXISTS idx_resume_comparisons_user_id ON resume_comparisons(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_comparisons_resume_id_1 ON resume_comparisons(resume_id_1);
CREATE INDEX IF NOT EXISTS idx_resume_comparisons_resume_id_2 ON resume_comparisons(resume_id_2);
CREATE INDEX IF NOT EXISTS idx_resume_comparisons_created_at ON resume_comparisons(created_at);

-- Create a function to clean up old comparisons
CREATE OR REPLACE FUNCTION cleanup_old_comparisons()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_comparisons WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume insights (to store AI-generated insights)
CREATE TABLE IF NOT EXISTS resume_insights (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  insight_type VARCHAR(100) NOT NULL, -- 'strengths', 'weaknesses', 'opportunities', etc.
  insight_text TEXT NOT NULL,
  insight_data JSONB, -- Additional insight data
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume insights
CREATE INDEX IF NOT EXISTS idx_resume_insights_resume_id ON resume_insights(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_insights_user_id ON resume_insights(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_insights_type ON resume_insights(insight_type);
CREATE INDEX IF NOT EXISTS idx_resume_insights_created_at ON resume_insights(created_at);

-- Create a function to clean up old insights
CREATE OR REPLACE FUNCTION cleanup_old_insights()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_insights WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume benchmarks (to store industry benchmarks)
CREATE TABLE IF NOT EXISTS resume_benchmarks (
  id SERIAL PRIMARY KEY,
  industry VARCHAR(100) NOT NULL,
  job_level VARCHAR(100) NOT NULL, -- 'entry', 'mid', 'senior', 'executive'
  metric_name VARCHAR(100) NOT NULL, -- 'ats_score', 'keyword_density', etc.
  metric_value NUMERIC NOT NULL,
  metric_data JSONB, -- Additional metric data
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume benchmarks
CREATE INDEX IF NOT EXISTS idx_resume_benchmarks_industry ON resume_benchmarks(industry);
CREATE INDEX IF NOT EXISTS idx_resume_benchmarks_job_level ON resume_benchmarks(job_level);
CREATE INDEX IF NOT EXISTS idx_resume_benchmarks_metric_name ON resume_benchmarks(metric_name);

-- Create a trigger to update the updated_at column for resume benchmarks
CREATE TRIGGER update_resume_benchmarks_updated_at
  BEFORE UPDATE ON resume_benchmarks
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a table for resume benchmark comparisons (to store resume vs benchmark comparisons)
CREATE TABLE IF NOT EXISTS resume_benchmark_comparisons (
  id SERIAL PRIMARY KEY,
  resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  benchmark_id INTEGER REFERENCES resume_benchmarks(id) ON DELETE CASCADE,
  comparison_value NUMERIC NOT NULL,
  comparison_data JSONB, -- Additional comparison data
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume benchmark comparisons
CREATE INDEX IF NOT EXISTS idx_resume_benchmark_comparisons_resume_id ON resume_benchmark_comparisons(resume_id);
CREATE INDEX IF NOT EXISTS idx_resume_benchmark_comparisons_user_id ON resume_benchmark_comparisons(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_benchmark_comparisons_benchmark_id ON resume_benchmark_comparisons(benchmark_id);
CREATE INDEX IF NOT EXISTS idx_resume_benchmark_comparisons_created_at ON resume_benchmark_comparisons(created_at);

-- Create a function to clean up old benchmark comparisons
CREATE OR REPLACE FUNCTION cleanup_old_benchmark_comparisons()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_benchmark_comparisons WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume goals (to store user resume goals)
CREATE TABLE IF NOT EXISTS resume_goals (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  goal_type VARCHAR(100) NOT NULL, -- 'job_application', 'interview', 'offer', etc.
  goal_title VARCHAR(255) NOT NULL,
  goal_description TEXT,
  target_date DATE,
  is_completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume goals
CREATE INDEX IF NOT EXISTS idx_resume_goals_user_id ON resume_goals(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_goals_goal_type ON resume_goals(goal_type);
CREATE INDEX IF NOT EXISTS idx_resume_goals_is_completed ON resume_goals(is_completed);
CREATE INDEX IF NOT EXISTS idx_resume_goals_target_date ON resume_goals(target_date);

-- Create a trigger to update the updated_at column for resume goals
CREATE TRIGGER update_resume_goals_updated_at
  BEFORE UPDATE ON resume_goals
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a table for resume goal milestones (to store goal milestones)
CREATE TABLE IF NOT EXISTS resume_goal_milestones (
  id SERIAL PRIMARY KEY,
  goal_id INTEGER REFERENCES resume_goals(id) ON DELETE CASCADE,
  milestone_title VARCHAR(255) NOT NULL,
  milestone_description TEXT,
  is_completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume goal milestones
CREATE INDEX IF NOT EXISTS idx_resume_goal_milestones_goal_id ON resume_goal_milestones(goal_id);
CREATE INDEX IF NOT EXISTS idx_resume_goal_milestones_is_completed ON resume_goal_milestones(is_completed);

-- Create a trigger to update the updated_at column for resume goal milestones
CREATE TRIGGER update_resume_goal_milestones_updated_at
  BEFORE UPDATE ON resume_goal_milestones
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a table for resume goal notes (to store goal notes)
CREATE TABLE IF NOT EXISTS resume_goal_notes (
  id SERIAL PRIMARY KEY,
  goal_id INTEGER REFERENCES resume_goals(id) ON DELETE CASCADE,
  note_text TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume goal notes
CREATE INDEX IF NOT EXISTS idx_resume_goal_notes_goal_id ON resume_goal_notes(goal_id);

-- Create a trigger to update the updated_at column for resume goal notes
CREATE TRIGGER update_resume_goal_notes_updated_at
  BEFORE UPDATE ON resume_goal_notes
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a table for resume goal reminders (to store goal reminders)
CREATE TABLE IF NOT EXISTS resume_goal_reminders (
  id SERIAL PRIMARY KEY,
  goal_id INTEGER REFERENCES resume_goals(id) ON DELETE CASCADE,
  reminder_date TIMESTAMP NOT NULL,
  reminder_message TEXT,
  is_sent BOOLEAN DEFAULT false,
  sent_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume goal reminders
CREATE INDEX IF NOT EXISTS idx_resume_goal_reminders_goal_id ON resume_goal_reminders(goal_id);
CREATE INDEX IF NOT EXISTS idx_resume_goal_reminders_reminder_date ON resume_goal_reminders(reminder_date);
CREATE INDEX IF NOT EXISTS idx_resume_goal_reminders_is_sent ON resume_goal_reminders(is_sent);

-- Create a function to clean up old sent reminders
CREATE OR REPLACE FUNCTION cleanup_old_sent_reminders()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_goal_reminders WHERE is_sent = true AND sent_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Create a table for resume goal attachments (to store goal attachments)
CREATE TABLE IF NOT EXISTS resume_goal_attachments (
  id SERIAL PRIMARY KEY,
  goal_id INTEGER REFERENCES resume_goals(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  file_type VARCHAR(100) NOT NULL,
  file_size INTEGER, -- in bytes
  file_path VARCHAR(500) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume goal attachments
CREATE INDEX IF NOT EXISTS idx_resume_goal_attachments_goal_id ON resume_goal_attachments(goal_id);

-- Create a table for resume goal collaborators (to store goal collaborators)
CREATE TABLE IF NOT EXISTS resume_goal_collaborators (
  id SERIAL PRIMARY KEY,
  goal_id INTEGER REFERENCES resume_goals(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  role VARCHAR(100) NOT NULL, -- 'owner', 'viewer', 'editor', etc.
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume goal collaborators
CREATE INDEX IF NOT EXISTS idx_resume_goal_collaborators_goal_id ON resume_goal_collaborators(goal_id);
CREATE INDEX IF NOT EXISTS idx_resume_goal_collaborators_user_id ON resume_goal_collaborators(user_id);

-- Create a table for resume goal comments (to store goal comments)
CREATE TABLE IF NOT EXISTS resume_goal_comments (
  id SERIAL PRIMARY KEY,
  goal_id INTEGER REFERENCES resume_goals(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  comment_text TEXT NOT NULL,
  parent_id INTEGER REFERENCES resume_goal_comments(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume goal comments
CREATE INDEX IF NOT EXISTS idx_resume_goal_comments_goal_id ON resume_goal_comments(goal_id);
CREATE INDEX IF NOT EXISTS idx_resume_goal_comments_user_id ON resume_goal_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_goal_comments_parent_id ON resume_goal_comments(parent_id);

-- Create a trigger to update the updated_at column for resume goal comments
CREATE TRIGGER update_resume_goal_comments_updated_at
  BEFORE UPDATE ON resume_goal_comments
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a table for resume goal activities (to store goal activities)
CREATE TABLE IF NOT EXISTS resume_goal_activities (
  id SERIAL PRIMARY KEY,
  goal_id INTEGER REFERENCES resume_goals(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  activity_type VARCHAR(100) NOT NULL, -- 'created', 'updated', 'completed', etc.
  activity_data JSONB, -- Additional activity data
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for resume goal activities
CREATE INDEX IF NOT EXISTS idx_resume_goal_activities_goal_id ON resume_goal_activities(goal_id);
CREATE INDEX IF NOT EXISTS idx_resume_goal_activities_user_id ON resume_goal_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_goal_activities_activity_type ON resume_goal_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_resume_goal_activities_created_at ON resume_goal_activities(created_at);

-- Create a function to clean up old goal activities
CREATE OR REPLACE FUNCTION cleanup_old_goal_activities()
RETURNS void AS $$
BEGIN
  DELETE FROM resume_goal_activities WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run cleanup functions (this would be implemented in your job scheduler)
-- Note: This is just a comment as the actual scheduling would depend on your environment
-- Example with pg_cron (if available):
-- SELECT cron.schedule('cleanup-job', '0 2 * * *', 'SELECT cleanup_expired_job_cache(); SELECT cleanup_expired_resume_shares(); SELECT cleanup_old_notifications(); SELECT cleanup_old_usage_data(); SELECT cleanup_old_audit_logs(); SELECT cleanup_old_api_usage_data(); SELECT cleanup_old_webhook_logs(); SELECT cleanup_old_integration_logs(); SELECT cleanup_old_activity_logs(); SELECT cleanup_old_performance_metrics(); SELECT cleanup_old_recommendations(); SELECT cleanup_old_comparisons(); SELECT cleanup_old_insights(); SELECT cleanup_old_benchmark_comparisons(); SELECT cleanup_old_sent_reminders(); SELECT cleanup_old_goal_activities();');