import OpenAI from "openai";
import { GoogleGenerativeAI } from "@google/generative-ai";
import type { PersonalInfo, WorkExperience, Skill, Education, Resume } from "@shared/schema";
import { normalizeDraft, postProcessSummary, postProcessExperienceBullets } from "./postprocess.js";

// ATS optimization keywords and patterns
const ATS_KEYWORDS = {
  actionVerbs: [
    "achieved", "managed", "led", "developed", "implemented", "created", "improved",
    "increased", "decreased", "optimized", "streamlined", "coordinated", "executed",
    "facilitated", "negotiated", "presented", "analyzed", "designed", "launched",
    "trained", "mentored", "supervised", "directed", "organized", "planned"
  ],
  skills: [
    "project management", "data analysis", "communication", "leadership", "teamwork",
    "problem solving", "critical thinking", "time management", "adaptability", "creativity",
    "collaboration", "attention to detail", "customer service", "sales", "marketing",
    "strategic planning", "budget management", "quality assurance", "risk assessment"
  ],
  technical: [
    "javascript", "python", "java", "react", "node.js", "sql", "aws", "azure", "docker",
    "kubernetes", "git", "ci/cd", "agile", "scrum", "rest api", "graphql", "mongodb",
    "postgresql", "mysql", "redis", "elasticsearch", "tensorflow", "pytorch", "tableau"
  ],
  metrics: [
    "%", "dollar", "$", "number", "count", "time", "rate", "ratio", "score", "growth",
    "reduction", "increase", "improvement", "efficiency", "productivity", "savings"
  ]
};

const geminiKey = process.env.GEMINI_API_KEY;
const openAiKey = process.env.OPENAI_API_KEY;

const gemini = geminiKey ? new GoogleGenerativeAI(geminiKey) : null;
const openai = openAiKey ? new OpenAI({ apiKey: openAiKey }) : null;

const DEFAULT_JSON_MODEL = "gpt-4o-mini";
const DEFAULT_TEXT_MODEL = "gpt-4o-mini";
const DEFAULT_GEMINI_JSON_MODEL = "gemini-2.5-pro";
const DEFAULT_GEMINI_TEXT_MODEL = "gemini-2.5-flash";

type JsonSchema<T> = {
  name: string;
  schema: T;
};

function safeJsonParse<T>(raw: string): T | null {
  try {
    return JSON.parse(raw) as T;
  } catch (error) {
    console.warn("Failed to parse JSON from model output", error);
    return null;
  }
}

async function callModel({
  systemPrompt,
  userPrompt,
  expectJson = false,
}: {
  systemPrompt: string;
  userPrompt: string;
  expectJson?: boolean;
}): Promise<string> {
  if (gemini) {
    const model = gemini.getGenerativeModel({
      model: expectJson ? DEFAULT_GEMINI_JSON_MODEL : DEFAULT_GEMINI_TEXT_MODEL,
      systemInstruction: systemPrompt,
      generationConfig: expectJson ? { responseMimeType: "application/json" } : undefined,
    });

    const result = await model.generateContent(userPrompt);
    const text = result.response.text();
    if (text) {
      return text;
    }
  }

  if (openai) {
    const response = await openai.chat.completions.create({
      model: expectJson ? DEFAULT_JSON_MODEL : DEFAULT_TEXT_MODEL,
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt },
      ],
      response_format: expectJson ? { type: "json_object" } : undefined,
    });

    const content = response.choices[0]?.message?.content;
    if (Array.isArray(content)) {
      return content.map((part) => (typeof part === "string" ? part : part.text ?? "")).join("");
    }
    return content ?? "";
  }

  throw new Error("No AI provider configured. Set GEMINI_API_KEY or OPENAI_API_KEY.");
}

async function generateJson<T>({ systemPrompt, userPrompt }: { systemPrompt: string; userPrompt: string; }): Promise<T | null> {
  try {
    const output = await callModel({ systemPrompt, userPrompt, expectJson: true });
    return safeJsonParse<T>(output);
  } catch (error) {
    console.error("Failed to generate JSON response", error);
    return null;
  }
}

async function generateText({ systemPrompt, userPrompt }: { systemPrompt: string; userPrompt: string; }): Promise<string | null> {
  try {
    return await callModel({ systemPrompt, userPrompt, expectJson: false });
  } catch (error) {
    console.error("Failed to generate text response", error);
    return null;
  }
}

export interface JobAnalysisResult {
  keywords: string[];
  requirements: string[];
  suggestedImprovements: string[];
  matchScore: string;
}

function fallbackKeywords(text: string): string[] {
  return Array.from(new Set(
    text
      .toLowerCase()
      .match(/[a-zA-Z][a-zA-Z+\/#\-]{2,}/g) ?? []
  ))
    .filter((word) => word.length > 3)
    .slice(0, 25);
}

export async function analyzeJobDescription(jobDescription: string): Promise<JobAnalysisResult> {
  const systemPrompt = `You are an expert resume and job analysis specialist. Analyze job descriptions to extract key information for ATS optimization and resume tailoring. Always respond with strict JSON.`;
  const userPrompt = `Analyze this job description and provide a JSON response with these keys: keywords (20-30 terms), requirements (short bullet phrases), suggestedImprovements (short bullet phrases), matchScore (one of entry, mid, senior, executive).\n\nJob Description:\n${jobDescription}`;
  const json = await generateJson<JobAnalysisResult>({ systemPrompt, userPrompt });

  if (json) {
    return json;
  }

  const keywords = fallbackKeywords(jobDescription).slice(0, 20);
  return {
    keywords,
    requirements: keywords.slice(0, 8).map((term) => `Highlight experience with ${term}`),
    suggestedImprovements: [
      "Quantify at least three achievements",
      "Add metrics demonstrating business impact",
      "Mirror terminology from the job description",
    ],
    matchScore: "mid",
  };
}

export async function generateProfessionalSummary(
  personalInfo: PersonalInfo,
  experience: WorkExperience[],
  skills: Skill[],
  targetJob?: string,
  jobKeywords?: string[]
): Promise<string> {
  // Extract key information from experience
  const yearsExperience = experience.reduce((total, exp) => {
    const startYear = parseInt(exp.startDate.split('-')[0]);
    const endYear = exp.current ? new Date().getFullYear() : parseInt(exp.endDate?.split('-')[0] || startYear.toString());
    return total + (endYear - startYear);
  }, 0);
  
  const topSkills = skills.slice(0, 8).map(skill => skill.name);
  const recentPositions = experience.slice(0, 3).map(exp => exp.position);
  
  // Combine job keywords with ATS keywords for better optimization
  const allKeywords = [
    ...(jobKeywords || []),
    ...ATS_KEYWORDS.actionVerbs.slice(0, 5),
    ...ATS_KEYWORDS.skills.slice(0, 5)
  ];
  
  const jobKeywordStr = allKeywords.length > 0
    ? allKeywords.slice(0, 15).join(", ")
    : "";

  const systemPrompt = `You are an expert resume writer and ATS optimization specialist. Write a professional summary that:
1. Is 3-4 sentences long (max 150 words)
2. Highlights years of experience and key achievements with quantifiable results
3. Incorporates relevant skills and expertise from the provided keywords
4. Is tailored for the target job position
5. Uses strong action verbs and includes metrics where possible
6. Is highly ATS-friendly with appropriate keywords and formatting
7. Avoids first-person pronouns (I, me, my)
8. Is professional and compelling
9. Includes industry-specific terminology when relevant

Focus on being concise yet impactful while ensuring maximum ATS compatibility.`;

  const userPrompt = `Write an ATS-optimized professional summary for:
- Name: ${personalInfo.firstName} ${personalInfo.lastName}
- Years of Experience: ${yearsExperience}
- Recent Positions: ${recentPositions.join(", ")}
- Top Skills: ${topSkills.join(", ")}
- Target Job: ${targetJob || "Not specified"}
- Job Keywords & ATS Terms: ${jobKeywordStr || "Not specified"}

Make sure to:
1. Include at least 5-7 relevant keywords naturally
2. Use action verbs from the beginning of sentences
3. Incorporate quantifiable achievements (%, $, numbers)
4. Match the tone and requirements of the target job
5. Ensure high ATS score potential`;

  const response = await generateText({ systemPrompt, userPrompt });
  if (response) {
    return postProcessSummary(response.trim());
  }

  return postProcessSummary(`${personalInfo.firstName || "Experienced"} ${personalInfo.lastName || "professional"} with ${yearsExperience} years of experience delivering measurable results. Skilled in ${topSkills.slice(0, 4).join(", ")} with experience leading high-impact initiatives. Passionate about driving outcomes in ${targetJob || "dynamic roles"}.`);
}

export async function enhanceExperienceDescription(
  experience: WorkExperience,
  jobKeywords?: string[]
): Promise<string> {
  // Combine job keywords with ATS keywords for better optimization
  const allKeywords = [
    ...(jobKeywords || []),
    ...ATS_KEYWORDS.actionVerbs,
    ...ATS_KEYWORDS.metrics
  ];
  
  const jobKeywordStr = allKeywords.length > 0
    ? allKeywords.slice(0, 20).join(", ")
    : "";

  const systemPrompt = `You are an expert resume writer and ATS optimization specialist. Enhance the work experience description to:
1. Be highly achievement-oriented with quantifiable results
2. Include specific metrics (%, $, numbers, time saved, etc.)
3. Begin each bullet point with strong action verbs
4. Incorporate relevant ATS keywords naturally throughout
5. Use industry-specific terminology when appropriate
6. Be perfectly formatted for ATS parsing (no special characters, consistent formatting)
7. Be concise (3-5 bullet points maximum)
8. Focus entirely on accomplishments, not responsibilities
9. Use the STAR method (Situation, Task, Action, Result) for each bullet
10. Ensure maximum ATS compatibility and keyword density

Use bullet points (•) for each achievement and ensure each point is impactful.`;

  const userPrompt = `Enhance this work experience description for maximum ATS optimization:
- Position: ${experience.position}
- Company: ${experience.company}
- Current Description: ${experience.description}
- Job Keywords & ATS Terms: ${jobKeywordStr || "Not specified"}

Transform this into 3-5 highly impactful bullet points that:
1. Start with powerful action verbs
2. Include specific, quantifiable achievements
3. Incorporate relevant ATS keywords naturally
4. Demonstrate clear results and impact
5. Are perfectly formatted for ATS systems

Example format: • [Action Verb] [Specific Task] resulting in [Quantifiable Result]`;

  const response = await generateText({ systemPrompt, userPrompt });
  if (response) {
    const lines = response
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0)
      .map((line) => (line.startsWith("-") || line.startsWith("•") ? line : `- ${line}`));
    const processed = postProcessExperienceBullets(lines);
    return processed.join("\n");
  }

  return `- ${experience.description}`;
}

export async function suggestSkillsForJob(
  jobDescription: string,
  currentSkills: string[]
): Promise<string[]> {
  // Create a comprehensive list of ATS-relevant skills
  const atsSkillCategories = {
    technical: ATS_KEYWORDS.technical.join(", "),
    action: ATS_KEYWORDS.actionVerbs.join(", "),
    soft: ATS_KEYWORDS.skills.join(", ")
  };
  
  const systemPrompt = `You are an expert career coach, resume writer, and ATS optimization specialist. Based on the job description, suggest the most relevant skills that would maximize ATS compatibility and make the candidate competitive.
    
    Consider:
    1. Hard skills explicitly mentioned or implied in the job description
    2. Soft skills that would be valuable for this role
    3. Technical skills and tools that are commonly used in this field
    4. Industry-specific skills that would make the candidate stand out
    5. Skills that complement the candidate's existing skillset
    6. ATS-friendly skill keywords that match industry standards
    7. Skills that demonstrate both competence and growth potential
    
    ATS Skills Reference:
    - Technical: ${atsSkillCategories.technical}
    - Soft Skills: ${atsSkillCategories.soft}
    - Actions: ${atsSkillCategories.action}
    
    Return a JSON array of skill strings (max 15 skills). Focus on the most important and relevant skills that will pass ATS screening and impress recruiters.`;

  const userPrompt = `Based on this job description, suggest the most ATS-relevant skills for the resume:
    
    Job Description:
    ${jobDescription}
    
    Candidate's Current Skills:
    ${currentSkills.join(", ")}
    
    Suggest 10-15 additional relevant skills that:
1. Will maximize ATS compatibility and match score
2. Are specifically mentioned or strongly implied in the job description
3. Demonstrate the candidate's qualifications for this role
4. Include industry-standard terminology
5. Complement the candidate's existing skillset

Prioritize skills that will help the resume pass ATS screening.`;

  const json = await generateJson<{ suggestedSkills: string[] }>({ systemPrompt, userPrompt });
  if (json?.suggestedSkills?.length) {
    return json.suggestedSkills;
  }

  const keywords = fallbackKeywords(jobDescription)
    .filter((keyword) => !currentSkills.includes(keyword))
    .slice(0, 12);

  return keywords;
}

export interface ResumeDraft {
  personalInfo?: PersonalInfo;
  professionalSummary?: string;
  workExperience?: WorkExperience[];
  education?: Education[];
  skills?: Skill[];
  targetJobTitle?: string;
  industry?: string;
}

export async function parseResumeFromText(rawText: string): Promise<ResumeDraft> {
  const systemPrompt = `You are an expert resume analyst. Convert raw resume text into structured JSON fields matching the schema provided. Respond with valid JSON only.`;
  const userPrompt = `Parse the following resume text and return JSON with keys: personalInfo, professionalSummary, workExperience, education, skills.\n\nResume Text:\n${rawText}`;
  const json = await generateJson<ResumeDraft>({ systemPrompt, userPrompt });

  if (json) {
    return normalizeDraft(json) as ResumeDraft;
  }

  return {
    professionalSummary: rawText.split(/\n+/)[0]?.slice(0, 280) ?? "",
    skills: fallbackKeywords(rawText).slice(0, 10).map((name, index) => ({
      id: `skill-${index}`,
      name,
      level: "advanced",
      category: "general",
    })),
  };
}

export interface ResumeBriefInput {
  role: string;
  yearsExperience: number;
  topSkills: string[];
  accomplishments: string[];
  industry?: string;
}

export async function generateResumeFromBrief(brief: ResumeBriefInput): Promise<ResumeDraft> {
  const systemPrompt = `You create full resume drafts based on short briefs. Output strict JSON matching the schema requested.`;
  const userPrompt = `Create a resume JSON structure with personalInfo, professionalSummary, workExperience (3 roles max), education (1-2 entries), skills (with id/name/level/category).\nRole: ${brief.role}\nExperience: ${brief.yearsExperience} years\nIndustry: ${brief.industry ?? "General"}\nTop Skills: ${brief.topSkills.join(", ")}\nSignature accomplishments: ${brief.accomplishments.join(" | ")}`;

  const json = await generateJson<ResumeDraft>({ systemPrompt, userPrompt });
  if (json) {
    return normalizeDraft(json) as ResumeDraft;
  }

  const fallbackSkills = brief.topSkills.map((name, index) => ({
    id: `skill-${index}`,
    name,
    level: "advanced",
    category: "core",
  }));

  return {
    professionalSummary: `${brief.yearsExperience}+ years of experience in ${brief.industry ?? "leading"} as a ${brief.role}. Skilled in ${brief.topSkills.slice(0, 4).join(", ")}. Known for ${brief.accomplishments[0] ?? "delivering measurable outcomes"}.`,
    skills: fallbackSkills,
    targetJobTitle: brief.role,
    industry: brief.industry,
  };
}

export function synthesizeResumeWithExisting(
  current: Partial<Resume>,
  draft: ResumeDraft
): Partial<Resume> {
  return {
    ...current,
    personalInfo: draft.personalInfo ?? current.personalInfo,
    professionalSummary: draft.professionalSummary ?? current.professionalSummary,
    workExperience: draft.workExperience ?? current.workExperience,
    education: draft.education ?? current.education,
    skills: draft.skills ?? current.skills,
    targetJobTitle: draft.targetJobTitle ?? current.targetJobTitle,
    industry: draft.industry ?? current.industry,
  };
}
