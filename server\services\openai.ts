import OpenAI from "openai";
import { GoogleGenerativeAI } from "@google/generative-ai";
import type { PersonalInfo, WorkExperience, Skill, Education, Resume } from "@shared/schema";

const geminiKey = process.env.GEMINI_API_KEY;
const openAiKey = process.env.OPENAI_API_KEY;

const gemini = geminiKey ? new GoogleGenerativeAI(geminiKey) : null;
const openai = openAiKey ? new OpenAI({ apiKey: openAiKey }) : null;

const DEFAULT_JSON_MODEL = "gpt-4o-mini";
const DEFAULT_TEXT_MODEL = "gpt-4o-mini";
const DEFAULT_GEMINI_JSON_MODEL = "gemini-2.5-pro";
const DEFAULT_GEMINI_TEXT_MODEL = "gemini-2.5-flash";

type JsonSchema<T> = {
  name: string;
  schema: T;
};

function safeJsonParse<T>(raw: string): T | null {
  try {
    return JSON.parse(raw) as T;
  } catch (error) {
    console.warn("Failed to parse <PERSON><PERSON><PERSON> from model output", error);
    return null;
  }
}

async function callModel({
  systemPrompt,
  userPrompt,
  expectJson = false,
}: {
  systemPrompt: string;
  userPrompt: string;
  expectJson?: boolean;
}): Promise<string> {
  if (gemini) {
    const model = gemini.getGenerativeModel({
      model: expectJson ? DEFAULT_GEMINI_JSON_MODEL : DEFAULT_GEMINI_TEXT_MODEL,
      systemInstruction: systemPrompt,
      generationConfig: expectJson ? { responseMimeType: "application/json" } : undefined,
    });

    const result = await model.generateContent(userPrompt);
    const text = result.response.text();
    if (text) {
      return text;
    }
  }

  if (openai) {
    const response = await openai.chat.completions.create({
      model: expectJson ? DEFAULT_JSON_MODEL : DEFAULT_TEXT_MODEL,
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt },
      ],
      response_format: expectJson ? { type: "json_object" } : undefined,
    });

    const content = response.choices[0]?.message?.content;
    if (Array.isArray(content)) {
      return content.map((part) => (typeof part === "string" ? part : part.text ?? "")).join("");
    }
    return content ?? "";
  }

  throw new Error("No AI provider configured. Set GEMINI_API_KEY or OPENAI_API_KEY.");
}

async function generateJson<T>({ systemPrompt, userPrompt }: { systemPrompt: string; userPrompt: string; }): Promise<T | null> {
  try {
    const output = await callModel({ systemPrompt, userPrompt, expectJson: true });
    return safeJsonParse<T>(output);
  } catch (error) {
    console.error("Failed to generate JSON response", error);
    return null;
  }
}

async function generateText({ systemPrompt, userPrompt }: { systemPrompt: string; userPrompt: string; }): Promise<string | null> {
  try {
    return await callModel({ systemPrompt, userPrompt, expectJson: false });
  } catch (error) {
    console.error("Failed to generate text response", error);
    return null;
  }
}

export interface JobAnalysisResult {
  keywords: string[];
  requirements: string[];
  suggestedImprovements: string[];
  matchScore: string;
}

function fallbackKeywords(text: string): string[] {
  return Array.from(new Set(
    text
      .toLowerCase()
      .match(/[a-zA-Z][a-zA-Z+\/#\-]{2,}/g) ?? []
  ))
    .filter((word) => word.length > 3)
    .slice(0, 25);
}

export async function analyzeJobDescription(jobDescription: string): Promise<JobAnalysisResult> {
  const systemPrompt = `You are an expert resume and job analysis specialist. Analyze job descriptions to extract key information for ATS optimization and resume tailoring. Always respond with strict JSON.`;
  const userPrompt = `Analyze this job description and provide a JSON response with these keys: keywords (20-30 terms), requirements (short bullet phrases), suggestedImprovements (short bullet phrases), matchScore (one of entry, mid, senior, executive).\n\nJob Description:\n${jobDescription}`;
  const json = await generateJson<JobAnalysisResult>({ systemPrompt, userPrompt });

  if (json) {
    return json;
  }

  const keywords = fallbackKeywords(jobDescription).slice(0, 20);
  return {
    keywords,
    requirements: keywords.slice(0, 8).map((term) => `Highlight experience with ${term}`),
    suggestedImprovements: [
      "Quantify at least three achievements",
      "Add metrics demonstrating business impact",
      "Mirror terminology from the job description",
    ],
    matchScore: "mid",
  };
}

export async function generateProfessionalSummary(
  personalInfo: PersonalInfo,
  experience: WorkExperience[],
  skills: Skill[],
  targetJob?: string,
  jobKeywords?: string[]
): Promise<string> {
  const systemPrompt = `You are a professional resume writer specializing in creating compelling professional summaries. Write in first person, be concise (3-4 sentences), and focus on achievements and differentiated value.`;
  const userPrompt = `Craft a professional summary.\nName: ${personalInfo.firstName ?? ""} ${personalInfo.lastName ?? ""}\nTarget Role: ${targetJob || "General professional"}\nKey Skills: ${skills.map((s) => s.name).join(", ")}\nPriority Keywords: ${(jobKeywords ?? []).join(", ")}\nRecent Roles:\n${experience.slice(0, 3).map((exp) => `- ${exp.position} at ${exp.company}: ${exp.description}`).join("\n")}`;

  const response = await generateText({ systemPrompt, userPrompt });
  if (response) {
    return response.trim();
  }

  return `${personalInfo.firstName || "Experienced"} ${personalInfo.lastName || "professional"} with a track record of delivering measurable results. Skilled in ${skills.slice(0, 4).map((s) => s.name).join(", ")} with experience leading high-impact initiatives. Passionate about driving outcomes in ${targetJob || "dynamic roles"}.`;
}

export async function enhanceExperienceDescription(
  experience: WorkExperience,
  jobKeywords?: string[]
): Promise<string> {
  const systemPrompt = `You are a professional resume writer. Rewrite job descriptions as crisp bullet points using strong action verbs, metrics, and ATS-friendly language.`;
  const userPrompt = `Rewrite the following experience into 3-4 bullet points. Each bullet must start with a hyphen and include clear results. Incorporate these keywords naturally: ${(jobKeywords ?? []).join(", ")}.\n\nPosition: ${experience.position}\nCompany: ${experience.company}\nDescription: ${experience.description}`;

  const response = await generateText({ systemPrompt, userPrompt });
  if (response) {
    return response
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0)
      .map((line) => (line.startsWith("-") ? line : `- ${line}`))
      .join("\n");
  }

  return `- ${experience.description}`;
}

export async function suggestSkillsForJob(
  jobDescription: string,
  currentSkills: string[]
): Promise<string[]> {
  const systemPrompt = `You are a career counselor specializing in skill gap analysis. Suggest relevant skills based on job requirements that the candidate should highlight or develop.`;
  const userPrompt = `Suggest 10-15 skills in JSON with key "suggestedSkills". Job description:\n${jobDescription}\n\nExisting skills: ${currentSkills.join(", ")}`;

  const json = await generateJson<{ suggestedSkills: string[] }>({ systemPrompt, userPrompt });
  if (json?.suggestedSkills?.length) {
    return json.suggestedSkills;
  }

  const keywords = fallbackKeywords(jobDescription)
    .filter((keyword) => !currentSkills.includes(keyword))
    .slice(0, 12);

  return keywords;
}

export interface ResumeDraft {
  personalInfo?: PersonalInfo;
  professionalSummary?: string;
  workExperience?: WorkExperience[];
  education?: Education[];
  skills?: Skill[];
  targetJobTitle?: string;
  industry?: string;
}

export async function parseResumeFromText(rawText: string): Promise<ResumeDraft> {
  const systemPrompt = `You are an expert resume analyst. Convert raw resume text into structured JSON fields matching the schema provided. Respond with valid JSON only.`;
  const userPrompt = `Parse the following resume text and return JSON with keys: personalInfo, professionalSummary, workExperience, education, skills.\n\nResume Text:\n${rawText}`;
  const json = await generateJson<ResumeDraft>({ systemPrompt, userPrompt });

  if (json) {
    return json;
  }

  return {
    professionalSummary: rawText.split(/\n+/)[0]?.slice(0, 280) ?? "",
    skills: fallbackKeywords(rawText).slice(0, 10).map((name, index) => ({
      id: `skill-${index}`,
      name,
      level: "advanced",
      category: "general",
    })),
  };
}

export interface ResumeBriefInput {
  role: string;
  yearsExperience: number;
  topSkills: string[];
  accomplishments: string[];
  industry?: string;
}

export async function generateResumeFromBrief(brief: ResumeBriefInput): Promise<ResumeDraft> {
  const systemPrompt = `You create full resume drafts based on short briefs. Output strict JSON matching the schema requested.`;
  const userPrompt = `Create a resume JSON structure with personalInfo, professionalSummary, workExperience (3 roles max), education (1-2 entries), skills (with id/name/level/category).\nRole: ${brief.role}\nExperience: ${brief.yearsExperience} years\nIndustry: ${brief.industry ?? "General"}\nTop Skills: ${brief.topSkills.join(", ")}\nSignature accomplishments: ${brief.accomplishments.join(" | ")}`;

  const json = await generateJson<ResumeDraft>({ systemPrompt, userPrompt });
  if (json) {
    return json;
  }

  const fallbackSkills = brief.topSkills.map((name, index) => ({
    id: `skill-${index}`,
    name,
    level: "advanced",
    category: "core",
  }));

  return {
    professionalSummary: `${brief.yearsExperience}+ years of experience in ${brief.industry ?? "leading"} as a ${brief.role}. Skilled in ${brief.topSkills.slice(0, 4).join(", ")}. Known for ${brief.accomplishments[0] ?? "delivering measurable outcomes"}.`,
    skills: fallbackSkills,
    targetJobTitle: brief.role,
    industry: brief.industry,
  };
}

export function synthesizeResumeWithExisting(
  current: Partial<Resume>,
  draft: ResumeDraft
): Partial<Resume> {
  return {
    ...current,
    personalInfo: draft.personalInfo ?? current.personalInfo,
    professionalSummary: draft.professionalSummary ?? current.professionalSummary,
    workExperience: draft.workExperience ?? current.workExperience,
    education: draft.education ?? current.education,
    skills: draft.skills ?? current.skills,
    targetJobTitle: draft.targetJobTitle ?? current.targetJobTitle,
    industry: draft.industry ?? current.industry,
  };
}
