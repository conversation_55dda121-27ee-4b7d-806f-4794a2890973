import { useState, useEffect } from "react";
import { useParams } from "wouter";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import StepNavigation from "@/components/resume-builder/StepNavigation";
import PersonalInfoForm from "@/components/resume-builder/PersonalInfoForm";
import ProfessionalSummaryForm from "@/components/resume-builder/ProfessionalSummaryForm";
import ExperienceForm from "@/components/resume-builder/ExperienceForm";
import EducationSkillsForm from "@/components/resume-builder/EducationSkillsForm";
import JobTargetingForm from "@/components/resume-builder/JobTargetingForm";
import ReviewExportForm from "@/components/resume-builder/ReviewExportForm";
import ResumePreview from "@/components/resume-builder/ResumePreview";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { Link } from "wouter";
import type { Resume, PersonalInfo, WorkExperience, Education, Skill } from "@shared/schema";

const STEPS = [
  { id: 1, title: "Personal Information", description: "Contact details" },
  { id: 2, title: "Professional Summary", description: "AI-powered summary" },
  { id: 3, title: "Work Experience", description: "Employment history" },
  { id: 4, title: "Education & Skills", description: "Qualifications" },
  { id: 5, title: "Job Targeting", description: "Job description input" },
  { id: 6, title: "Review & Export", description: "Final review" },
];

export default function ResumeBuilder() {
  const { id } = useParams();
  const { toast } = useToast();
  const { isAuthenticated, isLoading } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [resumeData, setResumeData] = useState<Partial<Resume>>({});

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  // Fetch existing resume if editing
  const { data: existingResume, isLoading: resumeLoading } = useQuery<Resume>({
    queryKey: ["/api/resumes", id],
    enabled: !!id && isAuthenticated,
  });

  // Update resume mutation
  const updateResumeMutation = useMutation({
    mutationFn: async (data: Partial<Resume>) => {
      if (id) {
        return await apiRequest('PUT', `/api/resumes/${id}`, data);
      } else {
        return await apiRequest('POST', '/api/resumes', {
          title: `Resume ${new Date().toLocaleDateString()}`,
          status: 'draft',
          ...data
        });
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/resumes'] });
      if (id) {
        queryClient.invalidateQueries({ queryKey: ['/api/resumes', id] });
      }
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to save resume. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Initialize resume data from existing resume
  useEffect(() => {
    if (existingResume) {
      setResumeData(existingResume);
    }
  }, [existingResume]);

  const handleStepChange = (step: number) => {
    setCurrentStep(step);
  };

  const handleNext = async () => {
    // Auto-save current data
    if (Object.keys(resumeData).length > 0) {
      await updateResumeMutation.mutateAsync(resumeData);
    }
    
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleDataChange = (stepData: any) => {
    setResumeData(prev => ({
      ...prev,
      ...stepData
    }));
  };

  const renderCurrentStep = () => {
    const stepProps = {
      data: resumeData,
      onDataChange: handleDataChange,
      onNext: handleNext,
      onPrevious: handlePrevious
    };

    switch (currentStep) {
      case 1:
        return <PersonalInfoForm {...stepProps} />;
      case 2:
        return <ProfessionalSummaryForm {...stepProps} />;
      case 3:
        return <ExperienceForm {...stepProps} />;
      case 4:
        return <EducationSkillsForm {...stepProps} />;
      case 5:
        return <JobTargetingForm {...stepProps} />;
      case 6:
        return <ReviewExportForm {...stepProps} resumeId={id} />;
      default:
        return <PersonalInfoForm {...stepProps} />;
    }
  };

  if (isLoading || resumeLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading resume builder...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  const progress = (currentStep / STEPS.length) * 100;

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm" data-testid="button-back-home">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Home
                </Button>
              </Link>
              <div className="h-8 w-px bg-border"></div>
              <span className="text-lg font-semibold">Resume Builder</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-sm text-muted-foreground">
                Progress: {Math.round(progress)}%
              </div>
              <div className="w-32 h-2 bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex min-h-[calc(100vh-4rem)]">
        {/* Sidebar - Desktop Only */}
        <aside className="w-72 bg-card border-r border-border p-6 hidden lg:block">
          <StepNavigation
            steps={STEPS}
            currentStep={currentStep}
            onStepChange={handleStepChange}
            progress={progress}
          />
        </aside>

        {/* Main Content */}
        <div className="flex-1 lg:flex">
          {/* Form Section */}
          <div className="lg:w-1/2 p-6 lg:p-8">
            <div className="max-w-lg mx-auto">
              {/* Mobile Step Navigation */}
              <div className="lg:hidden mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h1 className="text-xl font-bold">{STEPS[currentStep - 1].title}</h1>
                  <span className="text-sm text-muted-foreground bg-secondary px-3 py-1 rounded-full">
                    Step {currentStep} of {STEPS.length}
                  </span>
                </div>
                <p className="text-muted-foreground text-sm">
                  {STEPS[currentStep - 1].description}
                </p>
              </div>

              {renderCurrentStep()}
            </div>
          </div>

          {/* Preview Section */}
          <div className="lg:w-1/2 bg-muted/30 border-l border-border">
            <div className="sticky top-16 h-[calc(100vh-4rem)] overflow-auto">
              <ResumePreview
                resumeData={resumeData}
                currentStep={currentStep}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
