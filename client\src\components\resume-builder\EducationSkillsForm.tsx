import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, ArrowLeft, Plus, Trash2, X } from "lucide-react";
import type { Education, Skill, Resume } from "@shared/schema";

interface EducationSkillsFormProps {
  data: Partial<Resume>;
  onDataChange: (data: Partial<Resume>) => void;
  onNext: () => void;
  onPrevious: () => void;
}

const defaultEducation: Education = {
  id: "",
  institution: "",
  degree: "",
  field: "",
  graduationDate: "",
  gpa: ""
};

const defaultSkill: Skill = {
  id: "",
  name: "",
  level: "intermediate",
  category: "technical"
};

const skillLevels = [
  { value: "beginner", label: "Beginner" },
  { value: "intermediate", label: "Intermediate" },
  { value: "advanced", label: "Advanced" },
  { value: "expert", label: "Expert" }
];

const skillCategories = [
  { value: "technical", label: "Technical" },
  { value: "soft", label: "Soft Skills" },
  { value: "language", label: "Languages" },
  { value: "certification", label: "Certifications" },
  { value: "other", label: "Other" }
];

export default function EducationSkillsForm({
  data,
  onDataChange,
  onNext,
  onPrevious,
}: EducationSkillsFormProps) {
  const [education, setEducation] = useState<Education[]>([]);
  const [skills, setSkills] = useState<Skill[]>([]);
  const [newSkillName, setNewSkillName] = useState("");
  const [newSkillCategory, setNewSkillCategory] = useState("technical");
  const [newSkillLevel, setNewSkillLevel] = useState("intermediate");

  // Initialize form data
  useEffect(() => {
    if (data.education && Array.isArray(data.education)) {
      setEducation(data.education as Education[]);
    } else if (education.length === 0) {
      addEducation();
    }

    if (data.skills && Array.isArray(data.skills)) {
      setSkills(data.skills as Skill[]);
    }
  }, [data]);

  const updateData = (newEducation: Education[], newSkills: Skill[]) => {
    setEducation(newEducation);
    setSkills(newSkills);
    onDataChange({
      education: newEducation,
      skills: newSkills
    });
  };

  // Education functions
  const addEducation = () => {
    const newEducation: Education = {
      ...defaultEducation,
      id: Date.now().toString()
    };
    const updatedEducation = [...education, newEducation];
    updateData(updatedEducation, skills);
  };

  const removeEducation = (id: string) => {
    const updatedEducation = education.filter(edu => edu.id !== id);
    updateData(updatedEducation, skills);
  };

  const updateEducation = (id: string, field: keyof Education, value: string) => {
    const updatedEducation = education.map(edu =>
      edu.id === id ? { ...edu, [field]: value } : edu
    );
    updateData(updatedEducation, skills);
  };

  // Skills functions
  const addSkill = () => {
    if (!newSkillName.trim()) return;

    const newSkill: Skill = {
      id: Date.now().toString(),
      name: newSkillName.trim(),
      level: newSkillLevel,
      category: newSkillCategory
    };

    const updatedSkills = [...skills, newSkill];
    updateData(education, updatedSkills);
    
    setNewSkillName("");
    setNewSkillLevel("intermediate");
    setNewSkillCategory("technical");
  };

  const removeSkill = (id: string) => {
    const updatedSkills = skills.filter(skill => skill.id !== id);
    updateData(education, updatedSkills);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addSkill();
    }
  };

  const isValidForm = () => {
    const hasValidEducation = education.length > 0 && education.every(edu =>
      edu.institution.trim() && edu.degree.trim() && edu.field.trim() && edu.graduationDate
    );
    const hasValidSkills = skills.length >= 3;
    return hasValidEducation && hasValidSkills;
  };

  const handleNext = () => {
    if (isValidForm()) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="hidden lg:block">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Education & Skills</h1>
          <span className="text-sm text-muted-foreground bg-secondary px-3 py-1 rounded-full">
            Step 4 of 6
          </span>
        </div>
        <p className="text-muted-foreground">
          Add your educational background and key skills. Include relevant certifications and technical competencies.
        </p>
      </div>

      {/* Education Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Education</h3>
        
        {education.map((edu, index) => (
          <Card key={edu.id} className="border-border">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  {index === 0 ? "Highest Education" : `Education ${index + 1}`}
                </CardTitle>
                {education.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeEducation(edu.id)}
                    data-testid={`button-remove-education-${edu.id}`}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`institution-${edu.id}`}>Institution *</Label>
                  <Input
                    id={`institution-${edu.id}`}
                    type="text"
                    placeholder="University of California, Berkeley"
                    value={edu.institution}
                    onChange={(e) => updateEducation(edu.id, 'institution', e.target.value)}
                    data-testid={`input-institution-${edu.id}`}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor={`degree-${edu.id}`}>Degree *</Label>
                  <Input
                    id={`degree-${edu.id}`}
                    type="text"
                    placeholder="Bachelor of Science"
                    value={edu.degree}
                    onChange={(e) => updateEducation(edu.id, 'degree', e.target.value)}
                    data-testid={`input-degree-${edu.id}`}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`field-${edu.id}`}>Field of Study *</Label>
                  <Input
                    id={`field-${edu.id}`}
                    type="text"
                    placeholder="Computer Science"
                    value={edu.field}
                    onChange={(e) => updateEducation(edu.id, 'field', e.target.value)}
                    data-testid={`input-field-${edu.id}`}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor={`graduationDate-${edu.id}`}>Graduation Date *</Label>
                  <Input
                    id={`graduationDate-${edu.id}`}
                    type="month"
                    value={edu.graduationDate}
                    onChange={(e) => updateEducation(edu.id, 'graduationDate', e.target.value)}
                    data-testid={`input-graduationDate-${edu.id}`}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`gpa-${edu.id}`}>GPA (Optional)</Label>
                  <Input
                    id={`gpa-${edu.id}`}
                    type="text"
                    placeholder="3.8/4.0"
                    value={edu.gpa}
                    onChange={(e) => updateEducation(edu.id, 'gpa', e.target.value)}
                    data-testid={`input-gpa-${edu.id}`}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        <Button
          type="button"
          variant="outline"
          onClick={addEducation}
          className="w-full border-dashed"
          data-testid="button-add-education"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Another Education
        </Button>
      </div>

      {/* Skills Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Skills</h3>
        
        {/* Add Skill Form */}
        <Card className="border-border">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
              <div className="space-y-2">
                <Label htmlFor="skillName">Skill Name</Label>
                <Input
                  id="skillName"
                  type="text"
                  placeholder="React, Python, Project Management..."
                  value={newSkillName}
                  onChange={(e) => setNewSkillName(e.target.value)}
                  onKeyPress={handleKeyPress}
                  data-testid="input-skill-name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="skillCategory">Category</Label>
                <Select value={newSkillCategory} onValueChange={setNewSkillCategory}>
                  <SelectTrigger data-testid="select-skill-category">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {skillCategories.map(category => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="skillLevel">Proficiency</Label>
                <Select value={newSkillLevel} onValueChange={setNewSkillLevel}>
                  <SelectTrigger data-testid="select-skill-level">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {skillLevels.map(level => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button
                type="button"
                onClick={addSkill}
                disabled={!newSkillName.trim()}
                data-testid="button-add-skill"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Skill
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Skills Display */}
        {skills.length > 0 && (
          <Card className="border-border">
            <CardHeader>
              <CardTitle className="text-base">Your Skills ({skills.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {skillCategories.map(category => {
                  const categorySkills = skills.filter(skill => skill.category === category.value);
                  if (categorySkills.length === 0) return null;

                  return (
                    <div key={category.value}>
                      <h4 className="text-sm font-medium text-muted-foreground mb-2">
                        {category.label}
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {categorySkills.map(skill => (
                          <Badge
                            key={skill.id}
                            variant="secondary"
                            className="px-3 py-1 text-sm"
                            data-testid={`skill-${skill.id}`}
                          >
                            {skill.name}
                            <span className="ml-2 text-xs opacity-70">
                              {skill.level}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="ml-2 h-auto p-0 hover:bg-transparent"
                              onClick={() => removeSkill(skill.id)}
                              data-testid={`button-remove-skill-${skill.id}`}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {skills.length < 3 && (
          <div className="text-sm text-muted-foreground bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            ⚠️ Add at least 3 skills to continue. Include both technical and soft skills relevant to your target role.
          </div>
        )}
      </div>

      {/* Tips */}
      <Card className="border-accent/20 bg-accent/5">
        <CardContent className="p-4">
          <h4 className="text-sm font-medium mb-2">Skills & Education Tips</h4>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• List your most recent or highest education first</li>
            <li>• Include relevant certifications and online courses</li>
            <li>• Add 5-15 skills that match your target job requirements</li>
            <li>• Be honest about your skill proficiency levels</li>
            <li>• Include both technical and soft skills</li>
            <li>• Use industry-standard skill names and technologies</li>
          </ul>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-border">
        <Button variant="ghost" onClick={onPrevious} data-testid="button-previous">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <Button
          onClick={handleNext}
          disabled={!isValidForm()}
          data-testid="button-next"
        >
          Continue
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}
