import { useState, useEffect } from "react";
export function useAuth() {
    const [authState, setAuthState] = useState({
        user: null,
        isLoading: true,
        isAuthenticated: false,
    });
    useEffect(() => {
        // Use Passport.js authentication
        const checkAuthStatus = async () => {
            try {
                const response = await fetch("/api/auth/user");
                if (response.ok) {
                    const user = await response.json();
                    setAuthState({
                        user,
                        isLoading: false,
                        isAuthenticated: true,
                    });
                }
                else {
                    setAuthState({
                        user: null,
                        isLoading: false,
                        isAuthenticated: false,
                    });
                }
            }
            catch (error) {
                console.error("[Auth] Error checking status:", error);
                setAuthState({
                    user: null,
                    isLoading: false,
                    isAuthenticated: false,
                });
            }
        };
        checkAuthStatus();
    }, []);
    return authState;
}
// Sign in function
export async function signInWithGoogle() {
    // Use Passport.js sign in for Google OAuth
    window.location.href = "/api/login";
}
// Explicit dev login function for testing
export async function signInWithDemo() {
    // Use explicit dev login for development
    window.location.href = "/api/dev-login";
}
// Sign out function
export async function signOut() {
    // Use Passport.js sign out
    try {
        const response = await fetch("/api/logout", {
            method: "GET",
        });
        if (response.ok) {
            window.location.href = "/";
        }
        else {
            console.error("Logout failed:", response.statusText);
        }
    }
    catch (error) {
        console.error("[Auth] Error signing out:", error);
    }
}
