# Setup Guide

## Google OAuth Configuration

This application now uses Google OAuth for authentication. Follow these steps to set it up:

### 1. Create Google OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
2. Create a new project or select an existing one
3. Enable the Google+ API
4. Go to **Credentials** → **Create Credentials** → **OAuth 2.0 Client ID**
5. Configure the OAuth consent screen if prompted
6. Select **Web application** as the application type
7. Add authorized redirect URIs:
   - For local: `http://localhost:5001/api/callback`
   - For production: `https://try2work.com/api/callback`
8. Copy the **Client ID** and **Client Secret**

### 2. Environment Variables

#### For Local Development

Create a `.env` file in the root directory:

```bash
# Copy from .env.example
cp .env.example .env
```

Update these required variables:

```env
NODE_ENV=development
APP_URL=http://localhost:5001
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here
SESSION_SECRET=your-session-secret-here

# Optional: Use local login bypass (no Google OAuth required)
ALLOW_LOCAL_LOGIN=true
```

#### For Vercel Production

Set these environment variables in your Vercel dashboard (**Settings** → **Environment Variables**):

```env
NODE_ENV=production
APP_URL=https://try2work.com
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
SESSION_SECRET=generate-a-secure-random-string
DATABASE_URL=your-postgresql-connection-string
OPENAI_API_KEY=your-openai-key
GOOGLE_AI_API_KEY=your-google-ai-key
STRIPE_SECRET_KEY=your-stripe-secret
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable
STRIPE_WEBHOOK_SECRET=your-webhook-secret
```

### 3. Generate Session Secret

```bash
# Generate a secure random string
openssl rand -hex 32
```

### 4. Database Setup

#### PostgreSQL (Production)

Use a managed PostgreSQL service like:
- [Neon](https://neon.tech/)
- [Supabase](https://supabase.com/)
- [Railway](https://railway.app/)

Set the `DATABASE_URL` environment variable to your PostgreSQL connection string.

#### SQLite (Local Development)

The app will automatically use SQLite if `DATABASE_URL` is not set or points to a file.

### 5. Run the Application

#### Local Development

```bash
npm install
npm run dev
```

Visit `http://localhost:5001`

#### Build for Production

```bash
npm run build
```

### 6. Deploy to Vercel

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel
```

Or push to GitHub and connect your repository to Vercel.

## Troubleshooting

### "Cannot GET /api/login"

- Ensure all environment variables are set correctly
- Check Vercel deployment logs for initialization errors
- Verify Google OAuth credentials are correct

### "Unauthorized" errors

- Check that `SESSION_SECRET` is set
- Verify `DATABASE_URL` is configured for session storage
- Ensure Google OAuth redirect URIs match your app URL

### Local development without Google OAuth

Set `ALLOW_LOCAL_LOGIN=true` in your `.env` file to bypass OAuth during development.
