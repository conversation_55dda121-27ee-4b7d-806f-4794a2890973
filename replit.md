# Resume Builder Application

## Overview

This is a modern, AI-powered resume builder application that helps users create professional, ATS-optimized resumes tailored to specific job descriptions. The application features a multi-step resume creation process, real-time previews, job description analysis, and AI-enhanced content generation using Google's Gemini AI.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui component library
- **Routing**: Wouter for client-side routing
- **State Management**: TanStack Query (React Query) for server state and React hooks for local state
- **Build Tool**: Vite for development and production builds

### Backend Architecture
- **Runtime**: Node.js with Express.js server
- **Language**: TypeScript with ES modules
- **Database**: PostgreSQL with Drizzle ORM for schema management and queries
- **Session Management**: Express sessions with PostgreSQL session store
- **API Structure**: RESTful endpoints organized by feature areas (auth, resumes, AI services)

### Authentication System
- **Provider**: Replit Auth with OpenID Connect
- **Flow**: OAuth-based authentication with session persistence
- **Authorization**: Route-level middleware for protected endpoints
- **User Management**: Automatic user provisioning and profile synchronization

### Database Design
- **ORM**: Drizzle with PostgreSQL dialect
- **Schema**: Strongly typed with Zod validation schemas
- **Tables**: Users, resumes, job analyses, and session storage
- **Relationships**: One-to-many between users and resumes
- **Data Types**: JSON fields for flexible resume content storage

### AI Integration
- **Provider**: Google Gemini AI (instead of OpenAI)
- **Services**: Professional summary generation, job description analysis, content enhancement
- **Features**: ATS keyword extraction, skill suggestions, experience description improvement
- **Configuration**: Structured prompts with JSON response formatting

### Multi-Step Form System
- **Pattern**: Step-based resume builder with progress tracking
- **Components**: Modular form components for each section (personal info, experience, education, etc.)
- **Validation**: Client-side validation with error handling
- **Persistence**: Auto-save functionality with draft management

### PDF Generation
- **Approach**: HTML-to-PDF conversion pipeline
- **Templates**: Multiple resume templates (modern, classic, creative)
- **Customization**: Color schemes and layout options
- **Export**: Direct download functionality

### Job Analysis Features
- **Web Scraping**: Basic job posting content extraction from URLs
- **Text Processing**: Job description parsing and keyword extraction
- **Matching**: Resume-to-job compatibility scoring
- **Suggestions**: AI-powered improvement recommendations

### Development Tools
- **Hot Reload**: Vite development server with HMR
- **Type Safety**: Full TypeScript coverage with strict configuration
- **Error Handling**: Runtime error overlays and comprehensive error boundaries
- **Code Quality**: ESLint and Prettier integration (configured via package.json)

## External Dependencies

### Database Services
- **Neon Database**: PostgreSQL hosting with serverless architecture
- **Connection**: WebSocket-based connection pooling for serverless environments

### AI Services
- **Google Gemini**: AI model for content generation and analysis
- **Models**: Gemini-2.5-pro for complex reasoning tasks

### Authentication
- **Replit Auth**: OpenID Connect provider for user authentication
- **Session Storage**: PostgreSQL-backed session management

### UI Components
- **Radix UI**: Headless UI primitives for accessibility
- **shadcn/ui**: Pre-built component library with Tailwind CSS
- **Lucide React**: Icon library for consistent iconography

### Development Services
- **Replit Platform**: Development environment with specific plugins for cartographer and dev banner

### Build and Development
- **Vite**: Frontend build tool and development server
- **ESBuild**: Backend bundling for production deployment
- **PostCSS**: CSS processing with Tailwind CSS and Autoprefixer

### Additional Libraries
- **TanStack Query**: Server state management and caching
- **React Hook Form**: Form validation and management
- **Date-fns**: Date manipulation and formatting
- **Wouter**: Lightweight client-side routing
- **Class Variance Authority**: CSS utility for component variants