import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CreditCard, Lock, CheckCircle } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
export default function PaymentModal({ isOpen, onClose, resumeId, resumeTitle, amount, currency, onSuccess }) {
    const { toast } = useToast();
    const [isProcessing, setIsProcessing] = useState(false);
    const [paymentMethod, setPaymentMethod] = useState("card");
    const [cardNumber, setCardNumber] = useState('');
    const [expiration, setExpiration] = useState('');
    const [cvv, setCvv] = useState('');
    const [nameOnCard, setNameOnCard] = useState('');
    // Create payment intent mutation
    const createPaymentIntentMutation = useMutation({
        mutationFn: async (data) => {
            return await apiRequest('POST', '/api/payments/create-payment-intent', data);
        },
        onSuccess: (data) => {
            // Handle payment intent success
            console.log("Payment intent created:", data);
        },
        onError: (error) => {
            toast({
                title: "Error",
                description: "Failed to create payment. Please try again.",
                variant: "destructive",
            });
            setIsProcessing(false);
        },
    });
    // Create checkout session mutation
    const createCheckoutSessionMutation = useMutation({
        mutationFn: async () => {
            const baseUrl = window.location.origin;
            return await apiRequest('POST', '/api/payments/create-checkout-session', {
                resumeId,
                successUrl: `${baseUrl}/resume-builder/${resumeId}?payment=success`,
                cancelUrl: `${baseUrl}/resume-builder/${resumeId}?payment=cancelled`
            });
        },
        onSuccess: (data) => {
            // Redirect to Stripe Checkout
            if (data.url) {
                window.location.href = data.url;
            }
        },
        onError: (error) => {
            toast({
                title: "Error",
                description: "Failed to create payment session. Please try again.",
                variant: "destructive",
            });
            setIsProcessing(false);
        },
    });
    const handlePayment = async () => {
        setIsProcessing(true);
        if (paymentMethod === "card") {
            // Collect payment details
            const paymentData = {
                resumeId,
                amount,
                currency
            };
            createPaymentIntentMutation.mutate(paymentData);
            // Create checkout session for Stripe Checkout
            createCheckoutSessionMutation.mutate();
        }
    };
    const formatAmount = (amount, currency) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency.toUpperCase(),
        }).format(amount / 100);
    };
    return (<Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5"/>
            Complete Your Purchase
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Order Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>Resume PDF Download</span>
                <span>{formatAmount(amount, currency)}</span>
              </div>
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Resume Title</span>
                <span className="truncate max-w-[200px]" title={resumeTitle}>
                  {resumeTitle}
                </span>
              </div>
              <div className="border-t pt-3 flex justify-between font-semibold">
                <span>Total</span>
                <span>{formatAmount(amount, currency)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Payment Method Selection */}
          <Tabs value={paymentMethod} onValueChange={(value) => setPaymentMethod(value)}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="card">Card Payment</TabsTrigger>
              <TabsTrigger value="checkout">Secure Checkout</TabsTrigger>
            </TabsList>
            
            <TabsContent value="card" className="space-y-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Lock className="h-4 w-4"/>
                      <span>Your payment information is encrypted and secure</span>
                    </div>
                    
                    <div className="bg-muted/50 p-4 rounded-lg">
                      <p className="text-sm font-medium mb-2">Direct Card Payment</p>
                      <p className="text-xs text-muted-foreground">
                        Enter your card details directly on this page for a quick checkout experience.
                      </p>
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="cardNumber" className="block text-sm font-medium mb-2">Card Number</label>
                        <input id="cardNumber" type="text" value={cardNumber} onChange={(e) => setCardNumber(e.target.value)} placeholder="1234 5678 9012 3456" className="w-full p-2 border rounded"/>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="expiration" className="block text-sm font-medium mb-2">Expiration</label>
                          <input id="expiration" type="text" value={expiration} onChange={(e) => setExpiration(e.target.value)} placeholder="MM/YY" className="w-full p-2 border rounded"/>
                        </div>
                        <div>
                          <label htmlFor="cvv" className="block text-sm font-medium mb-2">CVV</label>
                          <input id="cvv" type="text" value={cvv} onChange={(e) => setCvv(e.target.value)} placeholder="123" className="w-full p-2 border rounded"/>
                        </div>
                      </div>
                      <div>
                        <label htmlFor="nameOnCard" className="block text-sm font-medium mb-2">Name on Card</label>
                        <input id="nameOnCard" type="text" value={nameOnCard} onChange={(e) => setNameOnCard(e.target.value)} placeholder="John Doe" className="w-full p-2 border rounded"/>
                      </div>
                      <Button type="submit" disabled={isProcessing}>
                        {isProcessing ? "Processing..." : `Pay ${formatAmount(amount, currency)}`}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="checkout" className="space-y-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Lock className="h-4 w-4"/>
                      <span>Secured by Stripe</span>
                    </div>
                    
                    <div className="bg-muted/50 p-4 rounded-lg">
                      <p className="text-sm font-medium mb-2">Stripe Checkout</p>
                      <p className="text-xs text-muted-foreground">
                        You'll be redirected to Stripe's secure payment page to complete your purchase.
                      </p>
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <CheckCircle className="h-3 w-3 text-green-500"/>
                      <span>Accepts all major credit cards</span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <CheckCircle className="h-3 w-3 text-green-500"/>
                      <span>Bank-level security</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button variant="outline" onClick={onClose} className="flex-1" disabled={isProcessing}>
              Cancel
            </Button>
            <Button onClick={handlePayment} className="flex-1" disabled={isProcessing}>
              {isProcessing ? "Processing..." : `Pay ${formatAmount(amount, currency)}`}
            </Button>
          </div>
          
          {/* Security Badge */}
          <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
            <Lock className="h-3 w-3"/>
            <span>SSL Secured Transaction</span>
          </div>
        </div>
      </DialogContent>
    </Dialog>);
}
