import PDFParser from "pdf2json";

// Wrapper for pdf2json with improved error handling and async support.
// Returns both a concatenated text string and the raw pdf2json output so callers
// can use structured page/text information when available.
export default async function parsePDF(buffer: Buffer) {
  return new Promise<{ text: string; raw: any }>((resolve, reject) => {
    try {
      const pdfParser = new PDFParser();

      let textContent = "";

      pdfParser.on("pdfParser_dataError", (errData: any) => {
        reject(new Error(`PDF parsing failed: ${errData?.parserError?.message || "Unknown error"}`));
      });

      pdfParser.on("pdfParser_dataReady", (pdfData: any) => {
        try {
          // Extract text content from all pages preserving run order
          if (pdfData && Array.isArray(pdfData.Pages)) {
            const allText: string[] = [];

            pdfData.Pages.forEach((page: any) => {
              if (Array.isArray(page.Texts)) {
                page.Texts.forEach((textBlock: any) => {
                  if (Array.isArray(textBlock.R)) {
                    textBlock.R.forEach((textRun: any) => {
                      if (textRun && textRun.T) {
                        allText.push(textRun.T);
                      }
                    });
                  }
                });
              }
            });

            textContent = allText.join(" ");
          }

          // Return both the flattened text and the raw pdf2json object
          resolve({ text: textContent, raw: pdfData });
        } catch (error: any) {
          reject(new Error(`Error processing PDF data: ${error?.message || String(error)}`));
        }
      });

      // Parse the buffer
      pdfParser.parseBuffer(buffer);
    } catch (error: any) {
      reject(new Error(`PDF parsing failed: ${error?.message || String(error)}`));
    }
  });
}
