import Stripe from 'stripe';
declare let stripe: Stripe;
export { stripe };
export interface CreatePaymentIntentParams {
    resumeId: string;
    userId: string;
    amount?: number;
    currency?: string;
}
export interface CreateCheckoutSessionParams {
    resumeId: string;
    userId: string;
    successUrl: string;
    cancelUrl: string;
    amount?: number;
    currency?: string;
}
/**
 * Create a payment intent for a resume download
 */
export declare function createPaymentIntent({ resumeId, userId, amount, // $1.99 in cents
currency }: CreatePaymentIntentParams): Promise<{
    paymentIntentId: string | null;
    status: string;
    amount: number | null;
    currency: string | null;
    clientSecret?: undefined;
} | {
    paymentIntentId: string;
    clientSecret: string | null;
    amount: number;
    currency: string;
    status: Stripe.PaymentIntent.Status;
}>;
/**
 * Create a checkout session for a resume download
 */
export declare function createCheckoutSession({ resumeId, userId, successUrl, cancelUrl, amount, // $1.99 in cents
currency }: CreateCheckoutSessionParams): Promise<{
    sessionId: string | null;
    status: string;
    url: string;
} | {
    sessionId: string;
    url: string | null;
    status: Stripe.Checkout.Session.Status | null;
}>;
/**
 * Handle Stripe webhook events
 */
export declare function handleWebhookEvent(event: Stripe.Event): Promise<void>;
/**
 * Check if a resume has been paid for
 */
export declare function checkPaymentStatus(resumeId: string, userId: string): Promise<boolean>;
/**
 * Refund a payment (admin function)
 */
export declare function refundPayment(resumeId: string, userId: string): Promise<Stripe.Response<Stripe.Refund>>;
//# sourceMappingURL=stripe.d.ts.map