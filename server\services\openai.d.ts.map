{"version": 3, "file": "openai.d.ts", "sourceRoot": "", "sources": ["openai.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAmH7F,MAAM,WAAW,iBAAiB;IAChC,QAAQ,EAAE,MAAM,EAAE,CAAC;IACnB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,qBAAqB,EAAE,MAAM,EAAE,CAAC;IAChC,UAAU,EAAE,MAAM,CAAC;CACpB;AAYD,wBAAsB,qBAAqB,CAAC,cAAc,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAoB9F;AAED,wBAAsB,2BAA2B,CAC/C,YAAY,EAAE,YAAY,EAC1B,UAAU,EAAE,cAAc,EAAE,EAC5B,MAAM,EAAE,KAAK,EAAE,EACf,SAAS,CAAC,EAAE,MAAM,EAClB,WAAW,CAAC,EAAE,MAAM,EAAE,GACrB,OAAO,CAAC,MAAM,CAAC,CAwDjB;AAED,wBAAsB,4BAA4B,CAChD,UAAU,EAAE,cAAc,EAC1B,WAAW,CAAC,EAAE,MAAM,EAAE,GACrB,OAAO,CAAC,MAAM,CAAC,CAqDjB;AAED,wBAAsB,mBAAmB,CACvC,cAAc,EAAE,MAAM,EACtB,aAAa,EAAE,MAAM,EAAE,GACtB,OAAO,CAAC,MAAM,EAAE,CAAC,CAqDnB;AAED,MAAM,WAAW,WAAW;IAC1B,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,cAAc,CAAC,EAAE,cAAc,EAAE,CAAC;IAClC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC;IACxB,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC;IACjB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,wBAAsB,mBAAmB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,CAkB/E;AAED,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,MAAM,CAAC;IACb,eAAe,EAAE,MAAM,CAAC;IACxB,SAAS,EAAE,MAAM,EAAE,CAAC;IACpB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,wBAAsB,uBAAuB,CAAC,KAAK,EAAE,gBAAgB,GAAG,OAAO,CAAC,WAAW,CAAC,CAsB3F;AAED,wBAAgB,4BAA4B,CAC1C,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,EACxB,KAAK,EAAE,WAAW,GACjB,OAAO,CAAC,MAAM,CAAC,CAWjB"}