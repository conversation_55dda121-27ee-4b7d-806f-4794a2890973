{"version": "6", "dialect": "sqlite", "id": "be9c6227-9bdc-4a19-b8ce-dec01fd13a2c", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"job_analyses": {"name": "job_analyses", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "resume_id": {"name": "resume_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "job_description": {"name": "job_description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "extracted_keywords": {"name": "extracted_keywords", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "requirements": {"name": "requirements", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "suggested_improvements": {"name": "suggested_improvements", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "match_score": {"name": "match_score", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"job_analyses_resume_id_resumes_id_fk": {"name": "job_analyses_resume_id_resumes_id_fk", "tableFrom": "job_analyses", "tableTo": "resumes", "columnsFrom": ["resume_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "resumes": {"name": "resumes", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "template_id": {"name": "template_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'modern'"}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'draft'"}, "payment_status": {"name": "payment_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'unpaid'"}, "stripe_session_id": {"name": "stripe_session_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "personal_info": {"name": "personal_info", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "professional_summary": {"name": "professional_summary", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "work_experience": {"name": "work_experience", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "education": {"name": "education", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "skills": {"name": "skills", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "target_job_title": {"name": "target_job_title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "target_company": {"name": "target_company", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "job_description": {"name": "job_description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "job_url": {"name": "job_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "extracted_keywords": {"name": "extracted_keywords", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ai_enhanced": {"name": "ai_enhanced", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"resumes_user_id_users_id_fk": {"name": "resumes_user_id_users_id_fk", "tableFrom": "resumes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sessions": {"name": "sessions", "columns": {"sid": {"name": "sid", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "sess": {"name": "sess", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expire": {"name": "expire", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "profile_image_url": {"name": "profile_image_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}