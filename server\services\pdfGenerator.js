import puppeteer from "puppeteer";
export async function generateResumePDF(resume, options = {
    template: 'modern',
    includePhoto: false,
    colorScheme: '#3B82F6'
}) {
    const html = generateResumeHTML(resume, options);
    try {
        // Launch Puppeteer browser
        const browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        const page = await browser.newPage();
        // Set content and wait for it to load
        await page.setContent(html, { waitUntil: 'networkidle0' });
        // Generate PDF with proper formatting
        const pdfBuffer = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
                top: '0.5in',
                right: '0.5in',
                bottom: '0.5in',
                left: '0.5in'
            }
        });
        await browser.close();
        return Buffer.from(pdfBuffer);
    }
    catch (error) {
        console.error('Error generating PDF:', error);
        // Fallback: return HTML as buffer (better than crashing)
        return Buffer.from(html);
    }
}
function generateResumeHTML(resume, options) {
    const personalInfo = resume.personalInfo;
    const workExperience = resume.workExperience || [];
    const education = resume.education || [];
    const skills = resume.skills || [];
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${personalInfo?.firstName} ${personalInfo?.lastName} - Resume</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background: white;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid ${options.colorScheme};
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .name {
            font-size: 32px;
            font-weight: bold;
            color: ${options.colorScheme};
            margin-bottom: 10px;
        }
        
        .contact-info {
            font-size: 14px;
            color: #666;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: ${options.colorScheme};
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .summary {
            font-size: 16px;
            line-height: 1.7;
            margin-bottom: 20px;
        }
        
        .experience-item, .education-item {
            margin-bottom: 20px;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .job-title, .degree {
            font-size: 18px;
            font-weight: bold;
        }
        
        .company, .school {
            font-size: 16px;
            color: #666;
        }
        
        .date-range {
            font-size: 14px;
            color: #888;
        }
        
        .description {
            margin-top: 10px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .skill-category {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .skill-category-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: ${options.colorScheme};
        }
        
        .skill-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .skill-tag {
            background: ${options.colorScheme}20;
            color: ${options.colorScheme};
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="name">${personalInfo?.firstName || ''} ${personalInfo?.lastName || ''}</div>
        <div class="contact-info">
            ${personalInfo?.email ? `<span>${personalInfo.email}</span>` : ''}
            ${personalInfo?.phone ? `<span>${personalInfo.phone}</span>` : ''}
            ${personalInfo?.city && personalInfo?.state ? `<span>${personalInfo.city}, ${personalInfo.state}</span>` : ''}
            ${personalInfo?.linkedinUrl ? `<span>${personalInfo.linkedinUrl}</span>` : ''}
        </div>
    </div>
    
    ${resume.professionalSummary ? `
    <div class="section">
        <div class="section-title">Professional Summary</div>
        <div class="summary">${resume.professionalSummary}</div>
    </div>
    ` : ''}
    
    ${workExperience.length > 0 ? `
    <div class="section">
        <div class="section-title">Professional Experience</div>
        ${workExperience.map(exp => `
        <div class="experience-item">
            <div class="item-header">
                <div>
                    <div class="job-title">${exp.position}</div>
                    <div class="company">${exp.company}</div>
                </div>
                <div class="date-range">${exp.startDate}${exp.endDate ? ` - ${exp.endDate}` : ' - Present'}</div>
            </div>
            <div class="description">${exp.description}</div>
        </div>
        `).join('')}
    </div>
    ` : ''}
    
    ${education.length > 0 ? `
    <div class="section">
        <div class="section-title">Education</div>
        ${education.map(edu => `
        <div class="education-item">
            <div class="item-header">
                <div>
                    <div class="degree">${edu.degree} in ${edu.field}</div>
                    <div class="school">${edu.institution}</div>
                </div>
                <div class="date-range">${edu.graduationDate}</div>
            </div>
        </div>
        `).join('')}
    </div>
    ` : ''}
    
    ${skills.length > 0 ? `
    <div class="section">
        <div class="section-title">Skills</div>
        <div class="skill-list">
            ${skills.map(skill => `<span class="skill-tag">${skill.name}</span>`).join('')}
        </div>
    </div>
    ` : ''}
</body>
</html>`;
}
export function generateResumePreviewHTML(resume) {
    return generateResumeHTML(resume, { template: 'modern', includePhoto: false, colorScheme: '#3B82F6' });
}
