import express, { type Request, Response } from 'express';
import multer from 'multer';
import fs from 'fs';
import path from 'path';

// Initialize Express app
const app = express();
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
});

// Health check endpoint
app.get("/api/health", (req: Request, res: Response) => {
  res.json({ 
    status: "ok", 
    timestamp: new Date().toISOString(),
    service: "Resume Builder API"
  });
});

// Auth endpoints (mock for now)
app.get("/api/login", (req: Request, res: Response) => {
  res.json({ 
    message: "Login endpoint working",
    timestamp: new Date().toISOString()
  });
});

app.get("/api/auth/user", (req: Request, res: Response) => {
  // Mock user for now
  res.json({
    id: "demo-user",
    email: "<EMAIL>",
    firstName: "Demo",
    lastName: "User"
  });
});

// Resume endpoints
app.get("/api/resumes", (req: Request, res: Response) => {
  // Mock resumes for now
  res.json([]);
});

app.post("/api/resumes", (req: Request, res: Response) => {
  try {
    const resumeData = {
      id: crypto.randomUUID(),
      title: req.body.title || "Untitled Resume",
      status: "draft",
      templateId: "modern",
      paymentStatus: "unpaid",
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: "demo-user"
    };
    res.json(resumeData);
  } catch (error) {
    console.error('Error creating resume:', error);
    res.status(400).json({ message: "Failed to create resume" });
  }
});

// Resume upload endpoint - actually parse the uploaded file
app.post("/api/resumes/upload", upload.single("resume"), (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: "Resume file is required" });
    }

    console.log(`[API] Processing resume upload: ${req.file.originalname}, size: ${req.file.size} bytes`);

    // Parse the uploaded file content
    const fileBuffer = req.file.buffer;
    let fileContent = '';
    
    // Simple text extraction from common resume formats
    if (req.file.mimetype === 'text/plain') {
      fileContent = fileBuffer.toString('utf-8');
    } else if (req.file.mimetype === 'application/pdf' || req.file.originalname.toLowerCase().endsWith('.pdf')) {
      // For PDF files, we'll extract text content
      fileContent = extractTextFromPdf(fileBuffer);
    } else if (req.file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || req.file.originalname.toLowerCase().endsWith('.docx')) {
      // For DOCX files, we'll extract text content
      fileContent = extractTextFromDocx(fileBuffer);
    } else {
      // Try to read as text for other formats
      try {
        fileContent = fileBuffer.toString('utf-8');
      } catch {
        fileContent = 'Could not extract text from file';
      }
    }

    console.log(`[API] Extracted text content length: ${fileContent.length} characters`);

    // Parse the extracted text to create resume data
    const parsedResume = parseResumeContent(fileContent, req.file.originalname);
    
    console.log('[API] Resume parsing completed successfully');
    res.json({ draft: parsedResume });
  } catch (error: any) {
    console.error('[API] Error processing resume upload:', error);
    res.status(500).json({
      message: "Failed to process resume file",
      error: error?.message || "Unknown error"
    });
  }
});

// Helper function to extract text from PDF (simplified)
function extractTextFromPdf(buffer: Buffer): string {
  // For production, use libraries like pdf-parse
  // For now, return placeholder text
  return "PDF content extraction would be implemented here with proper PDF parsing library";
}

// Helper function to extract text from DOCX (simplified)
function extractTextFromDocx(buffer: Buffer): string {
  // For production, use libraries like mammoth or docx
  // For now, return placeholder text
  return "DOCX content extraction would be implemented here with proper DOCX parsing library";
}

// Helper function to parse resume content
function parseResumeContent(content: string, filename: string) {
  const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  
  // Extract basic information using simple pattern matching
  const personalInfo = extractPersonalInfo(lines);
  const workExperience = extractWorkExperience(lines);
  const education = extractEducation(lines);
  const skills = extractSkills(lines);
  const professionalSummary = extractSummary(lines);

  return {
    personalInfo,
    professionalSummary,
    workExperience,
    education,
    skills
  };
}

// Helper functions for parsing different sections
function extractPersonalInfo(lines: string[]) {
  let email = '';
  let phone = '';
  let name = '';

  // Simple pattern matching for common info
  const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
  const phoneRegex = /(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})/;
  
  // Look for name (usually first few lines)
  for (let i = 0; i < Math.min(5, lines.length); i++) {
    if (lines[i].length > 5 && lines[i].length < 50 && !emailRegex.test(lines[i]) && !phoneRegex.test(lines[i])) {
      if (!name) {
        name = lines[i];
      }
    }
    if (emailRegex.test(lines[i])) {
      email = emailRegex.exec(lines[i])![1];
    }
    if (phoneRegex.test(lines[i])) {
      phone = phoneRegex.exec(lines[i])![1];
    }
  }

  const nameParts = name.split(' ');
  return {
    firstName: nameParts[0] || '',
    lastName: nameParts.slice(1).join(' ') || '',
    email: email || '',
    phone: phone || '',
    city: '',
    state: ''
  };
}

function extractWorkExperience(lines: string[]) {
  const experiences = [];
  let currentExp = null;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Look for company/position patterns
    if (line.length > 5 && (line.includes(',') || line.includes('-'))) {
      if (currentExp && currentExp.company) {
        experiences.push(currentExp);
      }
      currentExp = {
        id: crypto.randomUUID(),
        company: line,
        position: '',
        startDate: '',
        endDate: '',
        current: false,
        description: '',
        achievements: []
      };
    }
    
    // Look for position indicators
    if (line.toLowerCase().includes('developer') ||
        line.toLowerCase().includes('engineer') ||
        line.toLowerCase().includes('manager') ||
        line.toLowerCase().includes('analyst') ||
        line.toLowerCase().includes('specialist')) {
      if (currentExp) {
        currentExp.position = line;
      }
    }
    
    // Look for dates
    const dateRegex = /\b(19|20)\d{2}\b/;
    if (dateRegex.test(line)) {
      if (currentExp && !currentExp.startDate) {
        const year = line.match(dateRegex);
        if (year) {
          currentExp.startDate = year[0] + '-01';
          currentExp.endDate = new Date().getFullYear().toString() + '-01';
        }
      }
    }
    
    // Collect bullet points as achievements
    if (line.startsWith('•') || line.startsWith('-') || line.match(/^\d+\./)) {
      if (currentExp) {
        currentExp.description += line + '\n';
        (currentExp.achievements as string[]).push(line.replace(/^[•\-\d\.]\s*/, ''));
      }
    }
  }
  
  if (currentExp && currentExp.company) {
    experiences.push(currentExp);
  }
  
  return experiences;
}

function extractEducation(lines: string[]) {
  const education = [];
  
  for (const line of lines) {
    const degreeKeywords = ['bachelor', 'master', 'phd', 'doctorate', 'associate', 'diploma', 'degree'];
    const schoolKeywords = ['university', 'college', 'institute', 'school'];
    
    if (degreeKeywords.some(keyword => line.toLowerCase().includes(keyword)) ||
        schoolKeywords.some(keyword => line.toLowerCase().includes(keyword))) {
      education.push({
        id: crypto.randomUUID(),
        institution: line,
        degree: 'Bachelor\'s Degree',
        field: '',
        graduationDate: '',
        gpa: ''
      });
    }
  }
  
  return education;
}

function extractSkills(lines: string[]) {
  const skills = [];
  let skillSection = false;
  
  for (const line of lines) {
    if (line.toLowerCase().includes('skills') || line.toLowerCase().includes('technologies')) {
      skillSection = true;
      continue;
    }
    
    if (skillSection) {
      if (line.length > 0 && !line.toLowerCase().includes('experience') && !line.toLowerCase().includes('education')) {
        // Split skills by common delimiters
        const skillList = line.split(/[,|;]/).map(s => s.trim()).filter(s => s.length > 0);
        for (const skill of skillList) {
          skills.push({
            id: crypto.randomUUID(),
            name: skill,
            level: 'Intermediate',
            category: 'Technical'
          });
        }
      }
    }
  }
  
  return skills;
}

function extractSummary(lines: string[]) {
  // Look for summary sections
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.toLowerCase().includes('summary') || line.toLowerCase().includes('objective')) {
      // Take next few lines as summary
      const summaryLines = lines.slice(i + 1, i + 4);
      return summaryLines.join(' ');
    }
  }
  
  return "Professional with experience in relevant field.";
}

// AI endpoints (mock)
app.post("/api/ai/resume-brief", (req: Request, res: Response) => {
  try {
    const { role, yearsExperience, topSkills, accomplishments } = req.body;
    
    // Mock AI-generated resume draft
    const mockDraft = {
      personalInfo: {
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        phone: "(*************",
        city: "New York",
        state: "NY"
      },
      professionalSummary: `Experienced ${role} with ${yearsExperience} years of expertise in ${(topSkills as string[])?.slice(0, 3).join(', ')}. Proven track record in ${(accomplishments as string[])?.slice(0, 2).join(', ')}.`,
      workExperience: [
        {
          id: crypto.randomUUID(),
          company: "Previous Company",
          position: role,
          startDate: `${new Date().getFullYear() - yearsExperience}-01`,
          endDate: `${new Date().getFullYear()}-01`,
          current: true,
          description: `• Led development initiatives in ${(topSkills as string[])?.slice(0, 2).join(' and ')}\n• Delivered successful projects\n• Collaborated with cross-functional teams`,
          achievements: (accomplishments as string[])?.map((acc: string) => `• ${acc}`) || []
        }
      ],
      education: [
        {
          id: crypto.randomUUID(),
          institution: "University",
          degree: "Bachelor's Degree",
          field: "Related Field",
          graduationDate: `${new Date().getFullYear() - yearsExperience - 4}-05`
        }
      ],
      skills: (topSkills as string[])?.map((skill: string) => ({
        id: crypto.randomUUID(),
        name: skill,
        level: "Intermediate",
        category: "Technical"
      })) || []
    };

    res.json({ draft: mockDraft });
  } catch (error) {
    console.error('[API] Error generating resume from brief:', error);
    res.status(500).json({ message: "Failed to generate resume draft" });
  }
});

// Job analysis endpoint (mock)
app.post("/api/jobs/analyze", (req: Request, res: Response) => {
  try {
    const { jobDescription } = req.body;
    
    // Mock job analysis
    const mockAnalysis = {
      keywords: [
        "JavaScript", "React", "Node.js", "TypeScript", "Git",
        "Agile", "Team Leadership", "Problem Solving", "Communication"
      ],
      requirements: [
        "Experience with JavaScript frameworks",
        "Strong problem-solving skills",
        "Team collaboration experience"
      ],
      suggestedImprovements: [
        "Highlight leadership experience",
        "Quantify achievements with metrics",
        "Include relevant technical skills"
      ],
      matchScore: "mid"
    };

    res.json(mockAnalysis);
  } catch (error) {
    console.error('[API] Error analyzing job:', error);
    res.status(500).json({ message: "Failed to analyze job description" });
  }
});

// Payment endpoints (mock)
app.post("/api/payments/create-checkout-session", (req: Request, res: Response) => {
  try {
    const { resumeId } = req.body;
    res.json({
      sessionId: `cs_${crypto.randomUUID()}`,
      url: "https://checkout.stripe.com/mock-session",
      status: "complete"
    });
  } catch (error) {
    console.error('[API] Error creating payment session:', error);
    res.status(500).json({ message: "Failed to create payment session" });
  }
});

// PDF generation endpoint
app.get("/api/resumes/:id/pdf", (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Mock PDF response
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader("Content-Disposition", `attachment; filename="resume-${id}.pdf"`);
    res.send("Mock PDF content");
  } catch (error) {
    console.error('[API] Error generating PDF:', error);
    res.status(500).json({ message: "Failed to generate PDF" });
  }
});

// Error handling middleware
app.use((err: any, _req: Request, res: Response, _next: any) => {
  console.error('[API] Unhandled error:', err);
  res.status(500).json({ 
    message: 'Internal server error',
    error: err?.message || 'Unknown error'
  });
});

// Export handler for Vercel
export default async function handler(req: Request, res: Response) {
  try {
    app(req, res);
  } catch (error: any) {
    console.error('[API] Handler error:', error);
    if (!res.headersSent) {
      res.status(503).json({
        message: 'Service temporarily unavailable',
        error: error?.message || 'Unknown error'
      });
    }
  }
}
