/**
 * Test script for the complete payment flow
 * This script tests:
 * 1. Creating a payment intent
 * 2. Creating a checkout session
 * 3. Simulating a webhook event
 * 4. Verifying payment status
 */

import fetch from 'node-fetch';
import crypto from 'crypto';

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:5000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword'
};

// Test data
const TEST_RESUME = {
  title: 'Test Resume for Payment',
  personalInfo: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    city: 'New York',
    state: 'NY'
  },
  professionalSummary: 'Experienced software engineer with a passion for creating innovative solutions.',
  targetJobTitle: 'Software Engineer',
  industry: 'Technology'
};

// Helper function to make authenticated requests
async function authenticatedFetch(url, options = {}) {
  // First, login to get a session
  const loginResponse = await fetch(`${API_URL}/api/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(TEST_USER)
  });

  if (!loginResponse.ok) {
    throw new Error(`Login failed: ${loginResponse.statusText}`);
  }

  const cookies = loginResponse.headers.get('set-cookie');
  
  // Make the authenticated request
  return await fetch(`${API_URL}${url}`, {
    ...options,
    headers: {
      ...options.headers,
      'Cookie': cookies
    }
  });
}

// Test 1: Create a resume
async function testCreateResume() {
  console.log('Test 1: Creating a resume...');
  
  const response = await authenticatedFetch('/api/resumes', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(TEST_RESUME)
  });

  if (!response.ok) {
    throw new Error(`Failed to create resume: ${response.statusText}`);
  }

  const resume = await response.json();
  console.log(`✅ Resume created with ID: ${resume.id}`);
  return resume;
}

// Test 2: Create a payment intent
async function testCreatePaymentIntent(resumeId) {
  console.log('Test 2: Creating a payment intent...');
  
  const response = await authenticatedFetch('/api/payments/create-payment-intent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ resumeId })
  });

  if (!response.ok) {
    throw new Error(`Failed to create payment intent: ${response.statusText}`);
  }

  const paymentIntent = await response.json();
  console.log(`✅ Payment intent created with ID: ${paymentIntent.paymentIntentId}`);
  return paymentIntent;
}

// Test 3: Create a checkout session
async function testCreateCheckoutSession(resumeId) {
  console.log('Test 3: Creating a checkout session...');
  
  const response = await authenticatedFetch('/api/payments/create-checkout-session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      resumeId,
      successUrl: `${API_URL}/payment/success`,
      cancelUrl: `${API_URL}/payment/cancel`
    })
  });

  if (!response.ok) {
    throw new Error(`Failed to create checkout session: ${response.statusText}`);
  }

  const checkoutSession = await response.json();
  console.log(`✅ Checkout session created with ID: ${checkoutSession.sessionId}`);
  console.log(`   Checkout URL: ${checkoutSession.url}`);
  return checkoutSession;
}

// Test 4: Simulate a webhook event
async function testWebhookEvent(paymentIntentId, resumeId) {
  console.log('Test 4: Simulating a webhook event...');
  
  // Create a mock payment intent succeeded event
  const event = {
    id: `evt_test_${Date.now()}`,
    object: 'event',
    api_version: '2024-06-20',
    created: Math.floor(Date.now() / 1000),
    type: 'payment_intent.succeeded',
    data: {
      object: {
        id: paymentIntentId,
        object: 'payment_intent',
        amount: 199,
        currency: 'usd',
        status: 'succeeded',
        metadata: {
          resumeId,
          userId: 'test-user-id'
        },
        customer: 'cus_test'
      }
    }
  };

  // Sign the event with the webhook secret
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || 'whsec_test';
  const payload = JSON.stringify(event);
  const signature = crypto
    .createHmac('sha256', webhookSecret)
    .update(payload, 'utf8')
    .digest('hex');
  
  const stripeSignature = `t=${Date.now()},v1=${signature}`;

  const response = await fetch(`${API_URL}/api/webhooks/stripe`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Stripe-Signature': stripeSignature
    },
    body: payload
  });

  if (!response.ok) {
    throw new Error(`Failed to process webhook event: ${response.statusText}`);
  }

  console.log(`✅ Webhook event processed successfully`);
  return event;
}

// Test 5: Check payment status
async function testCheckPaymentStatus(resumeId) {
  console.log('Test 5: Checking payment status...');
  
  const response = await authenticatedFetch(`/api/payments/status/${resumeId}`);
  
  if (!response.ok) {
    throw new Error(`Failed to check payment status: ${response.statusText}`);
  }

  const status = await response.json();
  console.log(`✅ Payment status: ${status.paid ? 'Paid' : 'Unpaid'}`);
  return status;
}

// Test 6: Try to download PDF
async function testDownloadPDF(resumeId) {
  console.log('Test 6: Testing PDF download...');
  
  const response = await authenticatedFetch(`/api/resumes/${resumeId}/pdf`);
  
  if (!response.ok) {
    if (response.status === 402) {
      const error = await response.json();
      console.log(`✅ Payment required as expected: ${error.message}`);
      return { paymentRequired: true, ...error };
    }
    throw new Error(`Failed to download PDF: ${response.statusText}`);
  }

  const pdfBuffer = await response.buffer();
  console.log(`✅ PDF downloaded successfully (${pdfBuffer.length} bytes)`);
  return { success: true, size: pdfBuffer.length };
}

// Run all tests
async function runTests() {
  console.log('🧪 Starting payment flow tests...\n');
  
  try {
    // Test 1: Create a resume
    const resume = await testCreateResume();
    const resumeId = resume.id;
    
    // Test 2: Create a payment intent
    const paymentIntent = await testCreatePaymentIntent(resumeId);
    
    // Test 3: Create a checkout session
    const checkoutSession = await testCreateCheckoutSession(resumeId);
    
    // Test 4: Check payment status before payment
    const statusBefore = await testCheckPaymentStatus(resumeId);
    console.log(`   Payment status before webhook: ${statusBefore.paid ? 'Paid' : 'Unpaid'}`);
    
    // Test 5: Try to download PDF before payment
    const downloadBefore = await testDownloadPDF(resumeId);
    console.log(`   PDF download before payment: ${downloadBefore.paymentRequired ? 'Payment required' : 'Success'}`);
    
    // Test 6: Simulate a webhook event
    await testWebhookEvent(paymentIntent.paymentIntentId, resumeId);
    
    // Test 7: Check payment status after payment
    const statusAfter = await testCheckPaymentStatus(resumeId);
    console.log(`   Payment status after webhook: ${statusAfter.paid ? 'Paid' : 'Unpaid'}`);
    
    // Test 8: Try to download PDF after payment
    const downloadAfter = await testDownloadPDF(resumeId);
    console.log(`   PDF download after payment: ${downloadAfter.success ? 'Success' : 'Failed'}`);
    
    console.log('\n✅ All tests passed successfully!');
    
  } catch (error) {
    console.error(`\n❌ Test failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the tests
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('Starting payment flow tests...');
  runTests().catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

export {
  testCreateResume,
  testCreatePaymentIntent,
  testCreateCheckoutSession,
  testWebhookEvent,
  testCheckPaymentStatus,
  testDownloadPDF,
  runTests
};