/**
 * Basic Playwright test for ResumeAI local deployment
 * This script tests:
 * 1. Landing page loads correctly
 * 2. Sign in button works
 * 3. User can navigate to the home page
 */

const { test, expect } = require('@playwright/test');
const { loginAsDevUser } = require('./auth-helper.cjs');

test.describe('ResumeAI Basic Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the local deployment
    await page.goto('/');
  });

  test('Landing page loads correctly', async ({ page }) => {
    // Check if the page title is correct
    await expect(page).toHaveTitle(/ResumeAI/);

    // Check if the main heading is present
    await expect(page.locator('h1')).toContainText(/Build AI-Powered Resumes|Create Professional Resumes/i);

    // Check if the sign in button is present using test ID
    await expect(page.getByTestId('button-sign-in')).toBeVisible();

    // Check if the get started button is present
    await expect(page.getByTestId('button-get-started')).toBeVisible();

    // Check if feature cards are present (6 feature cards in the features section)
    const featureCards = page.locator('section#features .grid > div');
    await expect(featureCards).toHaveCount(6);
  });

  test('Sign in button works', async ({ page }) => {
    // Click the sign in button using test ID
    await page.getByTestId('button-sign-in').click();

    // Wait for redirect to home page after auto-login
    await page.waitForURL('/');
    await page.waitForLoadState('networkidle');

    // Check if we're on the home page (authenticated view)
    await expect(page.locator('h1')).toContainText(/Get Your Resume.*Job-Ready/i);

    // Verify the "Start Optimizing" button is visible (authenticated content)
    await expect(page.getByRole('button', { name: /Start Optimizing/i })).toBeVisible();
  });

  test('Get Started button works', async ({ page }) => {
    // Click the get started button using test ID
    await page.getByTestId('button-get-started').click();

    // Wait for redirect to home page after auto-login
    await page.waitForURL('/');
    await page.waitForLoadState('networkidle');

    // Check if we're on the home page (authenticated view)
    await expect(page.locator('h1')).toContainText(/Get Your Resume.*Job-Ready/i);
  });
});