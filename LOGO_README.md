# Try2Work Logo & Header

A clean, professional logo and header have been added to your application.

## Logo Design

The logo features:
- **Modern geometric design** with a circular background
- **Blue gradient** (#3B82F6 to #1E40AF) for professionalism
- **Document icon** representing resumes/work documents
- **Sparkle/AI element** in orange to highlight AI features
- **Clean typography** with "Try2Work" branding

### Logo Variants

The logo component supports three sizes:
- **Small** (`size="sm"`) - 24px icon, text-lg
- **Medium** (`size="md"`) - 32px icon, text-xl (default)
- **Large** (`size="lg"`) - 48px icon, text-3xl

You can also toggle the text visibility:
```tsx
<Logo showText={false} /> // Icon only
<Logo showText={true} />  // Icon + text (default)
```

## Files Created

### 1. Logo Component
**Location:** `client/src/components/Logo.tsx`

**Features:**
- Scalable SVG design
- Responsive sizing
- Gradient styling
- Optional text display

**Usage:**
```tsx
import Logo from '@/components/Logo';

// Basic usage
<Logo />

// With custom size
<Logo size="lg" />

// Icon only
<Logo showText={false} />

// With custom className
<Logo className="my-custom-class" />
```

### 2. Header Component
**Location:** `client/src/components/Header.tsx`

**Features:**
- Sticky header with backdrop blur
- Logo on the left
- Navigation menu in center (Desktop)
- User menu on the right
- Responsive mobile menu
- Active route highlighting

**Navigation Items:**
- Home
- Resume Builder
- Invoices
- User profile (email)
- Logout button

### 3. Updated Files
- `client/src/App.tsx` - Added Header component
- `client/src/pages/landing.tsx` - Updated to use new Logo

## Header Layout

```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│  [Logo]    Home | Resume Builder | Invoices     [User Menu]│
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Desktop View
- Logo (left): Clickable, navigates to home
- Navigation (center): Active route highlighted
- User menu (right): Email display + Logout button

### Mobile View
- Logo (left): Same functionality
- Hamburger menu (right): Dropdown with all navigation items

## Styling

### Colors
- **Primary gradient:** Blue (#3B82F6 to #1E40AF)
- **Accent:** Orange (#FFA500) for the AI sparkle
- **Text:** Gradient from blue-600 to blue-800
- **Background:** White/transparent with backdrop blur

### Header Styles
- **Height:** 64px (h-16)
- **Position:** Sticky top
- **Background:** Semi-transparent with blur effect
- **Border:** Bottom border for separation
- **Z-index:** 50 (stays on top)

## Customization

### Change Logo Colors

Edit `client/src/components/Logo.tsx`:

```tsx
// Change the gradient colors
<linearGradient id="gradient">
  <stop offset="0%" stopColor="#YOUR_COLOR_1" />
  <stop offset="100%" stopColor="#YOUR_COLOR_2" />
</linearGradient>

// Change the sparkle/accent color
<circle cx="25" cy="25" r="3" fill="#YOUR_ACCENT_COLOR" />
```

### Change Logo Text

Edit `client/src/components/Logo.tsx`:

```tsx
<span className={`font-bold ${text} bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent`}>
  Your Brand Name
</span>
```

### Modify Navigation Items

Edit `client/src/components/Header.tsx`:

```tsx
// Add new navigation item
<Button
  variant={location === "/your-route" ? "default" : "ghost"}
  onClick={() => navigate("/your-route")}
  className="text-sm"
>
  Your Page Name
</Button>
```

### Hide Header on Specific Pages

If you want to hide the header on certain pages, modify `client/src/App.tsx`:

```tsx
function Router() {
  const [location] = useLocation();
  const showHeader = location !== "/your-special-page";

  return (
    <>
      {showHeader && <Header />}
      <Switch>
        {/* routes */}
      </Switch>
    </>
  );
}
```

## Responsive Behavior

### Desktop (≥768px)
- Full navigation menu visible
- User email displayed
- Logout button visible

### Mobile (<768px)
- Logo visible
- Navigation collapsed into hamburger menu
- Dropdown menu for all navigation items
- User info in dropdown

## Best Practices

1. **Don't modify the SVG paths** unless you know SVG - it may break the design
2. **Use the size prop** instead of custom width/height to maintain proportions
3. **Keep the gradient consistent** with your brand colors
4. **Test on mobile** to ensure the hamburger menu works properly

## Logo Export

To use the logo elsewhere (marketing materials, etc.):

1. Copy the SVG code from `Logo.tsx`
2. Remove React-specific attributes (className → class)
3. Adjust viewBox if needed
4. Export as standalone SVG file

## Future Enhancements

Potential improvements you could make:

1. **Animated logo** - Add subtle animation on hover
2. **Dark mode variant** - Different colors for dark theme
3. **Favicon** - Create favicon from the logo SVG
4. **Loading state** - Show logo during page loads
5. **Search bar** - Add search functionality to header
6. **Notifications** - Add notification bell icon
7. **Theme switcher** - Add light/dark mode toggle

## Technical Details

- **Framework:** React with TypeScript
- **Routing:** Wouter
- **Styling:** Tailwind CSS
- **Icons:** Lucide React
- **UI Components:** Shadcn/ui

## Accessibility

The header includes:
- Semantic HTML (`<header>`, `<nav>`)
- Keyboard navigation support
- ARIA labels where needed
- Focus states for buttons
- Responsive touch targets

## Browser Support

Tested and working on:
- Chrome/Edge (latest)
- Firefox (latest)
- Safari (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)
