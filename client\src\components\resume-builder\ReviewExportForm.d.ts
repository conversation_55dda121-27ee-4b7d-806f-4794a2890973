import type { Resume } from "@shared/schema";
interface ReviewExportFormProps {
    data: Partial<Resume>;
    onDataChange: (data: Partial<Resume>) => void;
    onNext: () => void;
    onPrevious: () => void;
    resumeId?: string;
}
export default function ReviewExportForm({ data, onDataChange, onPrevious, resumeId, }: ReviewExportFormProps): import("react").JSX.Element;
export {};
//# sourceMappingURL=ReviewExportForm.d.ts.map