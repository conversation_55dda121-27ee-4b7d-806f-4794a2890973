import type { Resume } from "@shared/schema";
interface ProfessionalSummaryFormProps {
    data: Partial<Resume>;
    onDataChange: (data: Partial<Resume>) => void;
    onNext: () => void;
    onPrevious: () => void;
}
export default function ProfessionalSummaryForm({ data, onDataChange, onNext, onPrevious, }: ProfessionalSummaryFormProps): import("react").JSX.Element;
export {};
//# sourceMappingURL=ProfessionalSummaryForm.d.ts.map