import { useState, useEffect } from "react";
import type { ReactElement, ReactNode } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, Download, CheckCircle, AlertTriangle, FileText, Save, CreditCard, Lock, Palette } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import type { Resume, PersonalInfo, WorkExperience, Education, Skill } from "@shared/schema";
import PaymentModal from "@/components/PaymentModal";
import TemplateSelector from "@/components/TemplateSelector";

interface ReviewExportFormProps {
  data: Partial<Resume>;
  onDataChange: (data: Partial<Resume>) => void;
  onNext: () => void;
  onPrevious: () => void;
  resumeId?: string;
}

export default function ReviewExportForm({
  data,
  onDataChange,
  onPrevious,
  resumeId,
}: ReviewExportFormProps) {
  const { toast } = useToast();
  const [resumeTitle, setResumeTitle] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState("modern");
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentRequired, setPaymentRequired] = useState(false); // Free for limited time!
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);

  // Check payment status - Free for limited time!
  useEffect(() => {
    // Always free during promotional period
    setPaymentRequired(false);
  }, [data.paymentStatus]);

  // Initialize form data
  useEffect(() => {
    if (data.title) {
      setResumeTitle(data.title);
    } else {
      const personalInfo = data.personalInfo as PersonalInfo;
      const defaultTitle = personalInfo?.firstName && personalInfo?.lastName
        ? `${personalInfo.firstName} ${personalInfo.lastName} Resume`
        : `Resume ${new Date().toLocaleDateString()}`;
      setResumeTitle(defaultTitle);
    }
    if (data.templateId) {
      setSelectedTemplate(data.templateId);
    }
  }, [data]);

  // Final save mutation
  const saveFinalResumeMutation = useMutation({
    mutationFn: async () => {
      const finalData = {
        ...data,
        title: resumeTitle,
        templateId: selectedTemplate,
        status: 'complete'
      };

      if (resumeId) {
        return await apiRequest('PUT', `/api/resumes/${resumeId}`, finalData);
      } else {
        return await apiRequest('POST', '/api/resumes', finalData);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/resumes'] });
      if (resumeId) {
        queryClient.invalidateQueries({ queryKey: ['/api/resumes', resumeId] });
      }
      toast({
        title: "Success",
        description: "Resume saved successfully!",
      });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to save resume. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Export PDF mutation
  const exportPdfMutation = useMutation({
    mutationFn: async () => {
      if (!resumeId) {
        throw new Error("Resume must be saved before exporting");
      }
      const response = await fetch(`/api/resumes/${resumeId}/pdf`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate PDF');
      }
      
      return response.blob();
    },
    onSuccess: (blob) => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${resumeTitle.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      toast({
        title: "Success",
        description: "Resume downloaded successfully!",
      });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSaveResume = () => {
    saveFinalResumeMutation.mutate();
  };

  const handleExportPdf = async () => {
    // Save first if not saved
    if (!resumeId || saveFinalResumeMutation.isPending) {
      await saveFinalResumeMutation.mutateAsync();
    }

    // Check if payment is required
    if (paymentRequired) {
      setShowPaymentModal(true);
      return;
    }

    exportPdfMutation.mutate();
  };

  // Validation checks
  const getValidationStatus = () => {
    const issues = [];
    const personalInfo = data.personalInfo as PersonalInfo;
    const workExperience = data.workExperience as WorkExperience[];
    const education = data.education as Education[];
    const skills = data.skills as Skill[];

    if (!personalInfo?.firstName || !personalInfo?.lastName || !personalInfo?.email || !personalInfo?.phone) {
      issues.push("Personal information is incomplete");
    }

    if (!data.professionalSummary || data.professionalSummary.length < 50) {
      issues.push("Professional summary is missing or too short");
    }

    if (!workExperience || workExperience.length === 0) {
      issues.push("No work experience added");
    }

    if (!education || education.length === 0) {
      issues.push("No education information added");
    }

    if (!skills || skills.length < 3) {
      issues.push("Add at least 3 skills");
    }

    if (!data.targetJobTitle) {
      issues.push("Target job title not specified");
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  };

  const validation = getValidationStatus();

  const getCompletionStats = () => {
    const personalInfo = data.personalInfo as PersonalInfo;
    const workExperience = data.workExperience as WorkExperience[] || [];
    const education = data.education as Education[] || [];
    const skills = data.skills as Skill[] || [];

    return {
      personalInfo: !!(personalInfo?.firstName && personalInfo?.lastName && personalInfo?.email),
      professionalSummary: !!(data.professionalSummary && data.professionalSummary.length > 50),
      workExperience: workExperience.length > 0,
      education: education.length > 0,
      skills: skills.length >= 3,
      jobTargeting: !!(data.targetJobTitle && data.jobDescription)
    };
  };

  const completionStats = getCompletionStats();
  type CompletionKey = keyof typeof completionStats;
  const sectionLabels: Record<CompletionKey, string> = {
    personalInfo: "Personal Information",
    professionalSummary: "Professional Summary",
    workExperience: "Work Experience",
    education: "Education",
    skills: "Skills",
    jobTargeting: "Job Targeting",
  };
  const completionEntries = Object.entries(completionStats) as Array<[CompletionKey, boolean]>;
  const completedSections = completionEntries.filter(([, completed]) => completed).length;
  const totalSections = completionEntries.length;


  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="hidden lg:block">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Review & Export</h1>
          <span className="text-sm text-muted-foreground bg-secondary px-3 py-1 rounded-full">
            Step 6 of 6
          </span>
        </div>
        <p className="text-muted-foreground">
          Review your resume, make final adjustments, and export as PDF.
        </p>
      </div>

      {/* Resume Settings */}
      <Card className="border-border">
        <CardHeader>
          <CardTitle className="text-lg">Resume Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="resumeTitle">Resume Title</Label>
            <Input
              id="resumeTitle"
              type="text"
              value={resumeTitle}
              onChange={(e) => setResumeTitle(e.target.value)}
              placeholder="My Professional Resume"
              autoComplete="off"
              data-testid="input-resume-title"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="template">Template</Label>
            <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
              <SelectTrigger data-testid="select-template">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="modern">Modern</SelectItem>
                <SelectItem value="classic">Classic</SelectItem>
                <SelectItem value="creative">Creative</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Template Selection */}
      <Card className="border-border">
        <CardHeader>
          <CardTitle className="text-lg flex items-center justify-between">
            Choose Template
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowTemplateSelector(true)}
              className="flex items-center gap-2"
            >
              <Palette className="h-4 w-4" />
              Browse Industry Templates
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            {["modern", "classic", "creative"].map((template) => (
              <div
                key={template}
                className={`border rounded-lg p-4 cursor-pointer transition-all hover:border-primary ${
                  selectedTemplate === template ? "border-primary bg-primary/5" : ""
                }`}
                onClick={() => setSelectedTemplate(template)}
              >
                <div className="h-20 bg-muted rounded mb-2 flex items-center justify-center">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="font-medium capitalize">{template}</h3>
                <p className="text-xs text-muted-foreground">
                  {(() => {
                    switch (template) {
                      case "modern":
                        return "Clean and professional";
                      case "classic":
                        return "Traditional and elegant";
                      case "creative":
                        return "Bold and eye-catching";
                      default:
                        return "Professional template";
                    }
                  })()}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Completion Status */}
      <Card className="border-border">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            Resume Completion
            <Badge variant="secondary" className="ml-2">
              {completedSections}/{totalSections} Complete
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {completionEntries.map(([section, completed]) => (
              <div key={section} className="flex items-center space-x-3">
                {completed ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                )}
                <span className={`text-sm ${completed ? 'text-foreground' : 'text-muted-foreground'}`}>
                  {sectionLabels[section]}
                </span>
              </div>
            ))}

          </div>
        </CardContent>
      </Card>

      {/* Validation Issues */}
      {!validation.isValid && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="text-lg flex items-center text-yellow-800">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Issues to Address
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-yellow-700 space-y-1">
              {validation.issues.map((issue, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>{issue}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Job Targeting Summary */}
      {(() => {
        const extractedKeywords = data.extractedKeywords;
        const hasKeywords = extractedKeywords && Array.isArray(extractedKeywords) && extractedKeywords.length > 0;
        return hasKeywords ? (
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="text-lg text-green-800">Job Targeting Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.targetJobTitle && (
                  <div>
                    <span className="text-sm font-medium text-green-700">Target Position: </span>
                    <span className="text-sm text-green-600">{data.targetJobTitle}</span>
                    {data.targetCompany && (
                      <span className="text-sm text-green-600"> at {data.targetCompany}</span>
                    )}
                  </div>
                )}
                
                <div>
                  <span className="text-sm font-medium text-green-700 block mb-2">
                    Optimized Keywords ({extractedKeywords.length}):
                  </span>
                  <div className="flex flex-wrap gap-1">
                    {(extractedKeywords as string[]).slice(0, 15).map((keyword, index) => (
                      <Badge key={index} variant="outline" className="text-xs border-green-300 text-green-700">
                        {keyword}
                      </Badge>
                    ))}
                    {extractedKeywords.length > 15 && (
                      <Badge variant="outline" className="text-xs border-green-300 text-green-700">
                        +{extractedKeywords.length - 15} more
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : null;
      })()}

      {/* Export Actions */}
      <Card className="border-border">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            Export Your Resume
            <Badge variant="secondary" className="flex items-center gap-1 bg-green-100 text-green-700 border-green-300">
              FREE - Limited Time!
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              onClick={handleSaveResume}
              disabled={saveFinalResumeMutation.isPending}
              variant="outline"
              className="w-full"
              data-testid="button-save-resume"
            >
              <Save className="h-4 w-4 mr-2" />
              {saveFinalResumeMutation.isPending ? "Saving..." : "Save Resume"}
            </Button>

            <Button
              onClick={handleExportPdf}
              disabled={!validation.isValid || exportPdfMutation.isPending || saveFinalResumeMutation.isPending}
              className="w-full relative"
              data-testid="button-export-pdf"
            >
              {paymentRequired ? (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  {exportPdfMutation.isPending ? "Processing..." : "Pay & Download PDF"}
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  {exportPdfMutation.isPending ? "Generating..." : "Download PDF"}
                </>
              )}
            </Button>
          </div>

          {!validation.isValid && (
            <p className="text-sm text-muted-foreground text-center">
              Please address the issues above before exporting your resume.
            </p>
          )}
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 flex items-start gap-2">
            <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-green-800">Free for a limited time!</p>
              <p className="text-green-600">
                Download unlimited PDF versions of your resume completely free during our promotional period.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Success Message */}
      {validation.isValid && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-6 text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              Congratulations! Your Resume is Ready
            </h3>
            <p className="text-sm text-green-600 mb-4">
              Your AI-optimized resume is complete and ready for download. 
              It includes all the keywords and formatting needed to pass ATS systems.
            </p>
            <div className="flex items-center justify-center space-x-2 text-sm text-green-700">
              <FileText className="h-4 w-4" />
              <span>Professional • ATS-Optimized • Keyword-Rich</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-border">
        <Button variant="ghost" onClick={onPrevious} data-testid="button-previous">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <Button
          onClick={() => window.location.href = "/"}
          variant="outline"
          data-testid="button-finish"
        >
          Finish & Go Home
        </Button>
      </div>
      
      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          resumeId={resumeId || ''}
          resumeTitle={resumeTitle}
          amount={199} // $1.99 in cents
          currency="usd"
          onSuccess={() => {
            setShowPaymentModal(false);
            setPaymentRequired(false);
            queryClient.invalidateQueries({ queryKey: ['/api/resumes', resumeId] });
            toast({
              title: "Payment Successful",
              description: "Your payment has been processed. You can now download your resume.",
            });
          }}
        />
      )}
      
      {/* Template Selector Modal */}
      {showTemplateSelector && (
        <TemplateSelector
          isOpen={showTemplateSelector}
          onClose={() => setShowTemplateSelector(false)}
          resumeId={resumeId || ''}
          currentTemplate={selectedTemplate}
          industry={data.industry || undefined}
        />
      )}
    </div>
  );
}
