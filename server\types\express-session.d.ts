declare module 'express-session' {
  import * as express from 'express';
  import * as core from 'express-serve-static-core';

  interface MemoryStore {
    get(sid: string, callback: (err: any, session?: SessionData) => void): void;
    set(sid: string, session: SessionData, callback: (err: any) => void): void;
    destroy(sid: string, callback: (err: any) => void): void;
    length(callback: (err: any, length: number) => void): void;
    clear(callback: (err: any) => void): void;
  }

  interface SessionData {
    [key: string]: any;
  }

  interface SessionStore {
    get?: (sid: string, callback: (err: any, session?: SessionData) => void) => void;
    set?: (sid: string, session: SessionData, callback?: (err: any) => void) => void;
    destroy?: (sid: string, callback: (err: any) => void) => void;
    length?: (callback: (err: any, length: number) => void) => void;
    clear?: (callback: (err: any) => void) => void;
  }

  interface SessionOptions {
    secret: string | string[];
    name?: string;
    store?: SessionStore;
    cookie?: CookieOptions;
    genid?: (req: express.Request) => string;
    rolling?: boolean;
    resave?: boolean;
    saveUninitialized?: boolean;
    proxy?: boolean;
    sameSite?: 'lax' | 'strict' | boolean;
    secure?: boolean;
    httpOnly?: boolean;
    maxAge?: number;
  }

  interface CookieOptions {
    maxAge?: number;
    signed?: boolean;
    expires?: Date;
    httpOnly?: boolean;
    path?: string;
    sameSite?: 'lax' | 'strict' | boolean;
    secure?: boolean;
  }

  interface Session {
    id: string;
    userId?: string;
    [key: string]: any;
    destroy(callback?: (err: any) => void): void;
    regenerate(callback: (err: any) => void): void;
    save(callback?: (err: any) => void): void;
    touch(): void;
    cookie: Cookie;
  }

  interface Cookie {
    maxAge?: number;
    originalMaxAge?: number;
    expires?: Date;
    httpOnly?: boolean;
    path?: string;
    sameSite?: 'lax' | 'strict' | boolean;
    secure?: boolean;
  }

  interface Request extends express.Request {
    session: Session;
    sessionID: string;
  }

  interface Response extends express.Response {
    locals: core.Response['locals'] & {
      sessionID?: string;
    };
  }

  interface Application extends express.Application {
    use(generators: (req: Request, res: Response, next: core.NextFunction) => void): void;
  }

  export = session;
}