# Clerk to Passport.js Migration Guide

This guide explains how to migrate from Clerk authentication to Passport.js with Google OAuth.

## Current Status

✅ **Completed:**
- Passport.js authentication setup
- Google OAuth strategy implementation
- Session management configuration
- Client-side auth hook abstraction
- Server-side route integration

🔄 **In Progress:**
- Gradual component migration

## Migration Steps

### Step 1: Environment Variables

Add these to your `.env` file:

```env
# Google OAuth Credentials
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Session Secret
SESSION_SECRET=your-session-secret-key

# Remove Clerk environment variables (optional, after migration)
# VITE_CLERK_PUBLISHABLE_KEY=your-clerk-key
```

### Step 2: Database Migration

The database schema is already compatible. Users are stored with:
- `id` (Google OAuth ID)
- `email`
- `firstName`
- `lastName`
- `profileImageUrl`

### Step 3: Client-Side Migration

#### Option A: Gradual Migration (Recommended)

1. **Update `client/src/main.tsx`** to conditionally use Clerk or Passport:

```tsx
import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (PUBLISHABLE_KEY) {
  // Use Clerk (current)
  import("@clerk/clerk-react").then(({ ClerkProvider }) => {
    createRoot(document.getElementById("root")!).render(
      <ClerkProvider publishableKey={PUBLISHABLE_KEY}>
        <App />
      </ClerkProvider>
    );
  });
} else {
  // Use Passport.js (new)
  createRoot(document.getElementById("root")!).render(<App />);
}
```

2. **Update components** to use the new `useAuth` hook:

```tsx
// Before (Clerk)
import { useUser } from "@clerk/clerk-react";
const { user, isLoaded } = useUser();

// After (Universal)
import { useAuth } from "@/hooks/useAuth";
const { user, isLoading, isAuthenticated } = useAuth();
```

#### Option B: Complete Migration

1. **Remove Clerk dependencies** from `package.json`:
```bash
npm uninstall @clerk/backend @clerk/clerk-react @clerk/express
```

2. **Update `client/src/main.tsx`** to remove ClerkProvider

3. **Update all components** to use the new `useAuth` hook

### Step 4: Server-Side Migration

#### Option A: Gradual Migration

The server is already configured to support both systems. Routes will use Clerk authentication by default.

#### Option B: Complete Migration

1. **Remove Clerk middleware** from `server/routes.ts`:
```typescript
// Remove this line
import { setupClerkAuth, requireClerkAuth } from "./clerkAuth.js";

// Replace requireClerkAuth with requireAuth
// requireClerkAuth -> requireAuth
```

2. **Update route protection**:
```typescript
// Before
app.get("/api/resumes", requireClerkAuth, async (req: any, res) => {

// After
app.get("/api/resumes", requireAuth, async (req: any, res) => {
```

### Step 5: Testing

1. **Test Google OAuth flow**:
   - Visit `/api/auth/google`
   - Complete Google authentication
   - Verify user is created in database
   - Test protected routes

2. **Test session persistence**:
   - Refresh page
   - Verify user remains authenticated
   - Test logout functionality

### Step 6: Cleanup (After Complete Migration)

1. **Remove Clerk files**:
   - `server/clerkAuth.ts`
   - `server/clerkAuth.js`
   - `server/clerkAuth.d.ts`

2. **Remove Clerk environment variables** from `.env`

3. **Update package.json** to remove Clerk dependencies

## Benefits of Migration

### Cost Savings
- **Clerk**: Free tier limited to 10,000 MAU, then paid
- **Passport.js**: Completely unlimited, free forever

### Control
- Full control over authentication flow
- No vendor lock-in
- Customizable session management
- Easy to add additional OAuth providers

### Performance
- Reduced bundle size (no Clerk client library)
- Faster page loads
- Better SEO (no client-side auth dependencies)

## Troubleshooting

### Common Issues

1. **Google OAuth not working**
   - Verify `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET` are set
   - Ensure Google Cloud Console redirect URI matches: `http://localhost:5001/api/auth/google/callback`

2. **Session issues**
   - Verify `SESSION_SECRET` is set
   - Check browser cookies for session ID

3. **TypeScript errors**
   - Ensure all imports are correct
   - Check type definitions in `shared/schema.ts`

### Migration Support

During the migration period, the application will automatically detect which authentication system to use based on the presence of `VITE_CLERK_PUBLISHABLE_KEY`.

## Timeline

- **Week 1**: Complete Passport.js implementation ✅
- **Week 2**: Gradual client-side migration
- **Week 3**: Complete server-side migration
- **Week 4**: Testing and cleanup

## Rollback Plan

If issues arise during migration:

1. Re-enable `VITE_CLERK_PUBLISHABLE_KEY`
2. The application will automatically fall back to Clerk
3. No data loss occurs
4. Users can continue using existing accounts

## Next Steps

1. Set up Google OAuth credentials in Google Cloud Console
2. Add environment variables to `.env`
3. Test the Google OAuth flow
4. Begin gradual component migration
5. Monitor for any issues during the transition