import fetch from 'node-fetch';

export interface ScrapedJobData {
  title?: string;
  company?: string;
  description: string;
  location?: string;
  requirements?: string[];
}

export async function scrapeJobFromUrl(url: string): Promise<ScrapedJobData> {
  try {
    // Basic URL validation
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    
    // For now, we'll use a simple fetch approach
    // In a production app, you might want to use Puppeteer or specific APIs
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const html = await response.text();
    
    // Extract job information based on common patterns
    let title = extractTitle(html);
    let company = extractCompany(html, hostname);
    let description = extractDescription(html);
    let location = extractLocation(html);
    
    // If we can't extract much content, return the raw HTML as description
    if (!description || description.length < 100) {
      // Strip HTML tags and get text content
      description = html.replace(/<[^>]*>/g, ' ')
        .replace(/\s+/g, ' ')
        .trim()
        .substring(0, 5000); // Limit to reasonable size
    }
    
    return {
      title,
      company,
      description,
      location
    };
    
  } catch (error) {
    console.error('Error scraping job URL:', error);
    throw new Error(`Failed to scrape job posting: ${(error as Error).message}`);
  }
}

function extractTitle(html: string): string | undefined {
  // Common patterns for job titles
  const titlePatterns = [
    /<title[^>]*>([^<]+)/i,
    /<h1[^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)/i,
    /<h1[^>]*class="[^"]*job[^"]*"[^>]*>([^<]+)/i,
    /<h1[^>]*>([^<]+)</i,
  ];
  
  for (const pattern of titlePatterns) {
    const match = html.match(pattern);
    if (match && match[1]) {
      return match[1].trim().replace(/\s+/g, ' ');
    }
  }
  
  return undefined;
}

function extractCompany(html: string, hostname: string): string | undefined {
  // Try to extract company name from various patterns
  const companyPatterns = [
    /<[^>]*class="[^"]*company[^"]*"[^>]*>([^<]+)/i,
    /<[^>]*class="[^"]*employer[^"]*"[^>]*>([^<]+)/i,
  ];
  
  for (const pattern of companyPatterns) {
    const match = html.match(pattern);
    if (match && match[1]) {
      return match[1].trim().replace(/\s+/g, ' ');
    }
  }
  
  // Try to infer from hostname
  if (hostname.includes('linkedin')) {
    const linkedinMatch = html.match(/hiring for ([^,<]+)/i);
    if (linkedinMatch) return linkedinMatch[1].trim();
  }
  
  return undefined;
}

function extractDescription(html: string): string {
  // Look for job description patterns
  const descPatterns = [
    /<div[^>]*class="[^"]*description[^"]*"[^>]*>(.*?)<\/div>/is,
    /<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)<\/div>/is,
    /<section[^>]*class="[^"]*description[^"]*"[^>]*>(.*?)<\/section>/is,
  ];
  
  for (const pattern of descPatterns) {
    const match = html.match(pattern);
    if (match && match[1] && match[1].length > 100) {
      return match[1]
        .replace(/<[^>]*>/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();
    }
  }
  
  // Fallback: extract all text content
  return html
    .replace(/<script[^>]*>.*?<\/script>/gis, '')
    .replace(/<style[^>]*>.*?<\/style>/gis, '')
    .replace(/<[^>]*>/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
}

function extractLocation(html: string): string | undefined {
  const locationPatterns = [
    /<[^>]*class="[^"]*location[^"]*"[^>]*>([^<]+)/i,
    /<[^>]*class="[^"]*address[^"]*"[^>]*>([^<]+)/i,
  ];
  
  for (const pattern of locationPatterns) {
    const match = html.match(pattern);
    if (match && match[1]) {
      return match[1].trim().replace(/\s+/g, ' ');
    }
  }
  
  return undefined;
}

