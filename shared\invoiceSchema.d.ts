import { z } from "zod";
export declare const constructionWorkItemSchema: z.ZodObject<{
    itemNo: z.ZodNumber;
    description: z.ZodString;
    scheduledValue: z.ZodNumber;
    fromPreviousApplication: z.ZodNumber;
    thisPeriod: z.ZodNumber;
    materialsPresently: z.ZodNumber;
    totalCompleted: z.ZodNumber;
    percent: z.ZodNumber;
    balanceToFinish: z.ZodNumber;
    retainage: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    description: string;
    percent: number;
    itemNo: number;
    scheduledValue: number;
    fromPreviousApplication: number;
    thisPeriod: number;
    materialsPresently: number;
    totalCompleted: number;
    balanceToFinish: number;
    retainage: number;
}, {
    description: string;
    percent: number;
    itemNo: number;
    scheduledValue: number;
    fromPreviousApplication: number;
    thisPeriod: number;
    materialsPresently: number;
    totalCompleted: number;
    balanceToFinish: number;
    retainage: number;
}>;
export declare const constructionPaymentApplicationSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    userId: z.ZodString;
    applicationNo: z.ZodString;
    applicationDate: z.ZodString;
    periodTo: z.ZodString;
    projectNo: z.ZodString;
    contractDate: z.ZodString;
    project: z.ZodString;
    projectAddress: z.ZodString;
    owner: z.ZodString;
    ownerAddress: z.ZodString;
    contractor: z.ZodString;
    contractorAddress: z.ZodString;
    contractFor: z.ZodString;
    originalContractSum: z.ZodNumber;
    netChangeByChangeOrders: z.ZodNumber;
    workItems: z.ZodArray<z.ZodObject<{
        itemNo: z.ZodNumber;
        description: z.ZodString;
        scheduledValue: z.ZodNumber;
        fromPreviousApplication: z.ZodNumber;
        thisPeriod: z.ZodNumber;
        materialsPresently: z.ZodNumber;
        totalCompleted: z.ZodNumber;
        percent: z.ZodNumber;
        balanceToFinish: z.ZodNumber;
        retainage: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }, {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }>, "many">;
    retainagePercent: z.ZodNumber;
    retainageOnCompletedWork: z.ZodNumber;
    retainageOnStoredMaterial: z.ZodNumber;
    lessPreviousCertificates: z.ZodNumber;
    distributeTo: z.ZodObject<{
        owner: z.ZodBoolean;
        architect: z.ZodBoolean;
        contractor: z.ZodBoolean;
        field: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    }, {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    }>;
    createdAt: z.ZodOptional<z.ZodDate>;
    updatedAt: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    userId: string;
    applicationNo: string;
    applicationDate: string;
    periodTo: string;
    projectNo: string;
    contractDate: string;
    project: string;
    projectAddress: string;
    owner: string;
    ownerAddress: string;
    contractor: string;
    contractorAddress: string;
    contractFor: string;
    originalContractSum: number;
    netChangeByChangeOrders: number;
    workItems: {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }[];
    retainagePercent: number;
    retainageOnCompletedWork: number;
    retainageOnStoredMaterial: number;
    lessPreviousCertificates: number;
    distributeTo: {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    };
    id?: string | undefined;
    createdAt?: Date | undefined;
    updatedAt?: Date | undefined;
}, {
    userId: string;
    applicationNo: string;
    applicationDate: string;
    periodTo: string;
    projectNo: string;
    contractDate: string;
    project: string;
    projectAddress: string;
    owner: string;
    ownerAddress: string;
    contractor: string;
    contractorAddress: string;
    contractFor: string;
    originalContractSum: number;
    netChangeByChangeOrders: number;
    workItems: {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }[];
    retainagePercent: number;
    retainageOnCompletedWork: number;
    retainageOnStoredMaterial: number;
    lessPreviousCertificates: number;
    distributeTo: {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    };
    id?: string | undefined;
    createdAt?: Date | undefined;
    updatedAt?: Date | undefined;
}>;
export declare const insertConstructionPaymentApplicationSchema: z.ZodObject<Omit<{
    id: z.ZodOptional<z.ZodString>;
    userId: z.ZodString;
    applicationNo: z.ZodString;
    applicationDate: z.ZodString;
    periodTo: z.ZodString;
    projectNo: z.ZodString;
    contractDate: z.ZodString;
    project: z.ZodString;
    projectAddress: z.ZodString;
    owner: z.ZodString;
    ownerAddress: z.ZodString;
    contractor: z.ZodString;
    contractorAddress: z.ZodString;
    contractFor: z.ZodString;
    originalContractSum: z.ZodNumber;
    netChangeByChangeOrders: z.ZodNumber;
    workItems: z.ZodArray<z.ZodObject<{
        itemNo: z.ZodNumber;
        description: z.ZodString;
        scheduledValue: z.ZodNumber;
        fromPreviousApplication: z.ZodNumber;
        thisPeriod: z.ZodNumber;
        materialsPresently: z.ZodNumber;
        totalCompleted: z.ZodNumber;
        percent: z.ZodNumber;
        balanceToFinish: z.ZodNumber;
        retainage: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }, {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }>, "many">;
    retainagePercent: z.ZodNumber;
    retainageOnCompletedWork: z.ZodNumber;
    retainageOnStoredMaterial: z.ZodNumber;
    lessPreviousCertificates: z.ZodNumber;
    distributeTo: z.ZodObject<{
        owner: z.ZodBoolean;
        architect: z.ZodBoolean;
        contractor: z.ZodBoolean;
        field: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    }, {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    }>;
    createdAt: z.ZodOptional<z.ZodDate>;
    updatedAt: z.ZodOptional<z.ZodDate>;
}, "id" | "createdAt" | "updatedAt">, "strip", z.ZodTypeAny, {
    userId: string;
    applicationNo: string;
    applicationDate: string;
    periodTo: string;
    projectNo: string;
    contractDate: string;
    project: string;
    projectAddress: string;
    owner: string;
    ownerAddress: string;
    contractor: string;
    contractorAddress: string;
    contractFor: string;
    originalContractSum: number;
    netChangeByChangeOrders: number;
    workItems: {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }[];
    retainagePercent: number;
    retainageOnCompletedWork: number;
    retainageOnStoredMaterial: number;
    lessPreviousCertificates: number;
    distributeTo: {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    };
}, {
    userId: string;
    applicationNo: string;
    applicationDate: string;
    periodTo: string;
    projectNo: string;
    contractDate: string;
    project: string;
    projectAddress: string;
    owner: string;
    ownerAddress: string;
    contractor: string;
    contractorAddress: string;
    contractFor: string;
    originalContractSum: number;
    netChangeByChangeOrders: number;
    workItems: {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }[];
    retainagePercent: number;
    retainageOnCompletedWork: number;
    retainageOnStoredMaterial: number;
    lessPreviousCertificates: number;
    distributeTo: {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    };
}>;
export declare const updateConstructionPaymentApplicationSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodOptional<z.ZodOptional<z.ZodDate>>;
    updatedAt: z.ZodOptional<z.ZodOptional<z.ZodDate>>;
    userId: z.ZodOptional<z.ZodString>;
    applicationNo: z.ZodOptional<z.ZodString>;
    applicationDate: z.ZodOptional<z.ZodString>;
    periodTo: z.ZodOptional<z.ZodString>;
    projectNo: z.ZodOptional<z.ZodString>;
    contractDate: z.ZodOptional<z.ZodString>;
    project: z.ZodOptional<z.ZodString>;
    projectAddress: z.ZodOptional<z.ZodString>;
    owner: z.ZodOptional<z.ZodString>;
    ownerAddress: z.ZodOptional<z.ZodString>;
    contractor: z.ZodOptional<z.ZodString>;
    contractorAddress: z.ZodOptional<z.ZodString>;
    contractFor: z.ZodOptional<z.ZodString>;
    originalContractSum: z.ZodOptional<z.ZodNumber>;
    netChangeByChangeOrders: z.ZodOptional<z.ZodNumber>;
    workItems: z.ZodOptional<z.ZodArray<z.ZodObject<{
        itemNo: z.ZodNumber;
        description: z.ZodString;
        scheduledValue: z.ZodNumber;
        fromPreviousApplication: z.ZodNumber;
        thisPeriod: z.ZodNumber;
        materialsPresently: z.ZodNumber;
        totalCompleted: z.ZodNumber;
        percent: z.ZodNumber;
        balanceToFinish: z.ZodNumber;
        retainage: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }, {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }>, "many">>;
    retainagePercent: z.ZodOptional<z.ZodNumber>;
    retainageOnCompletedWork: z.ZodOptional<z.ZodNumber>;
    retainageOnStoredMaterial: z.ZodOptional<z.ZodNumber>;
    lessPreviousCertificates: z.ZodOptional<z.ZodNumber>;
    distributeTo: z.ZodOptional<z.ZodObject<{
        owner: z.ZodBoolean;
        architect: z.ZodBoolean;
        contractor: z.ZodBoolean;
        field: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    }, {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    }>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt?: Date | undefined;
    updatedAt?: Date | undefined;
    userId?: string | undefined;
    applicationNo?: string | undefined;
    applicationDate?: string | undefined;
    periodTo?: string | undefined;
    projectNo?: string | undefined;
    contractDate?: string | undefined;
    project?: string | undefined;
    projectAddress?: string | undefined;
    owner?: string | undefined;
    ownerAddress?: string | undefined;
    contractor?: string | undefined;
    contractorAddress?: string | undefined;
    contractFor?: string | undefined;
    originalContractSum?: number | undefined;
    netChangeByChangeOrders?: number | undefined;
    workItems?: {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }[] | undefined;
    retainagePercent?: number | undefined;
    retainageOnCompletedWork?: number | undefined;
    retainageOnStoredMaterial?: number | undefined;
    lessPreviousCertificates?: number | undefined;
    distributeTo?: {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    } | undefined;
}, {
    id: string;
    createdAt?: Date | undefined;
    updatedAt?: Date | undefined;
    userId?: string | undefined;
    applicationNo?: string | undefined;
    applicationDate?: string | undefined;
    periodTo?: string | undefined;
    projectNo?: string | undefined;
    contractDate?: string | undefined;
    project?: string | undefined;
    projectAddress?: string | undefined;
    owner?: string | undefined;
    ownerAddress?: string | undefined;
    contractor?: string | undefined;
    contractorAddress?: string | undefined;
    contractFor?: string | undefined;
    originalContractSum?: number | undefined;
    netChangeByChangeOrders?: number | undefined;
    workItems?: {
        description: string;
        percent: number;
        itemNo: number;
        scheduledValue: number;
        fromPreviousApplication: number;
        thisPeriod: number;
        materialsPresently: number;
        totalCompleted: number;
        balanceToFinish: number;
        retainage: number;
    }[] | undefined;
    retainagePercent?: number | undefined;
    retainageOnCompletedWork?: number | undefined;
    retainageOnStoredMaterial?: number | undefined;
    lessPreviousCertificates?: number | undefined;
    distributeTo?: {
        field: boolean;
        owner: boolean;
        contractor: boolean;
        architect: boolean;
    } | undefined;
}>;
export type ConstructionPaymentApplication = z.infer<typeof constructionPaymentApplicationSchema>;
export type InsertConstructionPaymentApplication = z.infer<typeof insertConstructionPaymentApplicationSchema>;
export type UpdateConstructionPaymentApplication = z.infer<typeof updateConstructionPaymentApplicationSchema>;
export declare const consultingLineItemSchema: z.ZodObject<{
    description: z.ZodString;
    feeValue: z.ZodNumber;
    percentDone: z.ZodNumber;
    feeToDate: z.ZodNumber;
    prevInvoice: z.ZodNumber;
    thisInvoice: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    description: string;
    feeValue: number;
    percentDone: number;
    feeToDate: number;
    prevInvoice: number;
    thisInvoice: number;
}, {
    description: string;
    feeValue: number;
    percentDone: number;
    feeToDate: number;
    prevInvoice: number;
    thisInvoice: number;
}>;
export declare const consultingInvoiceSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    userId: z.ZodString;
    companyName: z.ZodString;
    companyLogoUrl: z.ZodOptional<z.ZodString>;
    clientName: z.ZodString;
    clientAddress: z.ZodArray<z.ZodString, "many">;
    attention: z.ZodOptional<z.ZodString>;
    invoiceNumber: z.ZodString;
    invoiceDate: z.ZodString;
    gst: z.ZodString;
    jobNo: z.ZodString;
    orderNumber: z.ZodString;
    totalAmount: z.ZodNumber;
    projectReference: z.ZodString;
    progressClaimNo: z.ZodString;
    lineItems: z.ZodArray<z.ZodObject<{
        description: z.ZodString;
        feeValue: z.ZodNumber;
        percentDone: z.ZodNumber;
        feeToDate: z.ZodNumber;
        prevInvoice: z.ZodNumber;
        thisInvoice: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }, {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }>, "many">;
    gstRate: z.ZodNumber;
    createdAt: z.ZodOptional<z.ZodDate>;
    updatedAt: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    userId: string;
    companyName: string;
    clientName: string;
    clientAddress: string[];
    invoiceNumber: string;
    invoiceDate: string;
    gst: string;
    jobNo: string;
    orderNumber: string;
    totalAmount: number;
    projectReference: string;
    progressClaimNo: string;
    lineItems: {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }[];
    gstRate: number;
    id?: string | undefined;
    createdAt?: Date | undefined;
    updatedAt?: Date | undefined;
    companyLogoUrl?: string | undefined;
    attention?: string | undefined;
}, {
    userId: string;
    companyName: string;
    clientName: string;
    clientAddress: string[];
    invoiceNumber: string;
    invoiceDate: string;
    gst: string;
    jobNo: string;
    orderNumber: string;
    totalAmount: number;
    projectReference: string;
    progressClaimNo: string;
    lineItems: {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }[];
    gstRate: number;
    id?: string | undefined;
    createdAt?: Date | undefined;
    updatedAt?: Date | undefined;
    companyLogoUrl?: string | undefined;
    attention?: string | undefined;
}>;
export declare const insertConsultingInvoiceSchema: z.ZodObject<Omit<{
    id: z.ZodOptional<z.ZodString>;
    userId: z.ZodString;
    companyName: z.ZodString;
    companyLogoUrl: z.ZodOptional<z.ZodString>;
    clientName: z.ZodString;
    clientAddress: z.ZodArray<z.ZodString, "many">;
    attention: z.ZodOptional<z.ZodString>;
    invoiceNumber: z.ZodString;
    invoiceDate: z.ZodString;
    gst: z.ZodString;
    jobNo: z.ZodString;
    orderNumber: z.ZodString;
    totalAmount: z.ZodNumber;
    projectReference: z.ZodString;
    progressClaimNo: z.ZodString;
    lineItems: z.ZodArray<z.ZodObject<{
        description: z.ZodString;
        feeValue: z.ZodNumber;
        percentDone: z.ZodNumber;
        feeToDate: z.ZodNumber;
        prevInvoice: z.ZodNumber;
        thisInvoice: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }, {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }>, "many">;
    gstRate: z.ZodNumber;
    createdAt: z.ZodOptional<z.ZodDate>;
    updatedAt: z.ZodOptional<z.ZodDate>;
}, "id" | "createdAt" | "updatedAt">, "strip", z.ZodTypeAny, {
    userId: string;
    companyName: string;
    clientName: string;
    clientAddress: string[];
    invoiceNumber: string;
    invoiceDate: string;
    gst: string;
    jobNo: string;
    orderNumber: string;
    totalAmount: number;
    projectReference: string;
    progressClaimNo: string;
    lineItems: {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }[];
    gstRate: number;
    companyLogoUrl?: string | undefined;
    attention?: string | undefined;
}, {
    userId: string;
    companyName: string;
    clientName: string;
    clientAddress: string[];
    invoiceNumber: string;
    invoiceDate: string;
    gst: string;
    jobNo: string;
    orderNumber: string;
    totalAmount: number;
    projectReference: string;
    progressClaimNo: string;
    lineItems: {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }[];
    gstRate: number;
    companyLogoUrl?: string | undefined;
    attention?: string | undefined;
}>;
export declare const updateConsultingInvoiceSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodOptional<z.ZodOptional<z.ZodDate>>;
    updatedAt: z.ZodOptional<z.ZodOptional<z.ZodDate>>;
    userId: z.ZodOptional<z.ZodString>;
    companyName: z.ZodOptional<z.ZodString>;
    companyLogoUrl: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    clientName: z.ZodOptional<z.ZodString>;
    clientAddress: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    attention: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    invoiceNumber: z.ZodOptional<z.ZodString>;
    invoiceDate: z.ZodOptional<z.ZodString>;
    gst: z.ZodOptional<z.ZodString>;
    jobNo: z.ZodOptional<z.ZodString>;
    orderNumber: z.ZodOptional<z.ZodString>;
    totalAmount: z.ZodOptional<z.ZodNumber>;
    projectReference: z.ZodOptional<z.ZodString>;
    progressClaimNo: z.ZodOptional<z.ZodString>;
    lineItems: z.ZodOptional<z.ZodArray<z.ZodObject<{
        description: z.ZodString;
        feeValue: z.ZodNumber;
        percentDone: z.ZodNumber;
        feeToDate: z.ZodNumber;
        prevInvoice: z.ZodNumber;
        thisInvoice: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }, {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }>, "many">>;
    gstRate: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt?: Date | undefined;
    updatedAt?: Date | undefined;
    userId?: string | undefined;
    companyName?: string | undefined;
    companyLogoUrl?: string | undefined;
    clientName?: string | undefined;
    clientAddress?: string[] | undefined;
    attention?: string | undefined;
    invoiceNumber?: string | undefined;
    invoiceDate?: string | undefined;
    gst?: string | undefined;
    jobNo?: string | undefined;
    orderNumber?: string | undefined;
    totalAmount?: number | undefined;
    projectReference?: string | undefined;
    progressClaimNo?: string | undefined;
    lineItems?: {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }[] | undefined;
    gstRate?: number | undefined;
}, {
    id: string;
    createdAt?: Date | undefined;
    updatedAt?: Date | undefined;
    userId?: string | undefined;
    companyName?: string | undefined;
    companyLogoUrl?: string | undefined;
    clientName?: string | undefined;
    clientAddress?: string[] | undefined;
    attention?: string | undefined;
    invoiceNumber?: string | undefined;
    invoiceDate?: string | undefined;
    gst?: string | undefined;
    jobNo?: string | undefined;
    orderNumber?: string | undefined;
    totalAmount?: number | undefined;
    projectReference?: string | undefined;
    progressClaimNo?: string | undefined;
    lineItems?: {
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }[] | undefined;
    gstRate?: number | undefined;
}>;
export type ConsultingInvoice = z.infer<typeof consultingInvoiceSchema>;
export type InsertConsultingInvoice = z.infer<typeof insertConsultingInvoiceSchema>;
export type UpdateConsultingInvoice = z.infer<typeof updateConsultingInvoiceSchema>;
//# sourceMappingURL=invoiceSchema.d.ts.map