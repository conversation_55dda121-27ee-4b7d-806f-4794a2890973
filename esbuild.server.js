import esbuild from 'esbuild';
import path from 'path';

const build = async () => {
  try {
    await esbuild.build({
      entryPoints: ['server/index.ts'],
      bundle: true,
      platform: 'node',
      format: 'esm', // Keep ESM for top-level await support
      outdir: 'dist',
      external: [
        'express',
        'pg',
        'better-sqlite3',
        '@neondatabase/serverless',
        'openai',
        'stripe',
        'passport',
        'passport-google-oauth20',
        'multer',
        'jsdom',
        'mammoth',
        'pdf-parse',
        'puppeteer',
        'jspdf',
        'drizzle-orm',
        'drizzle-orm/sqlite-core',
        'drizzle-orm/neon-http',
        'drizzle-orm/better-sqlite3',
        // Node.js built-ins - these should be handled automatically by esbuild
        'fs',
        'path',
        'crypto',
        'url',
        'util',
        'stream',
        'buffer',
        'events',
        'http',
        'https'
      ],
      sourcemap: true,
      resolveExtensions: ['.ts', '.js', '.json'],
      alias: {
        '@shared': path.resolve(process.cwd(), 'shared'),
        '@': path.resolve(process.cwd(), 'client/src')
      },
      define: {
        'process.env.NODE_ENV': '"production"'
      },
      // Add banner to handle Node.js modules properly
      banner: {
        js: `
// Node.js compatibility for ESM modules
import { createRequire } from 'module';
const require = createRequire(import.meta.url);
`
      },
      logLevel: 'info'
    });
    console.log('Server build completed successfully');
  } catch (error) {
    console.error('Server build failed:', error);
    process.exit(1);
  }
};

build();