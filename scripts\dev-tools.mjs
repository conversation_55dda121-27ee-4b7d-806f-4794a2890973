import Database from "better-sqlite3";
import path from "node:path";
import fetch from "node-fetch";

/*
Dev tools for local testing

Usage:
  - Seed DB:   node scripts/dev-tools.mjs seed
  - Smoke-test:node scripts/dev-tools.mjs smoke

Notes:
  - Assumes the dev server is running at http://localhost:5000
  - The smoke test uses the development /api/login fallback to obtain a session cookie.
  - The seed command will insert a dev user row into data/app.db (safe for local dev).
*/

const BASE = "http://localhost:5000";

async function loginAndGetCookie() {
  // call /api/login and capture Set-Cookie without following redirect
  const res = await fetch(`${BASE}/api/login`, { method: "GET", redirect: "manual" });
  const setCookie = res.headers.get("set-cookie");
  if (!setCookie) {
    throw new Error("login did not return Set-Cookie");
  }
  // keep only cookie pair(s) before ';'
  const cookie = setCookie.split(";")[0];
  return cookie;
}

async function getUser(cookie) {
  const res = await fetch(`${BASE}/api/auth/user`, {
    headers: { Cookie: cookie },
  });
  return res.json();
}

async function createResume(cookie) {
  const payload = { title: "Dev Tools Test Resume" };
  const res = await fetch(`${BASE}/api/resumes`, {
    method: "POST",
    headers: { "Content-Type": "application/json", Cookie: cookie },
    body: JSON.stringify(payload),
  });
  const body = await res.text();
  return { status: res.status, body };
}

async function professionalSummary(cookie) {
  const payload = {
    personalInfo: { firstName: "Dev", lastName: "Tester", email: "<EMAIL>" },
    experience: [],
    skills: [],
    targetJob: "Software Engineer",
    jobKeywords: ["react", "frontend"]
  };
  const res = await fetch(`${BASE}/api/ai/professional-summary`, {
    method: "POST",
    headers: { "Content-Type": "application/json", Cookie: cookie },
    body: JSON.stringify(payload),
  });
  const json = await res.json();
  return json;
}

function seedDevUser() {
  const dbPath = path.join(process.cwd(), "data", "app.db");
  const db = new Database(dbPath);
  const id = "dev-user";
  const now = Math.floor(Date.now() / 1000);
  try {
    const stmt = db.prepare(
      `INSERT INTO users (id, email, first_name, last_name, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?)
       ON CONFLICT(id) DO UPDATE SET email=excluded.email, first_name=excluded.first_name, last_name=excluded.last_name, updated_at=excluded.updated_at;`
    );
    stmt.run(id, "<EMAIL>", "Dev", "User", now, now);
    console.log("Seeded dev user:", id);
  } catch (err) {
    console.error("Failed to seed dev user:", err && err.message ? err.message : err);
  } finally {
    db.close();
  }
}

async function runSmoke() {
  console.log("Starting smoke test against", BASE);
  try {
    const cookie = await loginAndGetCookie();
    console.log("Login cookie:", cookie);

    const user = await getUser(cookie);
    console.log("Auth user:", user);

    const cr = await createResume(cookie);
    console.log("Create resume:", cr.status, cr.body);

    const summary = await professionalSummary(cookie);
    console.log("Professional summary result:", summary);

    console.log("Smoke test completed.");
  } catch (err) {
    console.error("Smoke test failed:", err && err.message ? err.message : err);
    process.exitCode = 1;
  }
}

async function main() {
  const cmd = process.argv[2];
  if (!cmd || cmd === "help") {
    console.log("Usage: node scripts/dev-tools.mjs [seed|smoke]");
    return;
  }

  if (cmd === "seed") {
    seedDevUser();
    return;
  }

  if (cmd === "smoke") {
    await runSmoke();
    return;
  }

  console.log("Unknown command:", cmd);
  console.log("Usage: node scripts/dev-tools.mjs [seed|smoke]");
}

main();
