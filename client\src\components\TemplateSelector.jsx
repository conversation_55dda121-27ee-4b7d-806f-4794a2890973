import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Palette, Briefcase, Search, Check } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
export default function TemplateSelector({ isOpen, onClose, resumeId, currentTemplate, industry }) {
    const { toast } = useToast();
    const [selectedTemplate, setSelectedTemplate] = useState(currentTemplate || "modern");
    const [searchTerm, setSearchTerm] = useState("");
    const [selectedIndustry, setSelectedIndustry] = useState(industry || "all");
    const [templates, setTemplates] = useState({});
    // Load industry templates
    useEffect(() => {
        const loadTemplates = async () => {
            try {
                const response = await fetch("/src/templates/industry-templates.json");
                const data = await response.json();
                setTemplates(data.templates);
            }
            catch (error) {
                console.error("Error loading templates:", error);
            }
        };
        if (isOpen) {
            loadTemplates();
        }
    }, [isOpen]);
    // Update resume template mutation
    const updateTemplateMutation = useMutation({
        mutationFn: async (templateId) => {
            return await apiRequest('PUT', `/api/resumes/${resumeId}`, {
                templateId
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['/api/resumes', resumeId] });
            toast({
                title: "Template Updated",
                description: "Your resume template has been updated successfully.",
            });
            onClose();
        },
        onError: (error) => {
            toast({
                title: "Error",
                description: "Failed to update template. Please try again.",
                variant: "destructive",
            });
        },
    });
    const handleSelectTemplate = (templateId) => {
        setSelectedTemplate(templateId);
    };
    const handleApplyTemplate = () => {
        updateTemplateMutation.mutate(selectedTemplate);
    };
    // Filter templates based on search and industry
    const filteredTemplates = Object.entries(templates).filter(([id, template]) => {
        const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm.toLowerCase()));
        const matchesIndustry = selectedIndustry === "all" || id === selectedIndustry;
        return matchesSearch && matchesIndustry;
    });
    const industries = [
        { value: "all", label: "All Industries" },
        { value: "technology", label: "Technology" },
        { value: "healthcare", label: "Healthcare" },
        { value: "finance", label: "Finance" },
        { value: "marketing", label: "Marketing" },
        { value: "education", label: "Education" },
        { value: "sales", label: "Sales" },
        { value: "engineering", label: "Engineering" },
        { value: "executive", label: "Executive" },
    ];
    return (<Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5"/>
            Choose a Resume Template
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"/>
              <Input placeholder="Search templates..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10"/>
            </div>
            <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="Select industry"/>
              </SelectTrigger>
              <SelectContent>
                {industries.map((industry) => (<SelectItem key={industry.value} value={industry.value}>
                    {industry.label}
                  </SelectItem>))}
              </SelectContent>
            </Select>
          </div>

          {/* Template Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates.map(([id, template]) => (<Card key={id} className={`cursor-pointer transition-all hover:shadow-md ${selectedTemplate === id ? "ring-2 ring-primary" : ""}`} onClick={() => handleSelectTemplate(id)}>
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    {selectedTemplate === id && (<Check className="h-5 w-5 text-primary"/>)}
                  </div>
                  <p className="text-sm text-muted-foreground">{template.description}</p>
                </CardHeader>
                <CardContent className="space-y-3">
                  {/* Template Preview */}
                  <div className="h-32 bg-muted rounded-md flex items-center justify-center">
                    <div className="text-center">
                      <Briefcase className="h-8 w-8 mx-auto mb-2 text-muted-foreground"/>
                      <p className="text-xs text-muted-foreground">Template Preview</p>
                    </div>
                  </div>
                  
                  {/* Template Features */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Layout:</span>
                      <Badge variant="secondary" className="text-xs">
                        {template.layout}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Color:</span>
                      <Badge variant="secondary" className="text-xs" style={{
                backgroundColor: `var(--${template.colorScheme}-100)`,
                color: `var(--${template.colorScheme}-800)`
            }}>
                        {template.colorScheme}
                      </Badge>
                    </div>
                  </div>
                  
                  {/* Keywords */}
                  <div className="flex flex-wrap gap-1">
                    {template.keywords.slice(0, 3).map((keyword) => (<Badge key={keyword} variant="outline" className="text-xs">
                        {keyword}
                      </Badge>))}
                    {template.keywords.length > 3 && (<Badge variant="outline" className="text-xs">
                        +{template.keywords.length - 3}
                      </Badge>)}
                  </div>
                </CardContent>
              </Card>))}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button onClick={handleApplyTemplate} className="flex-1" disabled={updateTemplateMutation.isPending}>
              {updateTemplateMutation.isPending ? "Applying..." : "Apply Template"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>);
}
