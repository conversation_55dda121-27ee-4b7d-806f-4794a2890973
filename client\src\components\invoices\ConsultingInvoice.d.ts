import React from "react";
export interface ConsultingInvoiceData {
    companyName: string;
    companyLogoUrl?: string;
    clientName: string;
    clientAddress: string[];
    attention?: string;
    invoiceNumber: string;
    invoiceDate: string;
    gst: string;
    jobNo: string;
    orderNumber: string;
    totalAmount: number;
    projectReference: string;
    progressClaimNo: string;
    lineItems: Array<{
        description: string;
        feeValue: number;
        percentDone: number;
        feeToDate: number;
        prevInvoice: number;
        thisInvoice: number;
    }>;
    gstRate: number;
}
interface ConsultingInvoiceProps {
    data: ConsultingInvoiceData;
}
export default function ConsultingInvoice({ data }: ConsultingInvoiceProps): React.JSX.Element;
export {};
//# sourceMappingURL=ConsultingInvoice.d.ts.map